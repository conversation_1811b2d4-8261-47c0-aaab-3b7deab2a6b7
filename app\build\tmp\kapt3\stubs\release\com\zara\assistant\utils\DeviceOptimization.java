package com.zara.assistant.utils;

/**
 * Device Optimization Configuration
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b!\b\u0086\b\u0018\u00002\u00020\u0001BG\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\u0002\u0010\rJ\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0007H\u00c6\u0003J\t\u0010&\u001a\u00020\tH\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00c6\u0003JK\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00c6\u0001J\u0013\u0010)\u001a\u00020\u00072\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001J\t\u0010,\u001a\u00020\fH\u00d6\u0001R\u001a\u0010\u0005\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u000f\"\u0004\b\u0013\u0010\u0011R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R \u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u0019\"\u0004\b\u001a\u0010\u001bR\u001a\u0010\u0004\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u000f\"\u0004\b\u001d\u0010\u0011R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!\u00a8\u0006-"}, d2 = {"Lcom/zara/assistant/utils/DeviceOptimization;", "", "cpuCores", "", "recommendedThreads", "availableMemoryMB", "shouldUseMemoryCache", "", "deviceTier", "Lcom/zara/assistant/utils/DeviceTier;", "recommendations", "", "", "(IIIZLcom/zara/assistant/utils/DeviceTier;Ljava/util/List;)V", "getAvailableMemoryMB", "()I", "setAvailableMemoryMB", "(I)V", "getCpuCores", "setCpuCores", "getDeviceTier", "()Lcom/zara/assistant/utils/DeviceTier;", "setDeviceTier", "(Lcom/zara/assistant/utils/DeviceTier;)V", "getRecommendations", "()Ljava/util/List;", "setRecommendations", "(Ljava/util/List;)V", "getRecommendedThreads", "setRecommendedThreads", "getShouldUseMemoryCache", "()Z", "setShouldUseMemoryCache", "(Z)V", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "app_release"})
public final class DeviceOptimization {
    private int cpuCores;
    private int recommendedThreads;
    private int availableMemoryMB;
    private boolean shouldUseMemoryCache;
    @org.jetbrains.annotations.NotNull()
    private com.zara.assistant.utils.DeviceTier deviceTier;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> recommendations;
    
    public DeviceOptimization(int cpuCores, int recommendedThreads, int availableMemoryMB, boolean shouldUseMemoryCache, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DeviceTier deviceTier, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> recommendations) {
        super();
    }
    
    public final int getCpuCores() {
        return 0;
    }
    
    public final void setCpuCores(int p0) {
    }
    
    public final int getRecommendedThreads() {
        return 0;
    }
    
    public final void setRecommendedThreads(int p0) {
    }
    
    public final int getAvailableMemoryMB() {
        return 0;
    }
    
    public final void setAvailableMemoryMB(int p0) {
    }
    
    public final boolean getShouldUseMemoryCache() {
        return false;
    }
    
    public final void setShouldUseMemoryCache(boolean p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceTier getDeviceTier() {
        return null;
    }
    
    public final void setDeviceTier(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DeviceTier p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRecommendations() {
        return null;
    }
    
    public final void setRecommendations(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    public DeviceOptimization() {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceTier component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceOptimization copy(int cpuCores, int recommendedThreads, int availableMemoryMB, boolean shouldUseMemoryCache, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DeviceTier deviceTier, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> recommendations) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}