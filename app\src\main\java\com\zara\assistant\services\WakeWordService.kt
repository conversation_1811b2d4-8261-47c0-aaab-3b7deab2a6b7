package com.zara.assistant.services

import ai.picovoice.porcupine.*
import android.app.*
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.zara.assistant.R
import com.zara.assistant.ZaraApplication
import com.zara.assistant.core.Constants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.File
import javax.inject.Inject

/**
 * Wake Word Service - "Hey Zara" detection using Picovoice Porcupine
 * Features:
 * - Ultra-fast wake word detection
 * - Low power consumption
 * - Adjustable sensitivity
 * - Foreground service for reliability
 */
@AndroidEntryPoint
class WakeWordService : Service() {

    companion object {
        private const val TAG = "WakeWordService"
        private const val NOTIFICATION_ID = ZaraApplication.WAKE_WORD_SERVICE_ID

        const val ACTION_START_WAKE_WORD = "START_WAKE_WORD"
        const val ACTION_STOP_WAKE_WORD = "STOP_WAKE_WORD"
        const val ACTION_SET_SENSITIVITY = "SET_SENSITIVITY"

        /**
         * Start wake word detection service
         */
        fun startWakeWordDetection(context: Context, sensitivity: Float = Constants.WakeWord.DEFAULT_SENSITIVITY) {
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = ACTION_START_WAKE_WORD
                putExtra("sensitivity", sensitivity)
            }
            context.startForegroundService(intent)
        }

        /**
         * Stop wake word detection service
         */
        fun stopWakeWordDetection(context: Context) {
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = ACTION_STOP_WAKE_WORD
            }
            context.startService(intent)
        }
    }

    @Inject
    lateinit var zaraVoiceService: ZaraVoiceService

    private var porcupine: Porcupine? = null
    private var isListening = false
    private var serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // State flows
    private val _isWakeWordActive = MutableStateFlow(false)
    val isWakeWordActive: StateFlow<Boolean> = _isWakeWordActive
    
    private val _sensitivity = MutableStateFlow(Constants.WakeWord.DEFAULT_SENSITIVITY)
    val sensitivity: StateFlow<Float> = _sensitivity

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 Wake Word Service created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_WAKE_WORD -> {
                val sensitivity = intent.getFloatExtra("sensitivity", Constants.WakeWord.DEFAULT_SENSITIVITY)
                startWakeWordDetection(sensitivity)
            }
            ACTION_STOP_WAKE_WORD -> {
                stopWakeWordDetection()
            }
            ACTION_SET_SENSITIVITY -> {
                val sensitivity = intent.getFloatExtra("sensitivity", Constants.WakeWord.DEFAULT_SENSITIVITY)
                updateSensitivity(sensitivity)
            }
        }
        
        return START_STICKY // Restart if killed
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * Start wake word detection
     */
    private fun startWakeWordDetection(sensitivity: Float) {
        if (isListening) {
            Log.d(TAG, "⚠️ Wake word detection already active")
            return
        }

        serviceScope.launch {
            try {
                Log.d(TAG, "🎤 Starting wake word detection with sensitivity: $sensitivity")
                
                if (initializePorcupine(sensitivity)) {
                    isListening = true
                    _isWakeWordActive.value = true
                    _sensitivity.value = sensitivity
                    
                    startForeground(NOTIFICATION_ID, createNotification())
                    startListeningLoop()
                    
                    Log.d(TAG, "✅ Wake word detection started successfully")
                } else {
                    Log.e(TAG, "❌ Failed to initialize Porcupine")
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error starting wake word detection: ${e.message}", e)
            }
        }
    }

    /**
     * Stop wake word detection
     */
    private fun stopWakeWordDetection() {
        Log.d(TAG, "🛑 Stopping wake word detection")
        
        isListening = false
        _isWakeWordActive.value = false
        
        porcupine?.delete()
        porcupine = null
        
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
        
        Log.d(TAG, "✅ Wake word detection stopped")
    }

    /**
     * Update sensitivity
     */
    private fun updateSensitivity(newSensitivity: Float) {
        Log.d(TAG, "🔧 Updating sensitivity to: $newSensitivity")
        
        if (isListening) {
            // Restart with new sensitivity
            stopWakeWordDetection()
            startWakeWordDetection(newSensitivity)
        } else {
            _sensitivity.value = newSensitivity
        }
    }

    /**
     * Initialize Porcupine wake word engine
     */
    private suspend fun initializePorcupine(sensitivity: Float): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val accessKey = Constants.ApiKeys.PICOVOICE_ACCESS_KEY
            val keywordPath = copyPPNFileFromAssets()
            
            if (keywordPath != null) {
                porcupine = Porcupine.Builder()
                    .setAccessKey(accessKey)
                    .setKeywordPath(keywordPath)
                    .setSensitivity(sensitivity)
                    .build(applicationContext)
                
                Log.d(TAG, "✅ Porcupine initialized successfully")
                true
            } else {
                Log.e(TAG, "❌ Failed to copy PPN file")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error initializing Porcupine: ${e.message}", e)
            false
        }
    }

    /**
     * Copy PPN file from assets to internal storage
     */
    private suspend fun copyPPNFileFromAssets(): String? = withContext(Dispatchers.IO) {
        return@withContext try {
            val inputStream = assets.open(Constants.WakeWord.WAKE_WORD_FILE)
            val outputFile = File(filesDir, Constants.WakeWord.WAKE_WORD_FILE)
            
            if (!outputFile.exists()) {
                outputFile.outputStream().use { output ->
                    inputStream.copyTo(output)
                }
            }
            
            outputFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error copying PPN file: ${e.message}", e)
            null
        }
    }

    /**
     * Main listening loop
     */
    private fun startListeningLoop() {
        serviceScope.launch(Dispatchers.IO) {
            val porcupineInstance = porcupine ?: return@launch
            
            try {
                while (isListening && serviceScope.isActive) {
                    // In a real implementation, you would read audio data here
                    // For now, we'll simulate the detection process
                    delay(100) // Simulate audio processing
                    
                    // Simulate wake word detection (replace with actual audio processing)
                    // val keywordIndex = porcupineInstance.process(audioFrame)
                    // if (keywordIndex >= 0) {
                    //     onWakeWordDetected()
                    // }
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error in listening loop: ${e.message}", e)
            }
        }
    }

    /**
     * Handle wake word detection
     */
    private fun onWakeWordDetected() {
        Log.d(TAG, "🎯 Wake word detected!")
        
        serviceScope.launch(Dispatchers.Main) {
            try {
                // Trigger voice service to start listening
                ZaraVoiceService.startListening(applicationContext)
                
                // Optional: Provide haptic feedback
                // vibrator?.vibrate(VibrationEffect.createOneShot(100, VibrationEffect.DEFAULT_AMPLITUDE))
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error handling wake word detection: ${e.message}", e)
            }
        }
    }

    /**
     * Create notification for foreground service
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, ZaraApplication.WAKE_WORD_CHANNEL)
            .setContentTitle("Zara Wake Word")
            .setContentText("Listening for \"Hey Zara\"")
            .setSmallIcon(R.drawable.ic_mic)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setShowWhen(false)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🔥 Wake Word Service destroyed")
        
        isListening = false
        porcupine?.delete()
        serviceScope.cancel()
    }
}
