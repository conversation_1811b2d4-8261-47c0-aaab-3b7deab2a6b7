// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.utils.ApiKeyManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ZaraSTTService_Factory implements Factory<ZaraSTTService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public ZaraSTTService_Factory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public ZaraSTTService get() {
    return newInstance(contextProvider.get(), apiKeyManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static ZaraSTTService_Factory create(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new ZaraSTTService_Factory(contextProvider, apiKeyManagerProvider, performanceUtilsProvider);
  }

  public static ZaraSTTService newInstance(Context context, ApiKeyManager apiKeyManager,
      PerformanceUtils performanceUtils) {
    return new ZaraSTTService(context, apiKeyManager, performanceUtils);
  }
}
