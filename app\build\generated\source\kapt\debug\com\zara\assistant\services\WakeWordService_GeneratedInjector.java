package com.zara.assistant.services;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = WakeWordService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface WakeWordService_GeneratedInjector {
  void injectWakeWordService(WakeWordService wakeWordService);
}
