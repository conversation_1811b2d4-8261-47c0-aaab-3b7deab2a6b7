package com.zara.assistant.domain.model

import java.util.*

/**
 * AI Response Model - Represents responses from AI services
 */
data class AIResponse(
    val id: String,
    val text: String,
    val source: AISource,
    val confidence: Float,
    val processingTimeMs: Long,
    val timestamp: Date,
    val metadata: Map<String, String> = emptyMap()
)

/**
 * AI Source enum
 */
enum class AISource {
    COHERE,
    PERPLEXITY,
    LOCAL
}

/**
 * AI Health Status
 */
data class AIHealthStatus(
    val cohereAvailable: Boolean,
    val perplexityAvailable: Boolean,
    val localProcessingAvailable: Boolean,
    val averageResponseTime: Long,
    val errorRate: Float
)

/**
 * Command Classification
 */
data class CommandClassification(
    val category: CommandCategory,
    val intent: CommandIntent,
    val confidence: Float
)

/**
 * Conversation Model
 */
data class Conversation(
    val id: String,
    val userMessage: String,
    val aiResponse: String,
    val language: String,
    val timestamp: Date,
    val source: AISource,
    val processingTimeMs: Long
)
