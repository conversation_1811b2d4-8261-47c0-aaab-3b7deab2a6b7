package com.zara.assistant.domain.repository;

/**
 * Settings Repository Interface
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0005\u001a\u0002H\u0006\"\u0004\b\u0000\u0010\u00062\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u0002H\u0006H\u00a6@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH&J$\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0010\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\u0012J\u000e\u0010\u0013\u001a\u00020\u000fH\u00a6@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0014\u001a\u00020\u000f\"\u0004\b\u0000\u0010\u00062\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u0002H\u0006H\u00a6@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\fH\u00a6@\u00a2\u0006\u0002\u0010\u0018\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0019"}, d2 = {"Lcom/zara/assistant/domain/repository/SettingsRepository;", "", "exportPreferences", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPreference", "T", "key", "defaultValue", "(Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserPreferences", "Lkotlinx/coroutines/flow/Flow;", "Lcom/zara/assistant/domain/model/UserPreferences;", "importPreferences", "Lkotlin/Result;", "", "data", "importPreferences-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetToDefaults", "setPreference", "value", "updateUserPreferences", "preferences", "(Lcom/zara/assistant/domain/model/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface SettingsRepository {
    
    /**
     * Get user preferences
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.UserPreferences> getUserPreferences();
    
    /**
     * Update user preferences
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserPreferences(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserPreferences preferences, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Get specific preference
     */
    @org.jetbrains.annotations.Nullable()
    public abstract <T extends java.lang.Object>java.lang.Object getPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String key, T defaultValue, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super T> $completion);
    
    /**
     * Set specific preference
     */
    @org.jetbrains.annotations.Nullable()
    public abstract <T extends java.lang.Object>java.lang.Object setPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String key, T value, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Reset to default preferences
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object resetToDefaults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Export preferences
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object exportPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion);
}