R_DEF: Internal format may change without notice
local
color accent_error
color accent_info
color accent_success
color accent_warning
color black
color deep_teal
color gradient_end
color gradient_middle
color gradient_start
color gray_100
color gray_200
color gray_300
color gray_400
color gray_50
color gray_500
color gray_600
color gray_700
color gray_800
color gray_900
color luminous_blue
color md_theme_dark_background
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_inverseOnSurface
color md_theme_dark_inversePrimary
color md_theme_dark_inverseSurface
color md_theme_dark_onBackground
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_outlineVariant
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_scrim
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_shadow
color md_theme_dark_surface
color md_theme_dark_surfaceTint
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color neuro_background
color neuro_background_dark
color neuro_dark_highlight
color neuro_dark_shadow
color neuro_light_highlight
color neuro_light_shadow
color neuro_surface_dark
color neuro_surface_elevated_dark
color neuro_surface_elevated_light
color neuro_surface_light
color outline_variant
color outline_variant_dark
color primary
color soft_coral
color surface
color surface_dark
color text_primary_dark
color text_primary_light
color text_secondary_dark
color text_secondary_light
color text_tertiary_dark
color text_tertiary_light
color transparent
color voice_idle
color voice_listening
color voice_processing
color voice_speaking
color white
dimen accessibility_focus_border
dimen animation_offset
dimen app_bar_elevation
dimen app_bar_height
dimen bottom_nav_elevation
dimen bottom_nav_height
dimen bottom_sheet_corner_radius
dimen bottom_sheet_peek_height
dimen button_corner_radius
dimen button_height
dimen button_padding_horizontal
dimen button_padding_vertical
dimen card_corner_radius
dimen card_elevation
dimen card_margin
dimen card_padding
dimen corner_radius_lg
dimen corner_radius_md
dimen corner_radius_round
dimen corner_radius_sm
dimen corner_radius_xl
dimen corner_radius_xs
dimen corner_radius_xxl
dimen dialog_corner_radius
dimen dialog_margin
dimen dialog_padding
dimen fab_margin
dimen fab_size
dimen icon_size_lg
dimen icon_size_md
dimen icon_size_sm
dimen icon_size_xl
dimen icon_size_xs
dimen icon_size_xxl
dimen landscape_margin
dimen landscape_padding
dimen margin_card
dimen margin_horizontal
dimen margin_item
dimen margin_vertical
dimen min_touch_target
dimen navigation_bar_height
dimen neuro_blur_radius
dimen neuro_elevation
dimen neuro_inset_offset
dimen neuro_offset_x
dimen neuro_offset_y
dimen padding_lg
dimen padding_md
dimen padding_sm
dimen padding_xl
dimen padding_xs
dimen ripple_radius
dimen settings_icon_size
dimen settings_item_height
dimen settings_item_padding
dimen spacing_lg
dimen spacing_md
dimen spacing_sm
dimen spacing_xl
dimen spacing_xs
dimen spacing_xxl
dimen status_bar_height
dimen text_size_headline
dimen text_size_lg
dimen text_size_md
dimen text_size_sm
dimen text_size_title
dimen text_size_xl
dimen text_size_xs
dimen text_size_xxl
dimen voice_button_icon_size
dimen voice_button_size
dimen voice_ripple_size
dimen voice_wave_container_height
dimen voice_wave_height
dimen voice_wave_spacing
drawable bottom_sheet_background
drawable bottom_sheet_background_dark
drawable dialog_background
drawable dialog_background_dark
drawable fab_background
drawable fab_background_dark
drawable ic_close
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mic
drawable ic_microphone
drawable ic_settings
drawable ic_voice_assistant
drawable ic_zara_logo
drawable neuro_button_background
drawable neuro_button_background_dark
drawable neuro_card_background
drawable neuro_card_background_dark
drawable neuro_settings_item_background
drawable neuro_settings_item_background_dark
drawable slider_background
drawable slider_background_dark
drawable switch_thumb
drawable switch_thumb_dark
drawable switch_track
drawable switch_track_dark
drawable voice_button_background
drawable voice_button_background_dark
mipmap ic_launcher
mipmap ic_launcher_round
string about
string accessibility_service_description
string accessibility_service_summary
string accessibility_settings
string ai_personality
string ai_personality_casual
string ai_personality_friendly
string ai_personality_professional
string ai_response_brief
string ai_response_conversational
string ai_response_detailed
string ai_response_style
string ai_settings
string analytics_enabled
string app_description
string app_name
string auto_listen_after_response
string build_date
string cancel
string clear_data
string close
string cmd_call_contact
string cmd_close_app
string cmd_enable_airplane_mode
string cmd_increase_volume
string cmd_news
string cmd_next_song
string cmd_open_app
string cmd_pause_music
string cmd_play_music
string cmd_read_notifications
string cmd_search
string cmd_send_message
string cmd_set_brightness
string cmd_switch_app
string cmd_time
string cmd_turn_off_bluetooth
string cmd_turn_on_wifi
string cmd_weather
string commands_apps
string commands_communication
string commands_device_control
string commands_entertainment
string commands_help_title
string commands_information
string continue_text
string conversation_history
string copy
string data_collection
string delete
string deny
string developer
string disable
string done
string enable
string error_accessibility_service
string error_ai_service_unavailable
string error_microphone_permission
string error_network_connection
string error_notification_access
string error_speech_recognition
string error_text_to_speech
string export_data
string grant
string listening
string next
string ok
string open_source_licenses
string permission_accessibility_description
string permission_accessibility_title
string permission_location_description
string permission_location_title
string permission_microphone_description
string permission_microphone_title
string permission_notification_description
string permission_notification_title
string permission_overlay_description
string permission_overlay_title
string permission_phone_description
string permission_phone_title
string previous
string privacy_policy
string privacy_settings
string processing
string reset
string response_app_opened
string response_bluetooth_disabled
string response_calling_contact
string response_no_notifications
string response_volume_changed
string response_wifi_enabled
string retry
string save
string settings
string share
string skip
string speaking
string speech_pitch
string speech_rate
string stop
string tap_to_speak
string terms_of_service
string version
string voice_data_storage
string voice_error
string voice_idle
string voice_language
string voice_listening_manual
string voice_listening_wake
string voice_processing_ai
string voice_processing_notification
string voice_settings
string voice_speaking_response
string wake_word_active
string wake_word_disabled
string wake_word_enabled
string wake_word_inactive
string wake_word_sensitivity
string wake_word_sensitivity_high
string wake_word_sensitivity_low
string wake_word_sensitivity_medium
string wake_word_service_description
string wake_word_service_title
string wake_word_settings
string welcome_message
style NeumorphismBottomSheet
style NeumorphismButton
style NeumorphismCard
style NeumorphismDialog
style NeumorphismFAB
style NeumorphismSlider
style NeumorphismSwitch
style SettingsItem
style TextAppearance
style TextAppearance.Zara
style TextAppearance.Zara.Body1
style TextAppearance.Zara.Body2
style TextAppearance.Zara.Caption
style TextAppearance.Zara.Headline1
style TextAppearance.Zara.Headline2
style Theme.Zara
style Theme.Zara.NoActionBar
style VoiceButton
style VoiceStateText
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml device_admin
xml file_paths
