<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="NeumorphismBottomSheet">
        <item name="android:background">@drawable/bottom_sheet_background_dark</item>
    </style>
    <style name="NeumorphismButton">
        <item name="android:background">@drawable/neuro_button_background_dark</item>
        <item name="android:textColor">@color/text_primary_dark</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingHorizontal">24dp</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="NeumorphismCard">
        <item name="android:background">@drawable/neuro_card_background_dark</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:padding">16dp</item>
    </style>
    <style name="NeumorphismDialog">
        <item name="android:background">@drawable/dialog_background_dark</item>
        <item name="android:windowBackground">@color/transparent</item>
    </style>
    <style name="NeumorphismFAB">
        <item name="android:background">@drawable/fab_background_dark</item>
        <item name="android:elevation">0dp</item>
    </style>
    <style name="NeumorphismSlider">
        <item name="android:background">@drawable/slider_background_dark</item>
    </style>
    <style name="NeumorphismSwitch">
        <item name="android:thumb">@drawable/switch_thumb_dark</item>
        <item name="android:track">@drawable/switch_track_dark</item>
    </style>
    <style name="SettingsItem">
        <item name="android:background">@drawable/neuro_settings_item_background_dark</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">4dp</item>
    </style>
    <style name="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@android:color/white</item>
    </style>
    <style name="TextAppearance.Zara" parent="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/text_primary_dark</item>
    </style>
    <style name="TextAppearance.Zara.Body1" parent="TextAppearance.Zara">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.Zara.Body2" parent="TextAppearance.Zara">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_dark</item>
    </style>
    <style name="TextAppearance.Zara.Caption" parent="TextAppearance.Zara">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/text_tertiary_dark</item>
    </style>
    <style name="TextAppearance.Zara.Headline1" parent="TextAppearance.Zara">
        <item name="android:textSize">32sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.Zara.Headline2" parent="TextAppearance.Zara">
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Theme.Zara" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/luminous_blue</item>
        <item name="colorPrimaryDark">@color/deep_teal</item>
        <item name="colorAccent">@color/soft_coral</item>

        
        <item name="android:colorBackground">@color/neuro_background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary_dark</item>
        <item name="android:textColorSecondary">@color/text_secondary_dark</item>
        
        
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
    <style name="VoiceButton" parent="NeumorphismButton">
        <item name="android:background">@drawable/voice_button_background_dark</item>
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:textSize">0sp</item>
    </style>
    <style name="VoiceStateText" parent="TextAppearance.Zara.Body1">
        <item name="android:textAlignment">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/text_secondary_dark</item>
    </style>
</resources>