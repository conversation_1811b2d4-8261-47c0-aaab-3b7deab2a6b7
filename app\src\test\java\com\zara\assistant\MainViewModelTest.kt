package com.zara.assistant

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.domain.repository.SettingsRepository
import com.zara.assistant.domain.repository.VoiceRepository
import com.zara.assistant.ui.viewmodel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever

/**
 * Unit tests for MainViewModel
 */
@ExperimentalCoroutinesApi
class MainViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    @Mock
    private lateinit var voiceRepository: VoiceRepository

    @Mock
    private lateinit var settingsRepository: SettingsRepository

    private lateinit var viewModel: MainViewModel

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        Dispatchers.setMain(testDispatcher)

        // Setup default mock responses
        whenever(voiceRepository.getVoiceState()).thenReturn(
            flowOf(
                VoiceState(
                    currentState = VoiceState.State.IDLE,
                    isWakeWordActive = false,
                    isListening = false,
                    isProcessing = false,
                    isSpeaking = false
                )
            )
        )

        whenever(voiceRepository.isWakeWordDetectionActive()).thenReturn(flowOf(false))

        whenever(settingsRepository.observeVoiceSettings()).thenReturn(
            flowOf(
                VoiceSettings(
                    isWakeWordEnabled = true,
                    wakeWordSensitivity = 0.5f,
                    speechRate = 1.0f,
                    speechPitch = 1.0f,
                    language = "en-US",
                    autoListenAfterResponse = false
                )
            )
        )

        viewModel = MainViewModel(voiceRepository, settingsRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state is correct`() = runTest {
        // Given - ViewModel is initialized

        // When
        val uiState = viewModel.uiState.value

        // Then
        assertEquals(VoiceState.State.IDLE, uiState.voiceState.currentState)
        assertFalse(uiState.isWakeWordActive)
        assertTrue(uiState.isWakeWordEnabled)
        assertEquals(0.5f, uiState.wakeWordSensitivity)
        assertFalse(uiState.permissionsGranted)
        assertFalse(uiState.showPermissionDialog)
    }

    @Test
    fun `onPermissionsGranted updates state correctly`() = runTest {
        // When
        viewModel.onPermissionsGranted()

        // Then
        val uiState = viewModel.uiState.value
        assertTrue(uiState.permissionsGranted)
        assertTrue(uiState.shouldStartWakeWordService)
    }

    @Test
    fun `onPermissionsDenied shows permission dialog`() = runTest {
        // When
        viewModel.onPermissionsDenied()

        // Then
        val uiState = viewModel.uiState.value
        assertFalse(uiState.permissionsGranted)
        assertTrue(uiState.showPermissionDialog)
    }

    @Test
    fun `setMissingPermissions updates state with missing permissions`() = runTest {
        // Given
        val missingPermissions = listOf(
            "android.permission.RECORD_AUDIO",
            "android.permission.POST_NOTIFICATIONS"
        )

        // When
        viewModel.setMissingPermissions(missingPermissions)

        // Then
        val uiState = viewModel.uiState.value
        assertEquals(missingPermissions, uiState.missingPermissions)
        assertTrue(uiState.showPermissionDialog)
    }

    @Test
    fun `onWakeWordServiceStarted updates state correctly`() = runTest {
        // When
        viewModel.onWakeWordServiceStarted()

        // Then
        val uiState = viewModel.uiState.value
        assertFalse(uiState.shouldStartWakeWordService)
        assertTrue(uiState.isWakeWordServiceRunning)
    }

    @Test
    fun `onWakeWordDetected updates timestamp`() = runTest {
        // Given
        val beforeTime = System.currentTimeMillis()

        // When
        viewModel.onWakeWordDetected()

        // Then
        val uiState = viewModel.uiState.value
        assertNotNull(uiState.lastWakeWordDetection)
        assertTrue(uiState.lastWakeWordDetection!! >= beforeTime)
    }

    @Test
    fun `dismissError clears error message`() = runTest {
        // Given - Set an error first
        // This would require exposing a way to set error in the ViewModel
        // For now, we'll test the dismiss functionality

        // When
        viewModel.dismissError()

        // Then
        val uiState = viewModel.uiState.value
        assertNull(uiState.errorMessage)
    }

    @Test
    fun `dismissPermissionDialog hides dialog`() = runTest {
        // Given - Show permission dialog first
        viewModel.onPermissionsDenied()

        // When
        viewModel.dismissPermissionDialog()

        // Then
        val uiState = viewModel.uiState.value
        assertFalse(uiState.showPermissionDialog)
    }

    @Test
    fun `voice state text is correct for different states`() = runTest {
        // Test different voice states and their corresponding text
        val testCases = mapOf(
            VoiceState.State.IDLE to "Ready to listen",
            VoiceState.State.LISTENING_WAKE_WORD to "Say \"Hey Zara\" to start",
            VoiceState.State.LISTENING_COMMAND to "Listening for your command...",
            VoiceState.State.PROCESSING_COMMAND to "Processing your request...",
            VoiceState.State.GENERATING_RESPONSE to "Thinking about your request...",
            VoiceState.State.SPEAKING_RESPONSE to "Zara is responding...",
            VoiceState.State.EXECUTING_ACTION to "Executing your command...",
            VoiceState.State.DISABLED to "Voice assistant is disabled"
        )

        testCases.forEach { (state, expectedText) ->
            // Given
            val voiceState = VoiceState(
                currentState = state,
                isWakeWordActive = false,
                isListening = false,
                isProcessing = false,
                isSpeaking = false
            )

            whenever(voiceRepository.getVoiceState()).thenReturn(flowOf(voiceState))

            // Create new ViewModel instance to trigger state update
            val testViewModel = MainViewModel(voiceRepository, settingsRepository)
            testDispatcher.scheduler.advanceUntilIdle()

            // Then
            assertEquals(expectedText, testViewModel.uiState.value.voiceStateText)
        }
    }
}
