package com.zara.assistant.utils;

/**
 * Direct Permission Manager - Perfect permission handling for Zara
 * Features:
 * - Real-time permission state tracking
 * - Direct permission granting (where possible)
 * - Intelligent permission flow
 * - Live permission updates
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0012\b\u0007\u0018\u0000 +2\u00020\u0001:\u0001+B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0018\u001a\u00020\u0007H\u0002J\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010\u001b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010\u001d\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\u0011J\b\u0010\u001f\u001a\u00020\u0007H\u0002J\b\u0010 \u001a\u00020\u0007H\u0002J\u0010\u0010!\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\u000bH\u0002J\u000e\u0010#\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010$\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010%\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010&\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010\'\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010(\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u001c\u0010)\u001a\u00020\u001a2\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00070\nH\u0002R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00070\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u001c\u0010\u0013\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u0015\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R#\u0010\u0016\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00070\n0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000f\u00a8\u0006,"}, d2 = {"Lcom/zara/assistant/utils/DirectPermissionManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_allPermissionsGranted", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isGrantingPermissions", "_permissionStates", "", "", "allPermissionsGranted", "Lkotlinx/coroutines/flow/StateFlow;", "getAllPermissionsGranted", "()Lkotlinx/coroutines/flow/StateFlow;", "currentActivity", "Landroidx/fragment/app/FragmentActivity;", "isGrantingPermissions", "permissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "permissionStates", "getPermissionStates", "canDrawOverlays", "checkAllPermissions", "", "grantAllPermissionsDirectly", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initialize", "activity", "isAccessibilityServiceEnabled", "isNotificationListenerEnabled", "isPermissionGranted", "permission", "requestAccessibilityPermission", "requestAllPermissions", "requestNotificationListenerPermission", "requestOverlayPermission", "requestSpecialPermissions", "requestStandardPermissions", "updatePermissionStates", "permissions", "Companion", "app_debug"})
public final class DirectPermissionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DirectPermissionManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] REQUIRED_PERMISSIONS = {"android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET", "android.permission.VIBRATE", "android.permission.WAKE_LOCK", "android.permission.FOREGROUND_SERVICE"};
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] SPECIAL_PERMISSIONS = {"SYSTEM_ALERT_WINDOW", "NOTIFICATION_LISTENER", "ACCESSIBILITY_SERVICE"};
    @org.jetbrains.annotations.Nullable()
    private androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher;
    @org.jetbrains.annotations.Nullable()
    private androidx.fragment.app.FragmentActivity currentActivity;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> _permissionStates = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> permissionStates = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _allPermissionsGranted = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> allPermissionsGranted = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isGrantingPermissions = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isGrantingPermissions = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.utils.DirectPermissionManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public DirectPermissionManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> getPermissionStates() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getAllPermissionsGranted() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isGrantingPermissions() {
        return null;
    }
    
    /**
     * Initialize permission manager with activity
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity) {
    }
    
    /**
     * Check all required permissions
     */
    public final void checkAllPermissions() {
    }
    
    /**
     * Request all missing permissions
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object requestAllPermissions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Grant all permissions directly (simulated for demo)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object grantAllPermissionsDirectly(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Request standard Android permissions
     */
    private final java.lang.Object requestStandardPermissions(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Request special permissions
     */
    private final java.lang.Object requestSpecialPermissions(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Request Accessibility Service permission
     */
    private final java.lang.Object requestAccessibilityPermission(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Request Notification Listener permission
     */
    private final java.lang.Object requestNotificationListenerPermission(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Request System Alert Window permission
     */
    private final java.lang.Object requestOverlayPermission(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Check if standard permission is granted
     */
    private final boolean isPermissionGranted(java.lang.String permission) {
        return false;
    }
    
    /**
     * Check if overlay permission is granted
     */
    private final boolean canDrawOverlays() {
        return false;
    }
    
    /**
     * Check if notification listener is enabled
     */
    private final boolean isNotificationListenerEnabled() {
        return false;
    }
    
    /**
     * Check if accessibility service is enabled
     */
    private final boolean isAccessibilityServiceEnabled() {
        return false;
    }
    
    /**
     * Update permission states from result
     */
    private final void updatePermissionStates(java.util.Map<java.lang.String, java.lang.Boolean> permissions) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0019\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\n\n\u0002\u0010\b\u001a\u0004\b\u0006\u0010\u0007R\u0019\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\n\n\u0002\u0010\b\u001a\u0004\b\n\u0010\u0007R\u000e\u0010\u000b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/utils/DirectPermissionManager$Companion;", "", "()V", "REQUIRED_PERMISSIONS", "", "", "getREQUIRED_PERMISSIONS", "()[Ljava/lang/String;", "[Ljava/lang/String;", "SPECIAL_PERMISSIONS", "getSPECIAL_PERMISSIONS", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getREQUIRED_PERMISSIONS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getSPECIAL_PERMISSIONS() {
            return null;
        }
    }
}