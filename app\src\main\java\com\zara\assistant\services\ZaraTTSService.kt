package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.zara.assistant.core.Constants
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Zara Azure Text-to-Speech Service
 * Ultra-fast TTS with JennyNeural/GuyNeural voices and streaming audio
 */
@Singleton
class ZaraTTSService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: ApiKeyManager,
    private val performanceUtils: PerformanceUtils
) {
    companion object {
        private const val TAG = "ZaraTTS"
    }

    // Azure Speech components
    private var speechConfig: SpeechConfig? = null
    private var audioConfig: AudioConfig? = null
    private var speechSynthesizer: SpeechSynthesizer? = null
    
    // Service state
    private var isInitialized = false
    private var isSpeaking = false
    private var currentVoice = Constants.Voice.VOICE_JENNY_NEURAL
    private var speechRate = Constants.Voice.DEFAULT_SPEECH_RATE
    private var speechPitch = Constants.Voice.DEFAULT_SPEECH_PITCH
    private var currentListener: TTSListener? = null
    
    // State flows
    private val _isSpeaking = MutableStateFlow(false)
    val isSpeakingFlow: StateFlow<Boolean> = _isSpeaking.asStateFlow()
    
    private val _currentVoice = MutableStateFlow(Constants.Voice.VOICE_JENNY_NEURAL)
    val currentVoiceFlow: StateFlow<String> = _currentVoice.asStateFlow()
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * TTS Result Listener Interface
     */
    interface TTSListener {
        fun onSpeechStarted()
        fun onSpeechProgress(text: String, progress: Float)
        fun onSpeechCompleted()
        fun onSpeechCanceled()
        fun onError(error: String)
    }

    /**
     * Initialize Azure Speech TTS Service
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("TTS_Initialize")
            
            Log.d(TAG, "🚀 Initializing Azure TTS Service...")
            
            val speechKey = apiKeyManager.getAzureSpeechKey()
            val speechRegion = apiKeyManager.getAzureSpeechRegion()
            
            if (speechKey == null || speechRegion == null) {
                Log.e(TAG, "❌ Azure Speech credentials not available")
                return@withContext false
            }
            
            // Create speech configuration
            speechConfig = SpeechConfig.fromSubscription(speechKey, speechRegion).apply {
                // Set default voice
                speechSynthesisVoiceName = currentVoice
                
                // Optimize for streaming and low latency
                setProperty(PropertyId.SpeechServiceConnection_SynthEnableCompressedAudioTransmission, "true")
                setProperty(PropertyId.SpeechServiceConnection_SynthOutputFormat, "audio-16khz-32kbitrate-mono-mp3")
                
                // Enable SSML for advanced voice control
                setProperty(PropertyId.SpeechServiceConnection_SynthLanguage, "en-US")
                
                // Optimize for real-time streaming
                setProperty(PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "1000")
            }
            
            // Create audio configuration for speaker output
            audioConfig = AudioConfig.fromDefaultSpeakerOutput()
            
            isInitialized = true
            performanceUtils.endMeasurement(measurementId)
            
            Log.d(TAG, "✅ Azure TTS Service initialized successfully")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize Azure TTS: ${e.message}", e)
            false
        }
    }

    /**
     * Speak text with specified parameters
     */
    suspend fun speak(
        text: String,
        voice: String? = null,
        language: String? = null,
        rate: Float? = null,
        pitch: Float? = null,
        listener: TTSListener? = null
    ): Boolean = withContext(Dispatchers.Main) {
        try {
            if (!isInitialized) {
                Log.w(TAG, "⚠️ TTS not initialized")
                return@withContext false
            }
            
            if (isSpeaking) {
                Log.w(TAG, "⚠️ Already speaking, stopping current speech")
                stopSpeaking()
            }
            
            val measurementId = performanceUtils.startMeasurement("TTS_Speak")
            
            currentListener = listener
            
            // Update voice settings
            val selectedVoice = voice ?: selectVoiceForLanguage(language)
            val selectedRate = rate ?: speechRate
            val selectedPitch = pitch ?: speechPitch
            
            // Create SSML for advanced voice control
            val ssmlText = createSSML(text, selectedVoice, selectedRate, selectedPitch)
            
            // Create speech synthesizer
            speechSynthesizer = SpeechSynthesizer(speechConfig, audioConfig)
            
            setupSynthesisCallbacks()
            
            // Start synthesis
            speechSynthesizer?.SpeakSsmlAsync(ssmlText)
            
            isSpeaking = true
            _isSpeaking.value = true
            _currentVoice.value = selectedVoice
            
            performanceUtils.endMeasurement(measurementId)
            
            listener?.onSpeechStarted()
            Log.d(TAG, "🗣️ Started speaking: \"${text.take(50)}...\" (voice: $selectedVoice)")
            
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to speak: ${e.message}", e)
            currentListener?.onError("Failed to speak: ${e.message}")
            false
        }
    }

    /**
     * Stop current speech
     */
    suspend fun stopSpeaking(): Boolean = withContext(Dispatchers.Main) {
        try {
            if (!isSpeaking) {
                return@withContext true
            }
            
            val measurementId = performanceUtils.startMeasurement("TTS_Stop")
            
            // Stop synthesis
            speechSynthesizer?.StopSpeakingAsync()
            speechSynthesizer?.close()
            speechSynthesizer = null
            
            isSpeaking = false
            _isSpeaking.value = false
            
            performanceUtils.endMeasurement(measurementId)
            
            currentListener?.onSpeechCanceled()
            currentListener = null
            
            Log.d(TAG, "🛑 Stopped speaking")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop speaking: ${e.message}", e)
            false
        }
    }

    /**
     * Setup synthesis event callbacks
     */
    private fun setupSynthesisCallbacks() {
        speechSynthesizer?.let { synthesizer ->
            
            // Speech started
            synthesizer.SynthesisStarted.addEventListener { _, _ ->
                serviceScope.launch(Dispatchers.Main) {
                    Log.d(TAG, "🎵 Synthesis started")
                }
            }
            
            // Speech progress (streaming)
            synthesizer.Synthesizing.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    val audioData = e.result.audioData
                    val progress = calculateProgress(audioData)
                    currentListener?.onSpeechProgress("", progress)
                }
            }
            
            // Speech completed
            synthesizer.SynthesisCompleted.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    when (e.result.reason) {
                        ResultReason.SynthesizingAudioCompleted -> {
                            Log.d(TAG, "✅ Speech completed successfully")
                            isSpeaking = false
                            _isSpeaking.value = false
                            currentListener?.onSpeechCompleted()
                            currentListener = null
                        }
                        else -> {
                            Log.w(TAG, "⚠️ Speech completed with reason: ${e.result.reason}")
                        }
                    }
                }
            }
            
            // Handle errors
            synthesizer.SynthesisCanceled.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    val errorMessage = "Synthesis canceled or error occurred"
                    
                    Log.e(TAG, "❌ Synthesis error: $errorMessage")
                    
                    isSpeaking = false
                    _isSpeaking.value = false
                    currentListener?.onError(errorMessage)
                    currentListener = null
                }
            }
        }
    }

    /**
     * Create SSML for advanced voice control
     */
    private fun createSSML(
        text: String,
        voice: String,
        rate: Float,
        pitch: Float
    ): String {
        val ratePercent = ((rate - 1.0f) * 100).toInt()
        val pitchPercent = ((pitch - 1.0f) * 100).toInt()
        
        val rateStr = when {
            ratePercent > 0 -> "+${ratePercent}%"
            ratePercent < 0 -> "${ratePercent}%"
            else -> "0%"
        }
        
        val pitchStr = when {
            pitchPercent > 0 -> "+${pitchPercent}%"
            pitchPercent < 0 -> "${pitchPercent}%"
            else -> "0%"
        }
        
        return """
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="$voice">
                    <prosody rate="$rateStr" pitch="$pitchStr">
                        $text
                    </prosody>
                </voice>
            </speak>
        """.trimIndent()
    }

    /**
     * Select appropriate voice for language
     */
    private fun selectVoiceForLanguage(language: String?): String {
        return when (language) {
            Constants.Voice.LANGUAGE_HINDI -> Constants.Voice.VOICE_HINDI_FEMALE
            Constants.Voice.LANGUAGE_ENGLISH -> currentVoice
            else -> currentVoice
        }
    }

    /**
     * Calculate speech progress from audio data
     */
    private fun calculateProgress(audioData: ByteArray?): Float {
        // Simple progress calculation based on audio data size
        // In a real implementation, you might want more sophisticated progress tracking
        return if (audioData != null && audioData.isNotEmpty()) {
            minOf(audioData.size / 10000.0f, 1.0f)
        } else {
            0.0f
        }
    }

    /**
     * Set voice parameters
     */
    fun setVoiceParameters(
        voice: String? = null,
        rate: Float? = null,
        pitch: Float? = null
    ) {
        voice?.let { 
            currentVoice = it
            _currentVoice.value = it
        }
        rate?.let { speechRate = it.coerceIn(Constants.Voice.MIN_SPEECH_RATE, Constants.Voice.MAX_SPEECH_RATE) }
        pitch?.let { speechPitch = it.coerceIn(Constants.Voice.MIN_SPEECH_PITCH, Constants.Voice.MAX_SPEECH_PITCH) }
        
        Log.d(TAG, "🎛️ Voice parameters updated: voice=$currentVoice, rate=$speechRate, pitch=$speechPitch")
    }

    /**
     * Get available voices
     */
    suspend fun getAvailableVoices(): List<String> = withContext(Dispatchers.IO) {
        listOf(
            Constants.Voice.VOICE_JENNY_NEURAL,
            Constants.Voice.VOICE_GUY_NEURAL,
            Constants.Voice.VOICE_HINDI_FEMALE,
            Constants.Voice.VOICE_HINDI_MALE
        )
    }

    /**
     * Check if TTS is available and ready
     */
    fun isAvailable(): Boolean {
        return isInitialized && apiKeyManager.getAzureSpeechKey() != null
    }

    /**
     * Get current speaking state
     */
    fun isCurrentlySpeaking(): Boolean = isSpeaking

    /**
     * Cleanup resources
     */
    fun cleanup() {
        serviceScope.launch {
            stopSpeaking()
            speechSynthesizer?.close()
            speechConfig?.close()
            audioConfig?.close()
        }
    }
}
