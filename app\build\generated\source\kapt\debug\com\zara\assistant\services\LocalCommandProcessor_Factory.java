// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LocalCommandProcessor_Factory implements Factory<LocalCommandProcessor> {
  private final Provider<Context> contextProvider;

  private final Provider<SystemControlManager> systemControlManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public LocalCommandProcessor_Factory(Provider<Context> contextProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.systemControlManagerProvider = systemControlManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public LocalCommandProcessor get() {
    return newInstance(contextProvider.get(), systemControlManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static LocalCommandProcessor_Factory create(Provider<Context> contextProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new LocalCommandProcessor_Factory(contextProvider, systemControlManagerProvider, performanceUtilsProvider);
  }

  public static LocalCommandProcessor newInstance(Context context,
      SystemControlManager systemControlManager, PerformanceUtils performanceUtils) {
    return new LocalCommandProcessor(context, systemControlManager, performanceUtils);
  }
}
