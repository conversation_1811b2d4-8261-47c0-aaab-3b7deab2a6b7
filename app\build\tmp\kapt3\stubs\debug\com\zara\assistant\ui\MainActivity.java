package com.zara.assistant.ui;

/**
 * MainActivity - The stunning entry point of Zara
 * Features:
 * - Beautiful theme setup
 * - Permission management
 * - Navigation setup
 * - Service initialization
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001a\u001a\u00020\u001bH\u0003J\b\u0010\u001c\u001a\u00020\u001bH\u0002J\b\u0010\u001d\u001a\u00020\nH\u0002J\u0012\u0010\u001e\u001a\u00020\u001b2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0014J\b\u0010!\u001a\u00020\u001bH\u0014J\b\u0010\"\u001a\u00020\u001bH\u0014J\b\u0010#\u001a\u00020\u001bH\u0002J\b\u0010$\u001a\u00020\u001bH\u0002J\b\u0010%\u001a\u00020\u001bH\u0002R\u001e\u0010\u0003\u001a\u00020\u00048\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR+\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\n8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u0010\u0010\u0011\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000fR\u001a\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R+\u0010\u0016\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\n8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u0019\u0010\u0011\u001a\u0004\b\u0017\u0010\r\"\u0004\b\u0018\u0010\u000f\u00a8\u0006&"}, d2 = {"Lcom/zara/assistant/ui/MainActivity;", "Landroidx/fragment/app/FragmentActivity;", "()V", "directPermissionManager", "Lcom/zara/assistant/utils/DirectPermissionManager;", "getDirectPermissionManager", "()Lcom/zara/assistant/utils/DirectPermissionManager;", "setDirectPermissionManager", "(Lcom/zara/assistant/utils/DirectPermissionManager;)V", "<set-?>", "", "hasRequiredPermissions", "getHasRequiredPermissions", "()Z", "setHasRequiredPermissions", "(Z)V", "hasRequiredPermissions$delegate", "Landroidx/compose/runtime/MutableState;", "permissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "", "showPermissionScreen", "getShowPermissionScreen", "setShowPermissionScreen", "showPermissionScreen$delegate", "ZaraApp", "", "checkPermissions", "isFirstLaunch", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onResume", "openAppSettings", "setFirstLaunchComplete", "startServices", "app_debug"})
public final class MainActivity extends androidx.fragment.app.FragmentActivity {
    @javax.inject.Inject()
    public com.zara.assistant.utils.DirectPermissionManager directPermissionManager;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState hasRequiredPermissions$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState showPermissionScreen$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher = null;
    
    public MainActivity() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DirectPermissionManager getDirectPermissionManager() {
        return null;
    }
    
    public final void setDirectPermissionManager(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DirectPermissionManager p0) {
    }
    
    private final boolean getHasRequiredPermissions() {
        return false;
    }
    
    private final void setHasRequiredPermissions(boolean p0) {
    }
    
    private final boolean getShowPermissionScreen() {
        return false;
    }
    
    private final void setShowPermissionScreen(boolean p0) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @androidx.compose.runtime.Composable()
    private final void ZaraApp() {
    }
    
    /**
     * Check required permissions
     */
    private final void checkPermissions() {
    }
    
    /**
     * Start background services
     */
    private final void startServices() {
    }
    
    /**
     * Open app settings
     */
    private final void openAppSettings() {
    }
    
    /**
     * Check if this is the first launch
     */
    private final boolean isFirstLaunch() {
        return false;
    }
    
    /**
     * Mark first launch as complete
     */
    private final void setFirstLaunchComplete() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
}