// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PerformanceUtils_Factory implements Factory<PerformanceUtils> {
  private final Provider<Context> contextProvider;

  public PerformanceUtils_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PerformanceUtils get() {
    return newInstance(contextProvider.get());
  }

  public static PerformanceUtils_Factory create(Provider<Context> contextProvider) {
    return new PerformanceUtils_Factory(contextProvider);
  }

  public static PerformanceUtils newInstance(Context context) {
    return new PerformanceUtils(context);
  }
}
