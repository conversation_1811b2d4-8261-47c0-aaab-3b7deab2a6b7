"com.zara.assistant.ZaraApplication$com.zara.assistant.di.EncryptedPrefs"com.zara.assistant.di.RegularPrefs"com.zara.assistant.di.IoDispatcher$com.zara.assistant.di.MainDispatcher'com.zara.assistant.di.DefaultDispatcher&com.zara.assistant.di.ApplicationScope*com.zara.assistant.domain.model.AIResponse(com.zara.assistant.domain.model.AISource,com.zara.assistant.domain.model.Conversation/com.zara.assistant.domain.model.PermissionState,com.zara.assistant.domain.model.VoiceCommand/com.zara.assistant.domain.model.CommandCategory-com.zara.assistant.domain.model.CommandIntent+com.zara.assistant.domain.model.VoiceSource*com.zara.assistant.domain.model.VoiceState/com.zara.assistant.domain.model.UserPreferences5com.zara.assistant.services.CohereRequest.$serializer6com.zara.assistant.services.CohereResponse.$serializer8com.zara.assistant.services.CohereGeneration.$serializer7com.zara.assistant.services.NotificationListenerService9com.zara.assistant.services.PerplexityRequest.$serializer9com.zara.assistant.services.PerplexityMessage.$serializer:com.zara.assistant.services.PerplexityResponse.$serializer8com.zara.assistant.services.PerplexityChoice.$serializer7com.zara.assistant.services.PerplexityUsage.$serializer+com.zara.assistant.services.WakeWordService4com.zara.assistant.services.ZaraAccessibilityService3com.zara.assistant.services.ZaraDeviceAdminReceiver,com.zara.assistant.services.ZaraVoiceService<com.zara.assistant.services.ZaraVoiceService.ZaraVoiceBinder"com.zara.assistant.ui.MainActivity-com.zara.assistant.ui.viewmodel.MainViewModel(com.zara.assistant.utils.PermissionState#com.zara.assistant.utils.DeviceTier;<EMAIL>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 