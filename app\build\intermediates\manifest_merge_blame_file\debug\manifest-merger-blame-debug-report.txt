1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zara.assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Essential Audio and Speech Permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
14
15    <!-- Communication Permissions -->
16    <uses-permission android:name="android.permission.CALL_PHONE" />
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-69
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-66
17    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-75
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-72
18    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
18-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.READ_CALL_LOG" />
19-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:5-72
19-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:22-69
20    <uses-permission android:name="android.permission.READ_CONTACTS" />
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-72
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-69
21    <uses-permission android:name="android.permission.SEND_SMS" />
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-67
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-64
22    <uses-permission android:name="android.permission.READ_SMS" />
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-64
23
24    <!-- Network Permissions -->
25    <uses-permission android:name="android.permission.INTERNET" />
25-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:5-67
25-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:22-64
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-79
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-76
27
28    <!-- System Control Permissions -->
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:5-78
29-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:22-75
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-77
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-88
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-85
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-88
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-85
34
35    <!-- Connectivity Control -->
36    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
36-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:5-76
36-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:22-73
37    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
37-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:5-76
37-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH" />
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:5-68
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:22-65
39    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-74
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-71
40    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
40-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-76
40-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-73
41    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
41-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:5-73
41-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:22-70
42    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:5-78
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:22-75
43    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
43-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:5-79
43-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:22-76
44
45    <!-- Device Features -->
46    <uses-permission android:name="android.permission.CAMERA" />
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:5-65
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:22-62
47    <uses-permission android:name="android.permission.FLASHLIGHT" />
47-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:5-69
47-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:22-66
48    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
48-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-79
48-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-76
49    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-81
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-78
50
51    <!-- Notification and Accessibility -->
52    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
52-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-47:47
52-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-90
53    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
53-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-77
53-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-74
54    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:5-50:47
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:22-82
55    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
56
57    <!-- Storage -->
58    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-80
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-77
59    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-81
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-78
60
61    <!-- Hardware Features -->
62    <uses-feature
62-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:58:5-60:35
63        android:name="android.hardware.microphone"
63-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:9-51
64        android:required="true" />
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:60:9-32
65    <uses-feature
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:61:5-63:36
66        android:name="android.hardware.camera"
66-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:62:9-47
67        android:required="false" />
67-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:9-33
68    <uses-feature
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-66:36
69        android:name="android.hardware.camera.flash"
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:65:9-53
70        android:required="false" />
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:66:9-33
71    <uses-feature
71-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-69:36
72        android:name="android.hardware.bluetooth"
72-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:9-50
73        android:required="false" />
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:9-33
74    <uses-feature
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:70:5-72:36
75        android:name="android.hardware.wifi"
75-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:71:9-45
76        android:required="false" />
76-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:72:9-33
77
78    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
78-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
78-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
79    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
80    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
81    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
81-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:23:5-25:53
81-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:24:9-61
82    <uses-permission android:name="android.permission.REORDER_TASKS" />
82-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:5-72
82-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:22-69
83
84    <permission
84-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
85        android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
85-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
86        android:protectionLevel="signature" />
86-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
87
88    <uses-permission android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
89
90    <queries>
90-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:24:5-28:15
91        <package android:name="androidx.test.orchestrator" />
91-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:9-62
91-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:18-59
92        <package android:name="androidx.test.services" />
92-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:9-58
92-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:18-55
93        <package android:name="com.google.android.apps.common.testing.services" />
93-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:9-83
93-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:18-80
94    </queries>
95
96    <application
96-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-166:19
97        android:name="com.zara.assistant.ZaraApplication"
97-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:75:9-40
98        android:allowBackup="true"
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:76:9-35
99        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
99-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
100        android:dataExtractionRules="@xml/data_extraction_rules"
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:77:9-65
101        android:debuggable="true"
102        android:extractNativeLibs="false"
103        android:fullBackupContent="@xml/backup_rules"
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:9-54
104        android:hardwareAccelerated="true"
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:9-43
105        android:icon="@mipmap/ic_launcher"
105-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:9-43
106        android:label="@string/app_name"
106-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:80:9-41
107        android:largeHeap="true"
107-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:9-33
108        android:networkSecurityConfig="@xml/network_security_config"
108-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:42:18-78
109        android:roundIcon="@mipmap/ic_launcher_round"
109-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:9-54
110        android:supportsRtl="true"
110-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:82:9-35
111        android:theme="@style/Theme.Zara" >
111-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:83:9-42
112
113        <!-- Main Activity -->
114        <activity
114-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:89:9-103:20
115            android:name="com.zara.assistant.ui.MainActivity"
115-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:90:13-44
116            android:exported="true"
116-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:13-36
117            android:launchMode="singleTop"
117-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:94:13-43
118            android:screenOrientation="portrait"
118-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:13-49
119            android:theme="@style/Theme.Zara.NoActionBar" >
119-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:13-58
120            <intent-filter>
120-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:95:13-98:29
121                <action android:name="android.intent.action.MAIN" />
121-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:17-69
121-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:25-66
122
123                <category android:name="android.intent.category.LAUNCHER" />
123-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
123-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
124            </intent-filter>
125            <intent-filter>
125-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:99:13-102:29
126                <action android:name="android.intent.action.VOICE_COMMAND" />
126-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:17-78
126-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:25-75
127
128                <category android:name="android.intent.category.DEFAULT" />
128-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:17-76
128-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:27-73
129            </intent-filter>
130        </activity>
131
132        <!-- Voice Service -->
133        <service
133-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:106:9-110:58
134            android:name="com.zara.assistant.services.ZaraVoiceService"
134-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:107:13-54
135            android:enabled="true"
135-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:13-35
136            android:exported="false"
136-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:109:13-37
137            android:foregroundServiceType="microphone" />
137-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:13-55
138
139        <!-- Wake Word Service -->
140        <service
140-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:113:9-117:58
141            android:name="com.zara.assistant.services.WakeWordService"
141-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:13-53
142            android:enabled="true"
142-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:115:13-35
143            android:exported="false"
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:116:13-37
144            android:foregroundServiceType="microphone" />
144-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:117:13-55
145
146        <!-- Accessibility Service -->
147        <service
147-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:9-130:19
148            android:name="com.zara.assistant.services.ZaraAccessibilityService"
148-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:13-62
149            android:exported="true"
149-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:13-36
150            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
150-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:13-79
151            <intent-filter>
151-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:13-126:29
152                <action android:name="android.accessibilityservice.AccessibilityService" />
152-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:17-92
152-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:25-89
153            </intent-filter>
154
155            <meta-data
155-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:127:13-129:72
156                android:name="android.accessibilityservice"
156-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:128:17-60
157                android:resource="@xml/accessibility_service_config" />
157-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:129:17-69
158        </service>
159
160        <!-- Notification Listener Service -->
161        <service
161-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:133:9-140:19
162            android:name="com.zara.assistant.services.NotificationListenerService"
162-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:13-65
163            android:exported="true"
163-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-36
164            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
164-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-87
165            <intent-filter>
165-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:13-139:29
166                <action android:name="android.service.notification.NotificationListenerService" />
166-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:17-99
166-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:25-96
167            </intent-filter>
168        </service>
169
170        <!-- Device Admin Receiver -->
171        <receiver
171-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:143:9-153:20
172            android:name="com.zara.assistant.services.ZaraDeviceAdminReceiver"
172-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:144:13-61
173            android:exported="true"
173-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:13-36
174            android:permission="android.permission.BIND_DEVICE_ADMIN" >
174-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:13-70
175            <meta-data
175-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:147:13-149:56
176                android:name="android.app.device_admin"
176-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:148:17-56
177                android:resource="@xml/device_admin" />
177-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:149:17-53
178
179            <intent-filter>
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:150:13-152:29
180                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
180-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:17-82
180-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:25-79
181            </intent-filter>
182        </receiver>
183
184        <!-- File Provider -->
185        <provider
186            android:name="androidx.core.content.FileProvider"
186-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:157:13-62
187            android:authorities="com.zara.assistant.fileprovider"
187-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:13-64
188            android:exported="false"
188-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:159:13-37
189            android:grantUriPermissions="true" >
189-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:160:13-47
190            <meta-data
190-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:13-163:54
191                android:name="android.support.FILE_PROVIDER_PATHS"
191-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:17-67
192                android:resource="@xml/file_paths" />
192-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:163:17-51
193        </provider>
194
195        <service
195-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
196            android:name="androidx.camera.core.impl.MetadataHolderService"
196-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
197            android:enabled="false"
197-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
198            android:exported="false" >
198-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
199            <meta-data
199-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
200                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
200-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
201                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
201-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
202        </service>
203
204        <provider
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
205            android:name="androidx.startup.InitializationProvider"
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
206            android:authorities="com.zara.assistant.androidx-startup"
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
207            android:exported="false" >
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
208            <meta-data
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
209                android:name="androidx.work.WorkManagerInitializer"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
210                android:value="androidx.startup" />
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
211            <meta-data
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.emoji2.text.EmojiCompatInitializer"
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
213                android:value="androidx.startup" />
213-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
214            <meta-data
214-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
215-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
216                android:value="androidx.startup" />
216-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
218                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
218-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
219                android:value="androidx.startup" />
219-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
220            <meta-data
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
222                android:value="androidx.startup" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
223        </provider>
224
225        <service
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
226            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
228            android:enabled="@bool/enable_system_alarm_service_default"
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
229            android:exported="false" />
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
230        <service
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
231            android:name="androidx.work.impl.background.systemjob.SystemJobService"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
233            android:enabled="@bool/enable_system_job_service_default"
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
234            android:exported="true"
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
235            android:permission="android.permission.BIND_JOB_SERVICE" />
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
236        <service
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
237            android:name="androidx.work.impl.foreground.SystemForegroundService"
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
239            android:enabled="@bool/enable_system_foreground_service_default"
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
240            android:exported="false" />
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
241
242        <receiver
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
243            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
244            android:directBootAware="false"
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
245            android:enabled="true"
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
246            android:exported="false" />
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
247        <receiver
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
248            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
250            android:enabled="false"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
251            android:exported="false" >
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
252            <intent-filter>
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
253                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
254                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
255            </intent-filter>
256        </receiver>
257        <receiver
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
258            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
259            android:directBootAware="false"
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
260            android:enabled="false"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
261            android:exported="false" >
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
262            <intent-filter>
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
263                <action android:name="android.intent.action.BATTERY_OKAY" />
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
264                <action android:name="android.intent.action.BATTERY_LOW" />
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
265            </intent-filter>
266        </receiver>
267        <receiver
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
268            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
269            android:directBootAware="false"
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
270            android:enabled="false"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
271            android:exported="false" >
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
272            <intent-filter>
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
273                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
274                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
275            </intent-filter>
276        </receiver>
277        <receiver
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
278            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
279            android:directBootAware="false"
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
280            android:enabled="false"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
281            android:exported="false" >
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
282            <intent-filter>
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
283                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
284            </intent-filter>
285        </receiver>
286        <receiver
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
287            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
287-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
289            android:enabled="false"
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
291            <intent-filter>
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
292                <action android:name="android.intent.action.BOOT_COMPLETED" />
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
293                <action android:name="android.intent.action.TIME_SET" />
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
294                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
295            </intent-filter>
296        </receiver>
297        <receiver
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
298            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
299            android:directBootAware="false"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
300            android:enabled="@bool/enable_system_alarm_service_default"
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
301            android:exported="false" >
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
302            <intent-filter>
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
303                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
304            </intent-filter>
305        </receiver>
306        <receiver
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
307            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
308            android:directBootAware="false"
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
309            android:enabled="true"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
310            android:exported="true"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
311            android:permission="android.permission.DUMP" >
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
312            <intent-filter>
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
313                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
314            </intent-filter>
315        </receiver>
316
317        <activity
317-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
318            android:name="androidx.activity.ComponentActivity"
318-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
319            android:exported="true" />
319-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
320        <activity
320-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
321            android:name="androidx.compose.ui.tooling.PreviewActivity"
321-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
322            android:exported="true" /> <!-- Activity used to block background content while benchmarks are running -->
322-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
323        <activity
323-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:32:9-36:20
324            android:name="androidx.benchmark.IsolationActivity"
324-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:33:13-64
325            android:exported="true"
325-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:34:13-36
326            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
326-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:35:13-77
327        </activity>
328        <activity
328-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:27:9-34:20
329            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
329-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:28:13-99
330            android:exported="true"
330-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:29:13-36
331            android:theme="@style/WhiteBackgroundTheme" >
331-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:30:13-56
332            <intent-filter android:priority="-100" >
332-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
332-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
333                <category android:name="android.intent.category.LAUNCHER" />
333-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
333-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
334            </intent-filter>
335        </activity>
336        <activity
336-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:35:9-42:20
337            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
337-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:36:13-95
338            android:exported="true"
338-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:37:13-36
339            android:theme="@style/WhiteBackgroundTheme" >
339-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:38:13-56
340            <intent-filter android:priority="-100" >
340-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
340-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
341                <category android:name="android.intent.category.LAUNCHER" />
341-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
341-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
342            </intent-filter>
343        </activity>
344        <activity
344-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:43:9-50:20
345            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
345-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:44:13-103
346            android:exported="true"
346-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:45:13-36
347            android:theme="@style/WhiteBackgroundDialogTheme" >
347-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:46:13-62
348            <intent-filter android:priority="-100" >
348-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
348-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
349                <category android:name="android.intent.category.LAUNCHER" />
349-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
349-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
350            </intent-filter>
351        </activity>
352
353        <service
353-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
354            android:name="androidx.room.MultiInstanceInvalidationService"
354-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
355            android:directBootAware="true"
355-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
356            android:exported="false" />
356-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
357
358        <receiver
358-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
359            android:name="androidx.tracing.perfetto.TracingReceiver"
359-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
360            android:directBootAware="false"
360-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
361            android:enabled="true"
361-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
362            android:exported="true"
362-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
363            android:permission="android.permission.DUMP" >
363-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
364
365            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
366            <intent-filter>
366-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
367                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
367-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
367-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
368                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
368-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
368-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
369                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
369-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
369-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
373            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
373-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
374            android:directBootAware="false"
374-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
375            android:enabled="false"
375-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
376            android:exported="false" >
376-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
377        </receiver>
378        <receiver
378-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
379            android:name="androidx.profileinstaller.ProfileInstallReceiver"
379-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
380            android:directBootAware="false"
380-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
381            android:enabled="true"
381-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
382            android:exported="true"
382-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
383            android:permission="android.permission.DUMP" >
383-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
384            <intent-filter>
384-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
385                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
385-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
385-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
386            </intent-filter>
387            <intent-filter>
387-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
388                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
388-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
388-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
389            </intent-filter>
390            <intent-filter>
390-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
391                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
391-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
391-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
392            </intent-filter>
393            <intent-filter>
393-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
394                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
394-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
394-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
395            </intent-filter>
396        </receiver>
397
398        <provider
398-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
399            android:name="com.microsoft.cognitiveservices.speech.util.InternalContentProvider"
399-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
400            android:authorities="com.zara.assistant.MicrosoftCognitiveServicesSpeech.InternalContentProvider"
400-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
401            android:exported="false" />
401-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
402    </application>
403
404</manifest>
