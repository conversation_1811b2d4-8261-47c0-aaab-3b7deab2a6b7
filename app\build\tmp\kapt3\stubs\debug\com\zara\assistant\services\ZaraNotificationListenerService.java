package com.zara.assistant.services;

/**
 * Zara Notification Listener Service - Intelligent notification management
 * Features:
 * - Real-time notification monitoring
 * - Smart notification filtering
 * - Voice-controlled notification actions
 * - Intelligent notification summaries
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\t\u0018\u0000 12\u00020\u0001:\u00011B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0010\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u000e\u0010\u0013\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\u0015J\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\t0\u001bH\u0086@\u00a2\u0006\u0002\u0010\u0015J\u0010\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\bH\u0002J\u0010\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010\"\u001a\u00020\u001f2\u0006\u0010#\u001a\u00020$H\u0002J\u0010\u0010%\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\bH\u0002J\u000e\u0010&\u001a\u00020\'H\u0082@\u00a2\u0006\u0002\u0010\u0015J\b\u0010(\u001a\u00020)H\u0016J\b\u0010*\u001a\u00020)H\u0016J\b\u0010+\u001a\u00020)H\u0016J\u0010\u0010,\u001a\u00020)2\u0006\u0010\u0011\u001a\u00020\u0012H\u0016J\u0010\u0010-\u001a\u00020)2\u0006\u0010\u0011\u001a\u00020\u0012H\u0016J\u0010\u0010.\u001a\u00020\u001f2\u0006\u0010/\u001a\u00020\tH\u0002J\u0010\u00100\u001a\u00020\u001f2\u0006\u0010/\u001a\u00020\tH\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/zara/assistant/services/ZaraNotificationListenerService;", "Landroid/service/notification/NotificationListenerService;", "()V", "_notificationEvents", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Lcom/zara/assistant/services/NotificationEvent;", "notificationCache", "", "", "Lcom/zara/assistant/domain/repository/NotificationData;", "notificationEvents", "Lkotlinx/coroutines/flow/SharedFlow;", "getNotificationEvents", "()Lkotlinx/coroutines/flow/SharedFlow;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "convertToNotificationData", "sbn", "Landroid/service/notification/StatusBarNotification;", "dismissAllNotifications", "Lcom/zara/assistant/domain/model/SystemControlResult;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "dismissNotification", "key", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateNotificationSummary", "getActiveNotificationData", "", "getAppName", "packageName", "hasReplyAction", "", "notification", "Landroid/app/Notification;", "isOldNotification", "timestamp", "", "isSystemNotification", "loadExistingNotifications", "", "onDestroy", "", "onListenerConnected", "onListenerDisconnected", "onNotificationPosted", "onNotificationRemoved", "shouldIncludeInSummary", "notificationData", "shouldProcessNotification", "Companion", "app_debug"})
public final class ZaraNotificationListenerService extends android.service.notification.NotificationListenerService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ZaraNotificationListener";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.zara.assistant.domain.repository.NotificationData> notificationCache = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<com.zara.assistant.services.NotificationEvent> _notificationEvents = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<com.zara.assistant.services.NotificationEvent> notificationEvents = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.ZaraNotificationListenerService.Companion Companion = null;
    
    public ZaraNotificationListenerService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<com.zara.assistant.services.NotificationEvent> getNotificationEvents() {
        return null;
    }
    
    @java.lang.Override()
    public void onListenerConnected() {
    }
    
    @java.lang.Override()
    public void onNotificationPosted(@org.jetbrains.annotations.NotNull()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    @java.lang.Override()
    public void onNotificationRemoved(@org.jetbrains.annotations.NotNull()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    /**
     * Get all active notifications with intelligent filtering
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getActiveNotificationData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.repository.NotificationData>> $completion) {
        return null;
    }
    
    /**
     * Generate intelligent notification summary
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object generateNotificationSummary(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Dismiss notification by key
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object dismissNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SystemControlResult> $completion) {
        return null;
    }
    
    /**
     * Dismiss all notifications
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object dismissAllNotifications(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SystemControlResult> $completion) {
        return null;
    }
    
    /**
     * Load existing notifications on service start
     */
    private final java.lang.Object loadExistingNotifications(kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Convert StatusBarNotification to NotificationData
     */
    private final com.zara.assistant.domain.repository.NotificationData convertToNotificationData(android.service.notification.StatusBarNotification sbn) {
        return null;
    }
    
    /**
     * Get app name from package name
     */
    private final java.lang.String getAppName(java.lang.String packageName) {
        return null;
    }
    
    /**
     * Check if notification has reply action
     */
    private final boolean hasReplyAction(android.app.Notification notification) {
        return false;
    }
    
    /**
     * Determine if notification should be processed
     */
    private final boolean shouldProcessNotification(com.zara.assistant.domain.repository.NotificationData notificationData) {
        return false;
    }
    
    /**
     * Determine if notification should be included in summary
     */
    private final boolean shouldIncludeInSummary(com.zara.assistant.domain.repository.NotificationData notificationData) {
        return false;
    }
    
    /**
     * Check if package is a system notification
     */
    private final boolean isSystemNotification(java.lang.String packageName) {
        return false;
    }
    
    /**
     * Check if notification is too old (older than 24 hours)
     */
    private final boolean isOldNotification(long timestamp) {
        return false;
    }
    
    @java.lang.Override()
    public void onListenerDisconnected() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/ZaraNotificationListenerService$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}