package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0002H\u0003\u001a\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\bH\u0003\u001a\u0016\u0010\t\u001a\u00020\u00042\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u000bH\u0007\"\u0014\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"commandCategories", "", "Lcom/zara/assistant/ui/screens/CommandCategory;", "CommandCategoryCard", "", "category", "CommandItem", "command", "Lcom/zara/assistant/ui/screens/VoiceCommand;", "CommandsHelpScreen", "onNavigateBack", "Lkotlin/Function0;", "app_debug"})
public final class CommandsHelpScreenKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.zara.assistant.ui.screens.CommandCategory> commandCategories = null;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CommandsHelpScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CommandCategoryCard(com.zara.assistant.ui.screens.CommandCategory category) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CommandItem(com.zara.assistant.ui.screens.VoiceCommand command) {
    }
}