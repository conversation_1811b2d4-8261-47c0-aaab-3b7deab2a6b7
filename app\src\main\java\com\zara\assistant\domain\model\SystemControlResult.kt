package com.zara.assistant.domain.model

/**
 * System Control Result Model - Represents results of system operations
 */
data class SystemControlResult(
    val success: Boolean,
    val action: String,
    val message: String,
    val errorCode: String? = null,
    val data: Map<String, Any> = emptyMap()
)

/**
 * Permission State enum
 */
enum class PermissionState {
    GRANTED,
    DENIED,
    PENDING,
    NOT_REQUESTED
}

/**
 * Device State Model
 */
data class DeviceState(
    val wifiEnabled: Boolean,
    val bluetoothEnabled: Boolean,
    val mobileDataEnabled: Boolean,
    val dndEnabled: Boolean,
    val flashlightEnabled: Boolean,
    val volumeLevel: Int,
    val brightnessLevel: Int,
    val batteryLevel: Int,
    val isCharging: Boolean
)

/**
 * App Info Model
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val isSystemApp: Boolean,
    val isEnabled: Boolean,
    val versionName: String,
    val versionCode: Long
)
