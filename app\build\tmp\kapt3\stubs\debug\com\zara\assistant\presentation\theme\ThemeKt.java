package com.zara.assistant.presentation.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a%\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00142\u0011\u0010\u0015\u001a\r\u0012\u0004\u0012\u00020\u00120\u0016\u00a2\u0006\u0002\b\u0017H\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0005\"\u000e\u0010\u0006\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0005\"\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0015\u0010\r\u001a\u00020\u0003*\u00020\u000e8G\u00a2\u0006\u0006\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0018"}, d2 = {"DarkColorScheme", "Landroidx/compose/material3/ColorScheme;", "DarkNeumorphismColors", "Lcom/zara/assistant/presentation/theme/NeumorphismColors;", "getDarkNeumorphismColors", "()Lcom/zara/assistant/presentation/theme/NeumorphismColors;", "LightColorScheme", "LightNeumorphismColors", "getLightNeumorphismColors", "LocalNeumorphismColors", "Landroidx/compose/runtime/ProvidableCompositionLocal;", "getLocalNeumorphismColors", "()Landroidx/compose/runtime/ProvidableCompositionLocal;", "neumorphismColors", "Landroidx/compose/material3/MaterialTheme;", "getNeumorphismColors", "(Landroidx/compose/material3/MaterialTheme;)Lcom/zara/assistant/presentation/theme/NeumorphismColors;", "ZaraTheme", "", "darkTheme", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class ThemeKt {
    
    /**
     * Zara Neumorphism Theme - Beautiful soft UI design
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme LightColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme DarkColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.presentation.theme.NeumorphismColors LightNeumorphismColors = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.presentation.theme.NeumorphismColors DarkNeumorphismColors = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.runtime.ProvidableCompositionLocal<com.zara.assistant.presentation.theme.NeumorphismColors> LocalNeumorphismColors = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.NeumorphismColors getLightNeumorphismColors() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.NeumorphismColors getDarkNeumorphismColors() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.runtime.ProvidableCompositionLocal<com.zara.assistant.presentation.theme.NeumorphismColors> getLocalNeumorphismColors() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ZaraTheme(boolean darkTheme, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.NeumorphismColors getNeumorphismColors(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.MaterialTheme $this$neumorphismColors) {
        return null;
    }
}