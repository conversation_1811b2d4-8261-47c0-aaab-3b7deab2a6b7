package com.zara.assistant.services;

/**
 * Zara Notification Listener Service - Mind-blowing notification management
 * Features:
 * - Intelligent notification filtering
 * - Voice-controlled notification actions
 * - Smart notification summaries
 * - Priority-based handling
 * - Real-time notification streaming
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0000\n\u0002\u0010\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0002\u0018\u0000 .2\u00020\u0001:\u0001.B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u0019J\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00050\u001bH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u000e\u0010\u001c\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u0005H\u0082@\u00a2\u0006\u0002\u0010 J\b\u0010!\u001a\u00020\u001eH\u0002J\b\u0010\"\u001a\u00020\u001eH\u0016J\b\u0010#\u001a\u00020\u001eH\u0016J\u0012\u0010$\u001a\u00020\u001e2\b\u0010%\u001a\u0004\u0018\u00010&H\u0016J\u0012\u0010\'\u001a\u00020\u001e2\b\u0010%\u001a\u0004\u0018\u00010&H\u0016J\u0010\u0010(\u001a\u00020\u00052\u0006\u0010%\u001a\u00020&H\u0002J\u001e\u0010)\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00072\u0006\u0010*\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010+J\u0010\u0010,\u001a\u00020-2\u0006\u0010\u001f\u001a\u00020\u0005H\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/zara/assistant/services/NotificationListenerService;", "Landroid/service/notification/NotificationListenerService;", "()V", "_newNotifications", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Lcom/zara/assistant/services/NotificationData;", "_removedNotifications", "", "activeNotifications", "", "importantApps", "", "newNotifications", "Lkotlinx/coroutines/flow/SharedFlow;", "getNewNotifications", "()Lkotlinx/coroutines/flow/SharedFlow;", "removedNotifications", "getRemovedNotifications", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "dismissAllNotifications", "Lcom/zara/assistant/domain/model/SystemControlResult;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "dismissNotification", "key", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveNotifications", "", "getNotificationSummary", "handleHighPriorityNotification", "", "notification", "(Lcom/zara/assistant/services/NotificationData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadExistingNotifications", "onListenerConnected", "onListenerDisconnected", "onNotificationPosted", "sbn", "Landroid/service/notification/StatusBarNotification;", "onNotificationRemoved", "parseNotification", "replyToNotification", "message", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "shouldProcessNotification", "", "Companion", "app_release"})
public final class NotificationListenerService extends android.service.notification.NotificationListenerService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ZaraNotifications";
    @org.jetbrains.annotations.Nullable()
    private static android.service.notification.NotificationListenerService instance;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<com.zara.assistant.services.NotificationData> _newNotifications = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<com.zara.assistant.services.NotificationData> newNotifications = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<java.lang.String> _removedNotifications = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<java.lang.String> removedNotifications = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.zara.assistant.services.NotificationData> activeNotifications = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> importantApps = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.NotificationListenerService.Companion Companion = null;
    
    public NotificationListenerService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<com.zara.assistant.services.NotificationData> getNewNotifications() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<java.lang.String> getRemovedNotifications() {
        return null;
    }
    
    @java.lang.Override()
    public void onListenerConnected() {
    }
    
    @java.lang.Override()
    public void onNotificationPosted(@org.jetbrains.annotations.Nullable()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    @java.lang.Override()
    public void onNotificationRemoved(@org.jetbrains.annotations.Nullable()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    /**
     * Get all active notifications with intelligent filtering
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getActiveNotifications(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.services.NotificationData>> $completion) {
        return null;
    }
    
    /**
     * Get notification summary for voice reading
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getNotificationSummary(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Dismiss notification by key
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object dismissNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SystemControlResult> $completion) {
        return null;
    }
    
    /**
     * Dismiss all notifications
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object dismissAllNotifications(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SystemControlResult> $completion) {
        return null;
    }
    
    /**
     * Reply to notification (for messaging apps)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object replyToNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SystemControlResult> $completion) {
        return null;
    }
    
    /**
     * Parse notification into structured data
     */
    private final com.zara.assistant.services.NotificationData parseNotification(android.service.notification.StatusBarNotification sbn) {
        return null;
    }
    
    /**
     * Check if notification should be processed
     */
    private final boolean shouldProcessNotification(com.zara.assistant.services.NotificationData notification) {
        return false;
    }
    
    /**
     * Handle high-priority notifications
     */
    private final java.lang.Object handleHighPriorityNotification(com.zara.assistant.services.NotificationData notification, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Load existing notifications on service start
     */
    private final void loadExistingNotifications() {
    }
    
    @java.lang.Override()
    public void onListenerDisconnected() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006J\u0006\u0010\b\u001a\u00020\tR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/services/NotificationListenerService$Companion;", "", "()V", "TAG", "", "instance", "Landroid/service/notification/NotificationListenerService;", "getInstance", "isServiceEnabled", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.service.notification.NotificationListenerService getInstance() {
            return null;
        }
        
        public final boolean isServiceEnabled() {
            return false;
        }
    }
}