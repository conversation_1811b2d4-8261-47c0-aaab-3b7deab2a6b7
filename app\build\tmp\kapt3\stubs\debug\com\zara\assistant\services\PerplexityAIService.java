package com.zara.assistant.services;

/**
 * Perplexity AI Service - Information queries using Perplexity API
 * Optimized for real-time information retrieval and factual responses
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\r\b\u0007\u0018\u0000 32\u00020\u0001:\u00013B!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ(\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00112\b\u0010\u0002\u001a\u0004\u0018\u00010\u0011H\u0002J\u0010\u0010\u0013\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011H\u0002J\u001e\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0017\u001a\u00020\u0011H\u0002J2\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ:\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\u0006\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010 J\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010\"J2\u0010#\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010\u001dJ\u0006\u0010&\u001a\u00020\'J+\u0010(\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\u0006\u0010)\u001a\u00020\u00112\u0006\u0010*\u001a\u00020\u0011H\u0002\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b+\u0010,J.\u0010-\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\u0006\u0010.\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u0010\u001dJ\u001c\u00100\u001a\b\u0012\u0004\u0012\u00020\'0\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b1\u00102R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00064"}, d2 = {"Lcom/zara/assistant/services/PerplexityAIService;", "", "context", "Landroid/content/Context;", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "performanceUtils", "Lcom/zara/assistant/utils/PerformanceUtils;", "(Landroid/content/Context;Lcom/zara/assistant/utils/ApiKeyManager;Lcom/zara/assistant/utils/PerformanceUtils;)V", "httpClient", "Lokhttp3/OkHttpClient;", "json", "Lkotlinx/serialization/json/Json;", "buildMessages", "", "Lcom/zara/assistant/services/PerplexityMessage;", "query", "", "language", "buildSystemPrompt", "createPerplexityRequest", "Lokhttp3/Request;", "messages", "apiKey", "getNewsInfo", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/AIResponse;", "topic", "getNewsInfo-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getResponse", "getResponse-BWLJW6A", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getServiceStatus", "", "getWeatherInfo", "location", "getWeatherInfo-0E7RQCE", "isAvailable", "", "parsePerplexityResponse", "responseBody", "measurementId", "parsePerplexityResponse-gIAlu-s", "(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Object;", "searchInformation", "searchQuery", "searchInformation-0E7RQCE", "testConnection", "testConnection-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class PerplexityAIService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.ApiKeyManager apiKeyManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.PerformanceUtils performanceUtils = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "PerplexityAI";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = "https://api.perplexity.ai/";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CHAT_ENDPOINT = "chat/completions";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.serialization.json.Json json = null;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient httpClient = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.PerplexityAIService.Companion Companion = null;
    
    @javax.inject.Inject()
    public PerplexityAIService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        super();
    }
    
    /**
     * Build messages for chat completion
     */
    private final java.util.List<com.zara.assistant.services.PerplexityMessage> buildMessages(java.lang.String query, java.lang.String language, java.lang.String context) {
        return null;
    }
    
    /**
     * Build system prompt for Perplexity
     */
    private final java.lang.String buildSystemPrompt(java.lang.String language) {
        return null;
    }
    
    /**
     * Create Perplexity API request
     */
    private final okhttp3.Request createPerplexityRequest(java.util.List<com.zara.assistant.services.PerplexityMessage> messages, java.lang.String apiKey) {
        return null;
    }
    
    /**
     * Check if Perplexity service is available
     */
    public final boolean isAvailable() {
        return false;
    }
    
    /**
     * Get service status
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getServiceStatus() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/zara/assistant/services/PerplexityAIService$Companion;", "", "()V", "BASE_URL", "", "CHAT_ENDPOINT", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}