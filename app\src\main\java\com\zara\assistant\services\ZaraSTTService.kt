package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.zara.assistant.core.Constants
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Zara Azure Speech-to-Text Service
 * Ultra-fast STT with Hindi/English support and 30-second timeout
 */
@Singleton
class ZaraSTTService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: ApiKeyManager,
    private val performanceUtils: PerformanceUtils
) {
    companion object {
        private const val TAG = "ZaraSTT"
    }

    // Azure Speech components
    private var speechConfig: SpeechConfig? = null
    private var audioConfig: AudioConfig? = null
    private var speechRecognizer: SpeechRecognizer? = null
    private var autoDetectConfig: AutoDetectSourceLanguageConfig? = null
    
    // Service state
    private var isInitialized = false
    private var isListening = false
    private var currentLanguage = Constants.Voice.LANGUAGE_AUTO
    private var timeoutJob: Job? = null
    private var currentListener: STTListener? = null
    
    // State flows
    private val _isListening = MutableStateFlow(false)
    val isListeningFlow: StateFlow<Boolean> = _isListening.asStateFlow()
    
    private val _detectedLanguage = MutableStateFlow(Constants.Voice.LANGUAGE_AUTO)
    val detectedLanguageFlow: StateFlow<String> = _detectedLanguage.asStateFlow()
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * STT Result Listener Interface
     */
    interface STTListener {
        fun onListeningStarted()
        fun onPartialResult(text: String, language: String)
        fun onFinalResult(text: String, language: String, confidence: Float)
        fun onError(error: String)
        fun onTimeout()
        fun onListeningStopped()
    }

    /**
     * Initialize Azure Speech STT Service
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("STT_Initialize")
            
            Log.d(TAG, "🚀 Initializing Azure STT Service...")
            
            val speechKey = apiKeyManager.getAzureSpeechKey()
            val speechRegion = apiKeyManager.getAzureSpeechRegion()
            
            if (speechKey == null || speechRegion == null) {
                Log.e(TAG, "❌ Azure Speech credentials not available")
                return@withContext false
            }
            
            // Create speech configuration
            speechConfig = SpeechConfig.fromSubscription(speechKey, speechRegion).apply {
                // Optimize for real-time recognition
                setProperty(PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "3000")
                setProperty(PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "1000")
                setProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "500")
                
                // Enable detailed results
                requestWordLevelTimestamps()
                enableDictation()
                
                // Set output format for better performance
                outputFormat = OutputFormat.Detailed
            }
            
            // Create audio configuration for microphone
            audioConfig = AudioConfig.fromDefaultMicrophoneInput()
            
            // Setup auto-detection for Hindi/English
            autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages(
                listOf(Constants.Voice.LANGUAGE_ENGLISH, Constants.Voice.LANGUAGE_HINDI)
            )
            
            isInitialized = true
            performanceUtils.endMeasurement(measurementId)
            
            Log.d(TAG, "✅ Azure STT Service initialized successfully")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize Azure STT: ${e.message}", e)
            false
        }
    }

    /**
     * Start listening for voice input
     */
    suspend fun startListening(
        language: String = Constants.Voice.LANGUAGE_AUTO,
        listener: STTListener
    ): Boolean = withContext(Dispatchers.Main) {
        try {
            if (!isInitialized) {
                Log.w(TAG, "⚠️ STT not initialized")
                return@withContext false
            }
            
            if (isListening) {
                Log.w(TAG, "⚠️ Already listening")
                return@withContext false
            }
            
            val measurementId = performanceUtils.startMeasurement("STT_StartListening")
            
            currentListener = listener
            currentLanguage = language
            
            // Create speech recognizer based on language preference
            speechRecognizer = when (language) {
                Constants.Voice.LANGUAGE_AUTO -> {
                    SpeechRecognizer(speechConfig, autoDetectConfig, audioConfig)
                }
                else -> {
                    speechConfig?.speechRecognitionLanguage = language
                    SpeechRecognizer(speechConfig, audioConfig)
                }
            }
            
            setupRecognitionCallbacks()
            
            // Start continuous recognition
            speechRecognizer?.startContinuousRecognitionAsync()
            
            isListening = true
            _isListening.value = true
            
            // Start timeout timer (30 seconds)
            startTimeoutTimer()
            
            performanceUtils.endMeasurement(measurementId)
            
            listener.onListeningStarted()
            Log.d(TAG, "🎤 Started listening (language: $language)")
            
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start listening: ${e.message}", e)
            currentListener?.onError("Failed to start listening: ${e.message}")
            false
        }
    }

    /**
     * Stop listening
     */
    suspend fun stopListening(): Boolean = withContext(Dispatchers.Main) {
        try {
            if (!isListening) {
                return@withContext true
            }
            
            val measurementId = performanceUtils.startMeasurement("STT_StopListening")
            
            // Cancel timeout
            timeoutJob?.cancel()
            
            // Stop recognition
            speechRecognizer?.stopContinuousRecognitionAsync()
            speechRecognizer?.close()
            speechRecognizer = null
            
            isListening = false
            _isListening.value = false
            
            performanceUtils.endMeasurement(measurementId)
            
            currentListener?.onListeningStopped()
            currentListener = null
            
            Log.d(TAG, "🛑 Stopped listening")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop listening: ${e.message}", e)
            false
        }
    }

    /**
     * Setup recognition event callbacks
     */
    private fun setupRecognitionCallbacks() {
        speechRecognizer?.let { recognizer ->
            
            // Partial results (real-time)
            recognizer.recognizing.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    val text = e.result.text
                    val language = detectLanguageFromResult(e.result)
                    
                    if (text.isNotBlank()) {
                        _detectedLanguage.value = language
                        currentListener?.onPartialResult(text, language)
                        Log.d(TAG, "🔄 Partial: $text ($language)")
                    }
                }
            }
            
            // Final results
            recognizer.recognized.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    when (e.result.reason) {
                        ResultReason.RecognizedSpeech -> {
                            val text = e.result.text
                            val language = detectLanguageFromResult(e.result)
                            val confidence = extractConfidence(e.result)

                            if (text.isNotBlank()) {
                                _detectedLanguage.value = language
                                currentListener?.onFinalResult(text, language, confidence)
                                Log.d(TAG, "✅ Final: $text ($language, confidence: $confidence)")
                            }
                        }
                        ResultReason.NoMatch -> {
                            Log.d(TAG, "🔇 No speech detected")
                        }
                        else -> {
                            Log.d(TAG, "🔄 Other recognition result: ${e.result.reason}")
                        }
                    }
                }
            }
            
            // Handle errors
            recognizer.canceled.addEventListener { _, e ->
                serviceScope.launch(Dispatchers.Main) {
                    val errorMessage = when (e.reason) {
                        CancellationReason.Error -> "Recognition error: ${e.errorDetails}"
                        CancellationReason.EndOfStream -> "End of audio stream"
                        else -> "Recognition canceled: ${e.reason}"
                    }
                    
                    Log.e(TAG, "❌ Recognition error: $errorMessage")
                    currentListener?.onError(errorMessage)
                    
                    // Auto-stop on error
                    serviceScope.launch {
                        stopListening()
                    }
                }
            }
        }
    }

    /**
     * Start timeout timer (30 seconds)
     */
    private fun startTimeoutTimer() {
        timeoutJob = serviceScope.launch {
            delay(Constants.Voice.LISTENING_TIMEOUT_MS)
            
            if (isListening) {
                Log.d(TAG, "⏰ Listening timeout reached")
                withContext(Dispatchers.Main) {
                    currentListener?.onTimeout()
                }
                stopListening()
            }
        }
    }

    /**
     * Detect language from recognition result
     */
    private fun detectLanguageFromResult(result: SpeechRecognitionResult): String {
        return try {
            // Try to get auto-detected language
            val properties = result.properties
            properties.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult)
                ?: currentLanguage
        } catch (e: Exception) {
            currentLanguage
        }
    }

    /**
     * Extract confidence score from result
     */
    private fun extractConfidence(result: SpeechRecognitionResult): Float {
        return try {
            // Azure provides confidence in detailed results
            val json = result.properties.getProperty(PropertyId.SpeechServiceResponse_JsonResult)
            // Parse confidence from JSON if available, otherwise return default
            0.9f // Default high confidence for Azure
        } catch (e: Exception) {
            0.8f // Default confidence
        }
    }

    /**
     * Check if STT is available and ready
     */
    fun isAvailable(): Boolean {
        return isInitialized && apiKeyManager.getAzureSpeechKey() != null
    }

    /**
     * Get current listening state
     */
    fun isCurrentlyListening(): Boolean = isListening

    /**
     * Cleanup resources
     */
    fun cleanup() {
        serviceScope.launch {
            stopListening()
            speechRecognizer?.close()
            speechConfig?.close()
            audioConfig?.close()
        }
    }
}
