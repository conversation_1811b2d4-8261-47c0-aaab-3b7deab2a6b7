<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <!-- Pressed state - inset effect -->
            <item>
                <shape android:shape="oval">
                    <solid android:color="@color/neuro_surface_light" />
                </shape>
            </item>
            <item android:left="4dp" android:top="4dp" android:right="4dp" android:bottom="4dp">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="@color/neuro_light_shadow"
                        android:endColor="@color/neuro_light_highlight"
                        android:angle="135" />
                </shape>
            </item>
            <!-- Voice icon background -->
            <item android:left="20dp" android:top="20dp" android:right="20dp" android:bottom="20dp">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="@color/deep_teal"
                        android:endColor="@color/luminous_blue"
                        android:angle="45" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <!-- Normal state - raised effect with gradient -->
        <layer-list>
            <!-- Dark shadow (bottom-right) -->
            <item android:left="12dp" android:top="12dp">
                <shape android:shape="oval">
                    <solid android:color="@color/neuro_light_shadow" />
                </shape>
            </item>
            <!-- Light highlight (top-left) -->
            <item android:right="12dp" android:bottom="12dp">
                <shape android:shape="oval">
                    <solid android:color="@color/neuro_light_highlight" />
                </shape>
            </item>
            <!-- Main surface -->
            <item android:left="6dp" android:top="6dp" android:right="6dp" android:bottom="6dp">
                <shape android:shape="oval">
                    <solid android:color="@color/neuro_surface_light" />
                </shape>
            </item>
            <!-- Voice icon background -->
            <item android:left="26dp" android:top="26dp" android:right="26dp" android:bottom="26dp">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="@color/deep_teal"
                        android:endColor="@color/luminous_blue"
                        android:angle="45" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
