package com.zara.assistant.services;

/**
 * Zara Azure Speech-to-Text Service
 * Ultra-fast STT with Hindi/English support and 30-second timeout
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u000e\b\u0007\u0018\u0000 62\u00020\u0001:\u000267B!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010$\u001a\u00020%J\u0010\u0010&\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020(H\u0002J\u0010\u0010)\u001a\u00020*2\u0006\u0010\'\u001a\u00020(H\u0002J\u000e\u0010+\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010,J\u0006\u0010-\u001a\u00020\rJ\u0006\u0010.\u001a\u00020\rJ\b\u0010/\u001a\u00020%H\u0002J \u00100\u001a\u00020\r2\b\b\u0002\u00101\u001a\u00020\u000b2\u0006\u00102\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u00103J\b\u00104\u001a\u00020%H\u0002J\u000e\u00105\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010,R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u000e\u0010\u0019\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\r0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00068"}, d2 = {"Lcom/zara/assistant/services/ZaraSTTService;", "", "context", "Landroid/content/Context;", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "performanceUtils", "Lcom/zara/assistant/utils/PerformanceUtils;", "(Landroid/content/Context;Lcom/zara/assistant/utils/ApiKeyManager;Lcom/zara/assistant/utils/PerformanceUtils;)V", "_detectedLanguage", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isListening", "", "audioConfig", "Lcom/microsoft/cognitiveservices/speech/audio/AudioConfig;", "autoDetectConfig", "Lcom/microsoft/cognitiveservices/speech/AutoDetectSourceLanguageConfig;", "currentLanguage", "currentListener", "Lcom/zara/assistant/services/ZaraSTTService$STTListener;", "detectedLanguageFlow", "Lkotlinx/coroutines/flow/StateFlow;", "getDetectedLanguageFlow", "()Lkotlinx/coroutines/flow/StateFlow;", "isInitialized", "isListening", "isListeningFlow", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "speechConfig", "Lcom/microsoft/cognitiveservices/speech/SpeechConfig;", "speechRecognizer", "Lcom/microsoft/cognitiveservices/speech/SpeechRecognizer;", "timeoutJob", "Lkotlinx/coroutines/Job;", "cleanup", "", "detectLanguageFromResult", "result", "Lcom/microsoft/cognitiveservices/speech/SpeechRecognitionResult;", "extractConfidence", "", "initialize", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isAvailable", "isCurrentlyListening", "setupRecognitionCallbacks", "startListening", "language", "listener", "(Ljava/lang/String;Lcom/zara/assistant/services/ZaraSTTService$STTListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startTimeoutTimer", "stopListening", "Companion", "STTListener", "app_debug"})
public final class ZaraSTTService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.ApiKeyManager apiKeyManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.PerformanceUtils performanceUtils = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ZaraSTT";
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechConfig speechConfig;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.audio.AudioConfig audioConfig;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechRecognizer speechRecognizer;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.AutoDetectSourceLanguageConfig autoDetectConfig;
    private boolean isInitialized = false;
    private boolean isListening = false;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentLanguage = "auto";
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job timeoutJob;
    @org.jetbrains.annotations.Nullable()
    private com.zara.assistant.services.ZaraSTTService.STTListener currentListener;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isListening = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _detectedLanguage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> detectedLanguageFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.ZaraSTTService.Companion Companion = null;
    
    @javax.inject.Inject()
    public ZaraSTTService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getDetectedLanguageFlow() {
        return null;
    }
    
    /**
     * Initialize Azure Speech STT Service
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Start listening for voice input
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object startListening(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.ZaraSTTService.STTListener listener, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Stop listening
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object stopListening(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Setup recognition event callbacks
     */
    private final void setupRecognitionCallbacks() {
    }
    
    /**
     * Start timeout timer (30 seconds)
     */
    private final void startTimeoutTimer() {
    }
    
    /**
     * Detect language from recognition result
     */
    private final java.lang.String detectLanguageFromResult(com.microsoft.cognitiveservices.speech.SpeechRecognitionResult result) {
        return null;
    }
    
    /**
     * Extract confidence score from result
     */
    private final float extractConfidence(com.microsoft.cognitiveservices.speech.SpeechRecognitionResult result) {
        return 0.0F;
    }
    
    /**
     * Check if STT is available and ready
     */
    public final boolean isAvailable() {
        return false;
    }
    
    /**
     * Get current listening state
     */
    public final boolean isCurrentlyListening() {
        return false;
    }
    
    /**
     * Cleanup resources
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/ZaraSTTService$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * STT Result Listener Interface
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J \u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\u0003H&J\b\u0010\f\u001a\u00020\u0003H&J\u0018\u0010\r\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u0005H&J\b\u0010\u000e\u001a\u00020\u0003H&\u00a8\u0006\u000f"}, d2 = {"Lcom/zara/assistant/services/ZaraSTTService$STTListener;", "", "onError", "", "error", "", "onFinalResult", "text", "language", "confidence", "", "onListeningStarted", "onListeningStopped", "onPartialResult", "onTimeout", "app_debug"})
    public static abstract interface STTListener {
        
        public abstract void onListeningStarted();
        
        public abstract void onPartialResult(@org.jetbrains.annotations.NotNull()
        java.lang.String text, @org.jetbrains.annotations.NotNull()
        java.lang.String language);
        
        public abstract void onFinalResult(@org.jetbrains.annotations.NotNull()
        java.lang.String text, @org.jetbrains.annotations.NotNull()
        java.lang.String language, float confidence);
        
        public abstract void onError(@org.jetbrains.annotations.NotNull()
        java.lang.String error);
        
        public abstract void onTimeout();
        
        public abstract void onListeningStopped();
    }
}