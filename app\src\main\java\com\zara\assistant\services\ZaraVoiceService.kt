package com.zara.assistant.services

import android.app.Notification
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.zara.assistant.R
import com.zara.assistant.ZaraApplication
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * Zara Voice Orchestration Service
 * Coordinates STT, TTS, AI processing, and system controls
 * Features:
 * - 30-second auto-listening timeout
 * - Hindi/English support with user selection
 * - Smart voice command processing
 * - Ultra-fast performance optimization
 */
@AndroidEntryPoint
class ZaraVoiceService : Service() {
    
    companion object {
        private const val TAG = "ZaraVoiceService"
        private const val NOTIFICATION_ID = ZaraApplication.VOICE_SERVICE_ID
        
        const val ACTION_START_LISTENING = "START_LISTENING"
        const val ACTION_STOP_LISTENING = "STOP_LISTENING"
        const val ACTION_SPEAK = "SPEAK"
        const val ACTION_STOP_SPEAKING = "STOP_SPEAKING"
        const val ACTION_SET_AUTO_LISTEN = "SET_AUTO_LISTEN"
        
        fun startListening(context: Context, language: String = Constants.Voice.LANGUAGE_AUTO) {
            val intent = Intent(context, ZaraVoiceService::class.java).apply {
                action = ACTION_START_LISTENING
                putExtra("language", language)
            }
            ContextCompat.startForegroundService(context, intent)
        }
        
        fun stopListening(context: Context) {
            val intent = Intent(context, ZaraVoiceService::class.java).apply {
                action = ACTION_STOP_LISTENING
            }
            context.startService(intent)
        }
        
        fun speak(context: Context, text: String, language: String? = null) {
            val intent = Intent(context, ZaraVoiceService::class.java).apply {
                action = ACTION_SPEAK
                putExtra("text", text)
                putExtra("language", language)
            }
            context.startService(intent)
        }

        fun setAutoListenEnabled(context: Context, enabled: Boolean) {
            val intent = Intent(context, ZaraVoiceService::class.java).apply {
                action = ACTION_SET_AUTO_LISTEN
                putExtra("enabled", enabled)
            }
            context.startService(intent)
        }
    }

    // Service components
    @Inject lateinit var zaraSTTService: ZaraSTTService
    @Inject lateinit var zaraTTSService: ZaraTTSService
    @Inject lateinit var localCommandProcessor: LocalCommandProcessor
    @Inject lateinit var aiOrchestrationService: AIOrchestrationService

    // Auto-listen settings
    private var isAutoListenEnabled = false
    private var autoListenJob: Job? = null
    
    private val binder = ZaraVoiceBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Service state
    private var isListening = false
    private var isSpeaking = false
    private var currentLanguage = Constants.Voice.LANGUAGE_AUTO
    
    // State flows
    private val _voiceState = MutableStateFlow(VoiceState.IDLE)
    val voiceState: StateFlow<VoiceState> = _voiceState.asStateFlow()
    
    private val _currentCommand = MutableStateFlow<VoiceCommand?>(null)
    val currentCommand: StateFlow<VoiceCommand?> = _currentCommand.asStateFlow()
    
    private val _lastResponse = MutableStateFlow<AIResponse?>(null)
    val lastResponse: StateFlow<AIResponse?> = _lastResponse.asStateFlow()

    inner class ZaraVoiceBinder : Binder() {
        fun getService(): ZaraVoiceService = this@ZaraVoiceService
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 Zara Voice Service created")
        initializeServices()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_LISTENING -> {
                val language = intent.getStringExtra("language") ?: Constants.Voice.LANGUAGE_AUTO
                startListening(language)
            }
            ACTION_STOP_LISTENING -> {
                stopListening()
            }
            ACTION_SPEAK -> {
                val text = intent.getStringExtra("text") ?: ""
                val language = intent.getStringExtra("language")
                speak(text, language)
            }
            ACTION_STOP_SPEAKING -> {
                stopSpeaking()
            }
            ACTION_SET_AUTO_LISTEN -> {
                val enabled = intent.getBooleanExtra("enabled", false)
                setAutoListenEnabled(enabled)
            }
        }
        
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder = binder

    /**
     * Initialize all voice services
     */
    private fun initializeServices() {
        serviceScope.launch {
            try {
                Log.d(TAG, "🔧 Initializing voice services...")
                
                // Initialize STT service
                if (!zaraSTTService.initialize()) {
                    Log.e(TAG, "❌ Failed to initialize STT service")
                    return@launch
                }
                
                // Initialize TTS service
                if (!zaraTTSService.initialize()) {
                    Log.e(TAG, "❌ Failed to initialize TTS service")
                    return@launch
                }
                
                Log.d(TAG, "✅ All voice services initialized successfully")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to initialize services: ${e.message}", e)
            }
        }
    }

    /**
     * Start listening for voice input
     */
    private fun startListening(language: String) {
        serviceScope.launch {
            try {
                if (isListening) {
                    Log.w(TAG, "⚠️ Already listening")
                    return@launch
                }
                
                if (isSpeaking) {
                    Log.d(TAG, "🛑 Stopping speech before listening")
                    stopSpeaking()
                    delay(500) // Brief pause
                }
                
                // Start foreground service
                startForeground(NOTIFICATION_ID, createNotification("Listening..."))
                
                currentLanguage = language
                _voiceState.value = VoiceState.LISTENING
                
                // Start STT with callback
                val success = zaraSTTService.startListening(language, sttListener)
                
                if (success) {
                    isListening = true
                    Log.d(TAG, "🎤 Started listening (language: $language)")
                } else {
                    Log.e(TAG, "❌ Failed to start listening")
                    _voiceState.value = VoiceState.ERROR
                    stopForeground(true)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error starting listening: ${e.message}", e)
                _voiceState.value = VoiceState.ERROR
            }
        }
    }

    /**
     * Stop listening
     */
    private fun stopListening() {
        serviceScope.launch {
            try {
                if (!isListening) {
                    return@launch
                }
                
                zaraSTTService.stopListening()
                isListening = false
                _voiceState.value = VoiceState.IDLE
                
                stopForeground(true)
                
                Log.d(TAG, "🛑 Stopped listening")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error stopping listening: ${e.message}", e)
            }
        }
    }

    /**
     * Speak text using TTS
     */
    private fun speak(text: String, language: String? = null) {
        serviceScope.launch {
            try {
                if (text.isBlank()) {
                    Log.w(TAG, "⚠️ Empty text to speak")
                    return@launch
                }
                
                if (isListening) {
                    Log.d(TAG, "🛑 Stopping listening before speaking")
                    stopListening()
                    delay(300) // Brief pause
                }
                
                _voiceState.value = VoiceState.SPEAKING
                
                // Start TTS with callback
                val success = zaraTTSService.speak(
                    text = text,
                    language = language,
                    listener = ttsListener
                )
                
                if (success) {
                    isSpeaking = true
                    Log.d(TAG, "🗣️ Started speaking: \"${text.take(50)}...\"")
                } else {
                    Log.e(TAG, "❌ Failed to start speaking")
                    _voiceState.value = VoiceState.ERROR
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error speaking: ${e.message}", e)
                _voiceState.value = VoiceState.ERROR
            }
        }
    }

    /**
     * Stop speaking
     */
    private fun stopSpeaking() {
        serviceScope.launch {
            try {
                if (!isSpeaking) {
                    return@launch
                }
                
                zaraTTSService.stopSpeaking()
                isSpeaking = false
                _voiceState.value = VoiceState.IDLE
                
                Log.d(TAG, "🛑 Stopped speaking")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error stopping speaking: ${e.message}", e)
            }
        }
    }

    /**
     * STT Listener implementation
     */
    private val sttListener = object : ZaraSTTService.STTListener {
        override fun onListeningStarted() {
            Log.d(TAG, "👂 STT listening started")
        }

        override fun onPartialResult(text: String, language: String) {
            Log.d(TAG, "🔄 Partial result: $text")
            // Update UI with partial results if needed
        }

        override fun onFinalResult(text: String, language: String, confidence: Float) {
            serviceScope.launch {
                Log.d(TAG, "✅ Final result: $text (confidence: $confidence)")
                
                _voiceState.value = VoiceState.PROCESSING
                
                // Create voice command
                val command = VoiceCommand(
                    id = java.util.UUID.randomUUID().toString(),
                    originalText = text,
                    language = language,
                    confidence = confidence,
                    category = CommandCategory.UNKNOWN,
                    intent = CommandIntent.UNKNOWN,
                    source = VoiceSource.MANUAL
                )
                
                _currentCommand.value = command
                
                // Process command
                processVoiceCommand(command)
            }
        }

        override fun onError(error: String) {
            Log.e(TAG, "❌ STT error: $error")
            _voiceState.value = VoiceState.ERROR
            
            // Auto-restart on error if auto-listen is enabled
            if (isAutoListenEnabled) {
                serviceScope.launch {
                    delay(2000)
                    startListening(currentLanguage)
                }
            }
        }

        override fun onTimeout() {
            Log.d(TAG, "⏰ STT timeout")
            _voiceState.value = VoiceState.IDLE
            
            // Auto-restart if auto-listen is enabled
            if (isAutoListenEnabled) {
                serviceScope.launch {
                    delay(1000)
                    startListening(currentLanguage)
                }
            }
        }

        override fun onListeningStopped() {
            Log.d(TAG, "🛑 STT listening stopped")
            isListening = false
        }
    }

    /**
     * TTS Listener implementation
     */
    private val ttsListener = object : ZaraTTSService.TTSListener {
        override fun onSpeechStarted() {
            Log.d(TAG, "🗣️ TTS speech started")
        }

        override fun onSpeechProgress(text: String, progress: Float) {
            // Update progress if needed
        }

        override fun onSpeechCompleted() {
            Log.d(TAG, "✅ TTS speech completed")
            isSpeaking = false
            _voiceState.value = VoiceState.IDLE
            
            // Start auto-listen if enabled
            if (isAutoListenEnabled) {
                startAutoListen()
            }
        }

        override fun onSpeechCanceled() {
            Log.d(TAG, "🛑 TTS speech canceled")
            isSpeaking = false
            _voiceState.value = VoiceState.IDLE
        }

        override fun onError(error: String) {
            Log.e(TAG, "❌ TTS error: $error")
            isSpeaking = false
            _voiceState.value = VoiceState.ERROR
        }
    }

    /**
     * Process voice command
     */
    private suspend fun processVoiceCommand(command: VoiceCommand) {
        try {
            Log.d(TAG, "🧠 Processing command: ${command.originalText}")
            
            // First try local command processing
            val localResult = localCommandProcessor.processCommand(command)
            
            if (localResult.success) {
                Log.d(TAG, "✅ Local command processed: ${localResult.message}")
                speak(localResult.message, command.language)
                return
            }
            
            // If not a local command, use AI orchestration
            val aiResponse = aiOrchestrationService.processCommand(command)
            
            if (aiResponse.isSuccess) {
                val response = aiResponse.getOrNull()
                if (response != null) {
                    _lastResponse.value = response
                    speak(response.text, command.language)
                    Log.d(TAG, "✅ AI response: ${response.text.take(100)}...")
                } else {
                    speak("I'm sorry, I couldn't process that request.", command.language)
                }
            } else {
                Log.e(TAG, "❌ AI processing failed: ${aiResponse.exceptionOrNull()?.message}")
                speak("I'm sorry, I encountered an error processing your request.", command.language)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing command: ${e.message}", e)
            speak("I'm sorry, I encountered an error.", command.language)
        }
    }

    /**
     * Start auto-listen with 30-second timeout
     */
    private fun startAutoListen() {
        autoListenJob?.cancel()
        autoListenJob = serviceScope.launch {
            delay(1000) // Brief pause before auto-listen
            if (isAutoListenEnabled && !isListening && !isSpeaking) {
                Log.d(TAG, "🔄 Starting auto-listen")
                startListening(currentLanguage)
            }
        }
    }

    /**
     * Set auto-listen enabled state
     */
    private fun setAutoListenEnabled(enabled: Boolean) {
        isAutoListenEnabled = enabled
        Log.d(TAG, "🔄 Auto-listen ${if (enabled) "enabled" else "disabled"}")
        
        if (!enabled) {
            autoListenJob?.cancel()
        }
    }

    /**
     * Create notification for foreground service
     */
    private fun createNotification(status: String): Notification {
        return NotificationCompat.Builder(this, ZaraApplication.VOICE_SERVICE_CHANNEL)
            .setContentTitle("Zara Voice Assistant")
            .setContentText(status)
            .setSmallIcon(R.drawable.ic_mic)
            .setOngoing(true)
            .setSilent(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        
        serviceScope.launch {
            try {
                stopListening()
                stopSpeaking()
                autoListenJob?.cancel()
                
                zaraSTTService.cleanup()
                zaraTTSService.cleanup()
                
                Log.d(TAG, "🧹 Zara Voice Service destroyed")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error during cleanup: ${e.message}", e)
            }
        }
        
        serviceScope.cancel()
    }
}
