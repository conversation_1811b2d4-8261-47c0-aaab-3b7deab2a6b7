package com.zara.assistant.utils;

/**
 * Device Optimization data class
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0018\b\u0086\b\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\n\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\nH\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003JK\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0001J\u0013\u0010!\u001a\u00020\u00072\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\u0005H\u00d6\u0001J\t\u0010$\u001a\u00020\rH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006%"}, d2 = {"Lcom/zara/assistant/utils/DeviceOptimization;", "", "deviceTier", "Lcom/zara/assistant/utils/DeviceTier;", "recommendedConcurrency", "", "enableAnimations", "", "enableAdvancedFeatures", "memoryInfo", "Lcom/zara/assistant/utils/MemoryInfo;", "recommendations", "", "", "(Lcom/zara/assistant/utils/DeviceTier;IZZLcom/zara/assistant/utils/MemoryInfo;Ljava/util/List;)V", "getDeviceTier", "()Lcom/zara/assistant/utils/DeviceTier;", "getEnableAdvancedFeatures", "()Z", "getEnableAnimations", "getMemoryInfo", "()Lcom/zara/assistant/utils/MemoryInfo;", "getRecommendations", "()Ljava/util/List;", "getRecommendedConcurrency", "()I", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class DeviceOptimization {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.DeviceTier deviceTier = null;
    private final int recommendedConcurrency = 0;
    private final boolean enableAnimations = false;
    private final boolean enableAdvancedFeatures = false;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.MemoryInfo memoryInfo = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> recommendations = null;
    
    public DeviceOptimization(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DeviceTier deviceTier, int recommendedConcurrency, boolean enableAnimations, boolean enableAdvancedFeatures, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.MemoryInfo memoryInfo, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> recommendations) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceTier getDeviceTier() {
        return null;
    }
    
    public final int getRecommendedConcurrency() {
        return 0;
    }
    
    public final boolean getEnableAnimations() {
        return false;
    }
    
    public final boolean getEnableAdvancedFeatures() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.MemoryInfo getMemoryInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceTier component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.MemoryInfo component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.DeviceOptimization copy(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DeviceTier deviceTier, int recommendedConcurrency, boolean enableAnimations, boolean enableAdvancedFeatures, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.MemoryInfo memoryInfo, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> recommendations) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}