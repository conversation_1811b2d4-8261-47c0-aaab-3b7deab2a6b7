{"logs": [{"outputFile": "com.zara.assistant.app-mergeDebugResources-87:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\998ccd9a361f25607ee9bdd8ab3141e5\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "60,61,62,63", "startColumns": "4,4,4,4", "startOffsets": "2483,2548,2618,2682", "endColumns": "64,69,63,60", "endOffsets": "2543,2613,2677,2738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1a259f4fef2f815ba319024046901d62\\transformed\\jetified-core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "43,52,54,55,56,57,409,410,411,412,413,414,415,523,1037,1038,1039,1869,1871,2270,2279,2292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1623,1990,2090,2159,2231,2294,23882,23956,24032,24108,24185,24256,24325,30280,64921,65002,65094,117531,117640,145861,146321,147096", "endLines": "43,52,54,55,56,57,409,410,411,412,413,414,415,523,1037,1038,1039,1870,1872,2278,2291,2295", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "1678,2044,2154,2226,2289,2361,23951,24027,24103,24180,24251,24320,24391,30343,64997,65089,65182,117635,117756,146316,147091,147364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3093826e12afe4e65cbc1e4cc9cbb375\\transformed\\jetified-foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "753,754", "startColumns": "4,4", "startOffsets": "46098,46154", "endColumns": "55,54", "endOffsets": "46149,46204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0126a6a18779241fd55cc58603e1228\\transformed\\jetified-activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "493,514", "startColumns": "4,4", "startOffsets": "28669,29756", "endColumns": "41,59", "endOffsets": "28706,29811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5c8c20c5f9f0ed8aec2c62427edbc4af\\transformed\\jetified-lottie-6.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,118,165", "endLines": "2,3,38", "endColumns": "62,46,24", "endOffsets": "113,160,1884"}, "to": {"startLines": "41,488,3339", "startColumns": "4,4,4", "startOffsets": "1500,28406,188916", "endLines": "41,488,3373", "endColumns": "62,46,24", "endOffsets": "1558,28448,190635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76f8ef271629eccbc242979ade06d14f\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "42,75,76,91,92,194,195,331,332,333,334,335,336,337,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,439,440,441,486,487,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,524,571,572,573,574,575,576,577,746,2198,2199,2203,2204,2208,2457,2458,3111,3145,3201,3234,3264,3297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1563,3435,3507,4651,4716,10679,10748,19478,19548,19616,19688,19758,19819,19893,22245,22306,22367,22429,22493,22555,22616,22684,22784,22844,22910,22983,23052,23109,23161,25570,25642,25718,28336,28371,28822,28877,28940,28995,29053,29111,29172,29235,29292,29343,29393,29454,29511,29577,29611,29646,30348,33620,33687,33759,33828,33897,33971,34043,45700,141173,141290,141491,141601,141802,158748,158820,180320,181893,184123,185854,186854,187536", "endLines": "42,75,76,91,92,194,195,331,332,333,334,335,336,337,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,439,440,441,486,487,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,524,571,572,573,574,575,576,577,746,2198,2202,2203,2207,2208,2457,2458,3116,3154,3233,3254,3296,3302", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1618,3502,3590,4711,4777,10743,10806,19543,19611,19683,19753,19814,19888,19961,22301,22362,22424,22488,22550,22611,22679,22779,22839,22905,22978,23047,23104,23156,23218,25637,25713,25778,28366,28401,28872,28935,28990,29048,29106,29167,29230,29287,29338,29388,29449,29506,29572,29606,29641,29676,30413,33682,33754,33823,33892,33966,34038,34126,45766,141285,141486,141596,141797,141926,158815,158882,180518,182189,185849,186530,187531,187698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5d7bcac3a9f64fa9e2de7c8777a96db5\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "566", "startColumns": "4", "startOffsets": "33283", "endColumns": "82", "endOffsets": "33361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\256d85af91a7ef493b14963e8e05452c\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "481,494,517,3255,3260", "startColumns": "4,4,4,4,4", "startOffsets": "28085,28711,29920,186535,186705", "endLines": "481,494,517,3259,3263", "endColumns": "56,64,63,24,24", "endOffsets": "28137,28771,29979,186700,186849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1265bc1e4299577aaef89d15e8083b82\\transformed\\navigation-runtime-2.7.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "489,2563,3567,3570", "startColumns": "4,4,4,4", "startOffsets": "28453,162410,197166,197281", "endLines": "489,2569,3569,3572", "endColumns": "52,24,24,24", "endOffsets": "28501,162709,197276,197391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "34,35,40,53,58,59,64,65,66,67,68,71,72,77,78,79,80,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,115,116,118,119,120,121,122,123,124,125,126,127,128,129,199,200,201,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,226,227,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,348,349,353,354,355,356,357,358,359,425,426,427,428,429,430,431,432,474,475,476,477,483,491,492,495,512,519,520,521,522,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,738,778,779,780,781,782,783,791,792,796,800,804,809,815,822,826,830,835,839,843,847,851,855,859,865,869,875,879,885,889,894,898,901,905,911,915,921,925,931,934,938,942,946,950,954,955,956,957,960,963,966,969,973,974,975,976,977,980,982,984,986,991,992,996,1002,1006,1007,1009,1021,1022,1026,1032,1036,1040,1041,1045,1072,1076,1077,1081,1109,1281,1307,1478,1504,1535,1543,1549,1565,1587,1592,1597,1607,1616,1625,1629,1636,1655,1662,1663,1672,1675,1678,1682,1686,1690,1693,1694,1699,1704,1714,1719,1726,1732,1733,1736,1740,1745,1747,1749,1752,1755,1757,1761,1764,1771,1774,1777,1781,1783,1787,1789,1791,1793,1797,1805,1813,1825,1831,1840,1843,1854,1857,1858,1863,1864,1917,1986,2056,2057,2067,2076,2077,2079,2083,2086,2089,2092,2095,2098,2101,2104,2108,2111,2114,2117,2121,2124,2128,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2164,2166,2167,2168,2169,2170,2171,2172,2173,2175,2176,2178,2179,2181,2183,2184,2186,2187,2188,2189,2190,2191,2193,2194,2195,2196,2197,2209,2211,2213,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2254,2255,2256,2257,2258,2259,2260,2262,2266,2325,2326,2327,2328,2329,2330,2334,2335,2336,2362,2364,2366,2368,2370,2372,2373,2374,2375,2377,2379,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2395,2396,2397,2398,2400,2402,2403,2405,2406,2408,2410,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2425,2426,2427,2428,2430,2431,2432,2433,2434,2436,2438,2440,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2459,2534,2537,2540,2543,2557,2570,2612,2615,2644,2671,2680,2744,3107,3117,3155,3183,3303,3327,3333,3374,3395,3519,3578,3584,3592,3598,3633,3665,3731,3751,3806,3818,3844", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1199,1254,1451,2049,2366,2421,2743,2807,2877,2938,3013,3182,3259,3595,3680,3762,3838,4010,4087,4165,4271,4377,4456,4536,4593,4826,4900,4975,5040,5106,5166,5227,5299,5947,6014,6130,6189,6248,6307,6366,6425,6479,6533,6586,6640,6694,6748,11037,11111,11190,11263,11337,11408,11480,11552,11625,11682,11740,11813,11887,11961,12213,12285,12358,12428,12820,12880,13224,13293,13362,13432,13506,13582,13646,13723,13799,13876,13941,14010,14087,14162,14231,14299,14376,14442,14503,14600,14665,14734,14833,14904,14963,15021,15078,15137,15201,15272,15344,15416,15488,15560,15627,15695,15763,15822,15885,15949,16039,16130,16190,16256,16323,16389,16459,16523,16576,16643,16704,16771,16884,16942,17005,17070,17135,17210,17283,17355,17399,17446,17492,17541,17602,17663,17724,17786,17850,17914,17978,18043,18106,18166,18227,18293,18352,18412,18474,18545,18605,20447,20533,20755,20845,20932,21020,21102,21185,21275,24807,24859,24917,24962,25028,25092,25149,25206,27693,27750,27798,27847,28193,28573,28620,28776,29681,30037,30101,30163,30223,30418,30492,30562,30640,30694,30764,30849,30897,30943,31004,31067,31133,31197,31268,31331,31396,31460,31521,31582,31634,31707,31781,31850,31925,31999,32073,32214,45322,47825,47903,47993,48081,48177,48267,48849,48938,49185,49466,49718,50003,50396,50873,51095,51317,51593,51820,52050,52280,52510,52740,52967,53386,53612,54037,54267,54695,54914,55197,55405,55536,55763,56189,56414,56841,57062,57487,57607,57883,58184,58508,58799,59113,59250,59381,59486,59728,59895,60099,60307,60578,60690,60802,60907,61024,61238,61384,61524,61610,61958,62046,62292,62710,62959,63041,63139,63796,63896,64148,64572,64827,65187,65276,65513,67537,67779,67881,68134,70290,80971,82487,93182,94710,96467,97093,97513,98774,100039,100295,100531,101078,101572,102177,102375,102955,104323,104698,104816,105354,105511,105707,105980,106236,106406,106547,106611,106976,107343,108019,108283,108621,108974,109068,109254,109560,109822,109947,110074,110313,110524,110643,110836,111013,111468,111649,111771,112030,112143,112330,112432,112539,112668,112943,113451,113947,114824,115118,115688,115837,116569,116741,116825,117161,117253,119806,125037,130408,130470,131048,131632,131723,131836,132065,132225,132377,132548,132714,132883,133050,133213,133456,133626,133799,133970,134244,134443,134648,135459,135543,135639,135735,135833,135933,136035,136137,136239,136341,136443,136543,136639,136751,136880,137003,137134,137265,137363,137477,137571,137711,137845,137941,138053,138153,138269,138365,138477,138577,138717,138853,139017,139147,139305,139455,139596,139740,139875,139987,140137,140265,140393,140529,140661,140791,140921,141033,141931,142077,142221,143653,143719,143809,143885,143989,144079,144181,144289,144397,144497,144577,144669,144767,144877,144929,145007,145113,145205,145309,145419,145541,145704,148764,148844,148944,149034,149144,149234,149475,149569,149675,151092,151192,151304,151418,151534,151650,151744,151858,151970,152072,152192,152314,152396,152500,152620,152746,152844,152938,153026,153138,153254,153376,153488,153663,153779,153865,153957,154069,154193,154260,154386,154454,154582,154726,154854,154923,155018,155133,155246,155345,155454,155565,155676,155777,155882,155982,156112,156203,156326,156420,156532,156618,156722,156818,156906,157024,157128,157232,157358,157446,157554,157654,157744,157854,157938,158040,158124,158178,158242,158348,158434,158544,158628,158887,161503,161621,161736,161816,162177,162714,164118,164196,165540,166901,167289,170132,180185,180523,182194,183551,187703,188454,188716,190640,191019,195297,197578,197807,198101,198316,199399,200249,203275,204019,206150,206490,207801", "endLines": "34,35,40,53,58,59,64,65,66,67,68,71,72,77,78,79,80,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,115,116,118,119,120,121,122,123,124,125,126,127,128,129,199,200,201,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,226,227,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,348,349,353,354,355,356,357,358,359,425,426,427,428,429,430,431,432,474,475,476,477,483,491,492,495,512,519,520,521,522,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,738,778,779,780,781,782,790,791,795,799,803,808,814,821,825,829,834,838,842,846,850,854,858,864,868,874,878,884,888,893,897,900,904,910,914,920,924,930,933,937,941,945,949,953,954,955,956,959,962,965,968,972,973,974,975,976,979,981,983,985,990,991,995,1001,1005,1006,1008,1020,1021,1025,1031,1035,1036,1040,1044,1071,1075,1076,1080,1108,1280,1306,1477,1503,1534,1542,1548,1564,1586,1591,1596,1606,1615,1624,1628,1635,1654,1661,1662,1671,1674,1677,1681,1685,1689,1692,1693,1698,1703,1713,1718,1725,1731,1732,1735,1739,1744,1746,1748,1751,1754,1756,1760,1763,1770,1773,1776,1780,1782,1786,1788,1790,1792,1796,1804,1812,1824,1830,1839,1842,1853,1856,1857,1862,1863,1868,1985,2055,2056,2066,2075,2076,2078,2082,2085,2088,2091,2094,2097,2100,2103,2107,2110,2113,2116,2120,2123,2127,2131,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2163,2165,2166,2167,2168,2169,2170,2171,2172,2174,2175,2177,2178,2180,2182,2183,2185,2186,2187,2188,2189,2190,2192,2193,2194,2195,2196,2197,2210,2212,2214,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2253,2254,2255,2256,2257,2258,2259,2261,2265,2269,2325,2326,2327,2328,2329,2333,2334,2335,2336,2363,2365,2367,2369,2371,2372,2373,2374,2376,2378,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2394,2395,2396,2397,2399,2401,2402,2404,2405,2407,2409,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2424,2425,2426,2427,2429,2430,2431,2432,2433,2435,2437,2439,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2533,2536,2539,2542,2556,2562,2579,2614,2643,2670,2679,2743,3106,3110,3144,3182,3200,3326,3332,3338,3394,3518,3538,3583,3587,3597,3632,3644,3730,3750,3805,3817,3843,3850", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1249,1294,1495,2085,2416,2478,2802,2872,2933,3008,3084,3254,3332,3675,3757,3833,3909,4082,4160,4266,4372,4451,4531,4588,4646,4895,4970,5035,5101,5161,5222,5294,5367,6009,6077,6184,6243,6302,6361,6420,6474,6528,6581,6635,6689,6743,6797,11106,11185,11258,11332,11403,11475,11547,11620,11677,11735,11808,11882,11956,12031,12280,12353,12423,12494,12875,12936,13288,13357,13427,13501,13577,13641,13718,13794,13871,13936,14005,14082,14157,14226,14294,14371,14437,14498,14595,14660,14729,14828,14899,14958,15016,15073,15132,15196,15267,15339,15411,15483,15555,15622,15690,15758,15817,15880,15944,16034,16125,16185,16251,16318,16384,16454,16518,16571,16638,16699,16766,16879,16937,17000,17065,17130,17205,17278,17350,17394,17441,17487,17536,17597,17658,17719,17781,17845,17909,17973,18038,18101,18161,18222,18288,18347,18407,18469,18540,18600,18668,20528,20615,20840,20927,21015,21097,21180,21270,21361,24854,24912,24957,25023,25087,25144,25201,25255,27745,27793,27842,27893,28222,28615,28664,28817,29708,30096,30158,30218,30275,30487,30557,30635,30689,30759,30844,30892,30938,30999,31062,31128,31192,31263,31326,31391,31455,31516,31577,31629,31702,31776,31845,31920,31994,32068,32209,32279,45370,47898,47988,48076,48172,48262,48844,48933,49180,49461,49713,49998,50391,50868,51090,51312,51588,51815,52045,52275,52505,52735,52962,53381,53607,54032,54262,54690,54909,55192,55400,55531,55758,56184,56409,56836,57057,57482,57602,57878,58179,58503,58794,59108,59245,59376,59481,59723,59890,60094,60302,60573,60685,60797,60902,61019,61233,61379,61519,61605,61953,62041,62287,62705,62954,63036,63134,63791,63891,64143,64567,64822,64916,65271,65508,67532,67774,67876,68129,70285,80966,82482,93177,94705,96462,97088,97508,98769,100034,100290,100526,101073,101567,102172,102370,102950,104318,104693,104811,105349,105506,105702,105975,106231,106401,106542,106606,106971,107338,108014,108278,108616,108969,109063,109249,109555,109817,109942,110069,110308,110519,110638,110831,111008,111463,111644,111766,112025,112138,112325,112427,112534,112663,112938,113446,113942,114819,115113,115683,115832,116564,116736,116820,117156,117248,117526,125032,130403,130465,131043,131627,131718,131831,132060,132220,132372,132543,132709,132878,133045,133208,133451,133621,133794,133965,134239,134438,134643,134973,135538,135634,135730,135828,135928,136030,136132,136234,136336,136438,136538,136634,136746,136875,136998,137129,137260,137358,137472,137566,137706,137840,137936,138048,138148,138264,138360,138472,138572,138712,138848,139012,139142,139300,139450,139591,139735,139870,139982,140132,140260,140388,140524,140656,140786,140916,141028,141168,142072,142216,142354,143714,143804,143880,143984,144074,144176,144284,144392,144492,144572,144664,144762,144872,144924,145002,145108,145200,145304,145414,145536,145699,145856,148839,148939,149029,149139,149229,149470,149564,149670,149762,151187,151299,151413,151529,151645,151739,151853,151965,152067,152187,152309,152391,152495,152615,152741,152839,152933,153021,153133,153249,153371,153483,153658,153774,153860,153952,154064,154188,154255,154381,154449,154577,154721,154849,154918,155013,155128,155241,155340,155449,155560,155671,155772,155877,155977,156107,156198,156321,156415,156527,156613,156717,156813,156901,157019,157123,157227,157353,157441,157549,157649,157739,157849,157933,158035,158119,158173,158237,158343,158429,158539,158623,158743,161498,161616,161731,161811,162172,162405,163226,164191,165535,166896,167284,170127,180180,180315,181888,183546,184118,188449,188711,188911,191014,195292,195898,197802,197953,198311,199394,199706,203270,204014,206145,206485,207796,207999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a81feca8f62c4cb5733ffcc676aee47\\transformed\\jetified-material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "644,645,646,647,648,649,650,651,652,653,656,657,658,659,660,661,662,663,664,665,666,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "38628,38716,38802,38883,38967,39036,39101,39184,39290,39376,39496,39550,39619,39680,39749,39838,39933,40007,40104,40197,40295,40444,40535,40623,40719,40817,40881,40949,41036,41130,41197,41269,41341,41442,41551,41627,41696,41744,41810,41874,41931,41988,42060,42110,42164,42235,42306,42376,42445,42503,42579,42650,42724,42810,42860,42930", "endLines": "644,645,646,647,648,649,650,651,652,655,656,657,658,659,660,661,662,663,664,665,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "38711,38797,38878,38962,39031,39096,39179,39285,39371,39491,39545,39614,39675,39744,39833,39928,40002,40099,40192,40290,40439,40530,40618,40714,40812,40876,40944,41031,41125,41192,41264,41336,41437,41546,41622,41691,41739,41805,41869,41926,41983,42055,42105,42159,42230,42301,42371,42440,42498,42574,42645,42719,42805,42855,42925,42990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c1ea15a6d1c31f45b2085fa5e508c15\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "516", "startColumns": "4", "startOffsets": "29870", "endColumns": "49", "endOffsets": "29915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\21bb5e18c0eee6b9c7273b75e0822c2a\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2580,2596,2602,3645,3661", "startColumns": "4,4,4,4,4", "startOffsets": "163231,163656,163834,199711,200122", "endLines": "2595,2601,2611,3660,3664", "endColumns": "24,24,24,24,24", "endOffsets": "163651,163829,164113,200117,200244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c1dbd90dee8ec981efff90fba80648e\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,478,479,480,482,484,518,581,582,612,614,620,641,642,704,706,707,709,727,728,739,748,749,751,1873,1876,1879", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25783,25842,25901,25961,26021,26081,26141,26201,26261,26321,26381,26441,26501,26560,26620,26680,26740,26800,26860,26920,26980,27040,27100,27160,27219,27279,27339,27398,27457,27516,27575,27634,27898,27972,28030,28142,28227,29984,34267,34332,36249,36375,36689,38465,38517,42995,43095,43149,43219,44661,44711,45375,45809,45856,45946,117761,117873,117984", "endLines": "442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,478,479,480,482,484,518,581,582,612,614,620,641,642,704,706,707,709,727,728,739,748,749,751,1875,1878,1882", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "25837,25896,25956,26016,26076,26136,26196,26256,26316,26376,26436,26496,26555,26615,26675,26735,26795,26855,26915,26975,27035,27095,27155,27214,27274,27334,27393,27452,27511,27570,27629,27688,27967,28025,28080,28188,28277,30032,34327,34381,36310,36471,36742,38512,38572,43052,43144,43180,43248,44706,44760,45416,45851,45887,46031,117868,117979,118174"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "134,36,49,128,139,123,117,64,72,77,94,98,103,83,89,3,28,56,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5647,1646,2272,5417,5826,5265,5058,2888,3232,3405,4102,4240,4452,3637,3895,84,1313,2542,4760", "endLines": "136,46,53,131,142,125,120,69,75,80,96,101,107,87,92,25,33,61,114", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "5778,2230,2498,5609,5990,5385,5233,3192,3399,3606,4234,4446,4724,3889,4096,1274,1602,2849,5026"}, "to": {"startLines": "1883,1886,1897,1902,1906,1910,1913,2132,2138,2215,2219,2222,2226,2231,2236,2296,2319,2337,2343", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "118179,118315,118904,119135,119332,119501,119626,134978,135287,142359,142565,142702,142913,143190,143447,147369,148470,149767,150079", "endLines": "1885,1896,1901,1905,1909,1912,1916,2137,2141,2218,2221,2225,2230,2235,2239,2318,2324,2342,2347", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "118310,118899,119130,119327,119496,119621,119801,135282,135454,142560,142697,142908,143185,143442,143648,148465,148759,150074,150345"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "44,87,88,43,53,81,80,79,76,78,77,54,41,59,4,3,50,150,162,62,169,125,121,117,115,132,137,120,138,136,127,133,126,116,122,131,114,113,130,107,108,106,110,105,109,173,57,167,84,165,160,151,158,170,157,101,98,96,97,102,99,100,63,159,9,171,161,154,27,26,35,34,25,24,29,28,31,30,33,32,172,152,42,10,164,144,142,145,146,143,141,168,163,38,166,174,11,49,48,175,8,153,149,58,21,16,73,18,17,19,93,39,20,12,67,66,13,47,70,68,69,92,91,40,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2661,4511,4715,2596,3016,4308,4247,4178,3998,4108,4050,3074,2491,3302,119,77,2908,8088,8603,3388,8883,6830,6658,6488,6347,7218,7437,6597,7493,7379,6974,7276,6899,6415,6721,7160,6273,6211,7090,5921,5978,5854,6104,5791,6043,9045,3166,8805,4411,8723,8531,8138,8447,8923,8405,5577,5322,5122,5231,5670,5417,5496,3442,8491,390,8961,8569,8309,1454,1373,2199,2129,1249,1175,1663,1585,1846,1775,2020,1956,8999,8189,2543,441,8683,7809,7655,7870,7936,7741,7581,8843,8645,2330,8765,9096,494,2854,2802,9134,336,8247,8044,3236,1077,722,3903,852,777,930,5012,2376,1006,543,3605,3531,615,2730,3803,3681,3739,4913,4843,2434,232", "endColumns": "39,203,95,64,57,56,60,68,51,69,57,60,51,55,79,41,81,49,41,53,39,68,62,77,67,57,55,60,59,57,76,61,74,72,67,57,73,61,69,56,64,66,64,62,60,50,69,37,59,41,37,50,43,37,41,92,94,108,90,88,78,80,51,39,50,37,33,69,130,80,103,69,123,73,111,77,109,70,108,63,45,57,52,52,39,60,85,65,83,67,73,39,37,45,39,37,48,53,51,37,53,61,43,65,67,54,57,77,74,75,84,57,70,71,75,73,75,71,59,57,63,98,69,56,103", "endOffsets": "2696,4710,4806,2656,3069,4360,4303,4242,4045,4173,4103,3130,2538,3353,194,114,2985,8133,8640,3437,8918,6894,6716,6561,6410,7271,7488,6653,7548,7432,7046,7333,6969,6483,6784,7213,6342,6268,7155,5973,6038,5916,6164,5849,6099,9091,3231,8838,4466,8760,8564,8184,8486,8956,8442,5665,5412,5226,5317,5754,5491,5572,3489,8526,436,8994,8598,8374,1580,1449,2298,2194,1368,1244,1770,1658,1951,1841,2124,2015,9040,8242,2591,489,8718,7865,7736,7931,8015,7804,7650,8878,8678,2371,8800,9129,538,2903,2849,9167,385,8304,8083,3297,1140,772,3956,925,847,1001,5092,2429,1072,610,3676,3600,686,2797,3858,3734,3798,5007,4908,2486,331"}, "to": {"startLines": "552,553,554,555,556,557,558,559,560,561,562,563,564,565,567,568,569,570,578,579,580,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,608,609,610,611,615,616,617,618,619,621,622,623,624,625,626,627,628,629,640,643,705,708,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,729,730,731,732,733,734,735,736,737,740,741,742,743,744,745,747,750,752,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32284,32324,32528,32624,32689,32747,32804,32865,32934,32986,33056,33114,33175,33227,33366,33446,33488,33570,34131,34173,34227,34386,34455,34518,34596,34664,34722,34778,34839,34899,34957,35034,35096,35171,35244,35312,35370,35444,35506,35576,35633,35698,35765,35830,35893,36030,36081,36151,36189,36476,36518,36556,36607,36651,36747,36789,36882,36977,37086,37177,37266,37345,37426,38425,38577,43057,43185,43253,43323,43454,43535,43639,43709,43833,43907,44019,44097,44207,44278,44387,44451,44497,44555,44608,44765,44805,44866,44952,45018,45102,45170,45244,45284,45421,45467,45507,45545,45594,45648,45771,45892,46036,46209,46253,46319,46387,46442,46500,46578,46653,46729,46814,46872,46943,47015,47091,47165,47241,47313,47373,47431,47495,47594,47664,47721", "endColumns": "39,203,95,64,57,56,60,68,51,69,57,60,51,55,79,41,81,49,41,53,39,68,62,77,67,57,55,60,59,57,76,61,74,72,67,57,73,61,69,56,64,66,64,62,60,50,69,37,59,41,37,50,43,37,41,92,94,108,90,88,78,80,51,39,50,37,33,69,130,80,103,69,123,73,111,77,109,70,108,63,45,57,52,52,39,60,85,65,83,67,73,39,37,45,39,37,48,53,51,37,53,61,43,65,67,54,57,77,74,75,84,57,70,71,75,73,75,71,59,57,63,98,69,56,103", "endOffsets": "32319,32523,32619,32684,32742,32799,32860,32929,32981,33051,33109,33170,33222,33278,33441,33483,33565,33615,34168,34222,34262,34450,34513,34591,34659,34717,34773,34834,34894,34952,35029,35091,35166,35239,35307,35365,35439,35501,35571,35628,35693,35760,35825,35888,35949,36076,36146,36184,36244,36513,36551,36602,36646,36684,36784,36877,36972,37081,37172,37261,37340,37421,37473,38460,38623,43090,43214,43318,43449,43530,43634,43704,43828,43902,44014,44092,44202,44273,44382,44446,44492,44550,44603,44656,44800,44861,44947,45013,45097,45165,45239,45279,45317,45462,45502,45540,45589,45643,45695,45804,45941,46093,46248,46314,46382,46437,46495,46573,46648,46724,46809,46867,46938,47010,47086,47160,47236,47308,47368,47426,47490,47589,47659,47716,47820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9bdc921d1f4d0055fa05a172e1713bcb\\transformed\\jetified-camera-view-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "36,44,3588", "startColumns": "4,4,4", "startOffsets": "1299,1683,197958", "endLines": "39,51,3591", "endColumns": "11,11,24", "endOffsets": "1446,1985,198096"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "107,110,76,75,80,79,92,93,52,51,53,54,46,45,47,48,27,26,30,25,28,24,29,87,88,89,84,83,70,69,68,71,67,72,118,119,13,11,14,12,106,115,34,33,37,35,36,20,19,18,21,17,111,98,96,97,6,5,4,7,3,8,114,63,60,59,58,64,61,57,62,41,40,42,103,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3785,3866,2755,2709,2884,2835,3226,3284,1846,1801,1898,1955,1642,1597,1692,1735,930,882,1075,835,978,788,1026,3056,3108,3153,2993,2953,2554,2510,2466,2598,2422,2642,4119,4167,445,349,488,398,3737,4033,1207,1161,1346,1256,1301,676,634,593,718,552,3914,3469,3364,3416,200,158,117,242,76,284,3984,2300,2167,2123,2079,2350,2211,2035,2255,1473,1423,1527,3650,3553,3601", "endColumns": "56,47,47,45,50,48,57,56,51,44,56,54,49,44,42,43,47,47,50,46,47,46,48,51,44,45,41,39,43,43,43,43,43,44,47,48,42,48,41,46,47,52,48,45,49,44,44,41,41,40,41,40,44,49,51,52,41,41,40,41,40,42,48,49,43,43,43,46,43,43,44,53,49,49,58,47,48", "endOffsets": "3837,3909,2798,2750,2930,2879,3279,3336,1893,1841,1950,2005,1687,1637,1730,1774,973,925,1121,877,1021,830,1070,3103,3148,3194,3030,2988,2593,2549,2505,2637,2461,2682,4162,4211,483,393,525,440,3780,4081,1251,1202,1391,1296,1341,713,671,629,755,588,3954,3514,3411,3464,237,195,153,279,112,322,4028,2345,2206,2162,2118,2392,2250,2074,2295,1522,1468,1572,3704,3596,3645"}, "to": {"startLines": "315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,338,339,340,341,342,343,344,345,346,347,350,351,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,416,417,418,419,420,421,422,423,424,433,434,435,436,437,438", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18673,18730,18778,18826,18872,18923,18972,19030,19087,19139,19184,19241,19296,19346,19391,19434,19966,20014,20062,20113,20160,20208,20255,20304,20356,20401,20620,20662,21366,21410,21454,21498,21542,21586,21631,21679,21728,21771,21820,21862,21909,21957,22010,22059,22105,22155,22200,23223,23265,23307,23348,23390,23431,23476,23526,23578,23631,23673,23715,23756,23798,23839,24396,24445,24495,24539,24583,24627,24674,24718,24762,25260,25314,25364,25414,25473,25521", "endColumns": "56,47,47,45,50,48,57,56,51,44,56,54,49,44,42,43,47,47,50,46,47,46,48,51,44,45,41,39,43,43,43,43,43,44,47,48,42,48,41,46,47,52,48,45,49,44,44,41,41,40,41,40,44,49,51,52,41,41,40,41,40,42,48,49,43,43,43,46,43,43,44,53,49,49,58,47,48", "endOffsets": "18725,18773,18821,18867,18918,18967,19025,19082,19134,19179,19236,19291,19341,19386,19429,19473,20009,20057,20108,20155,20203,20250,20299,20351,20396,20442,20657,20697,21405,21449,21493,21537,21581,21626,21674,21723,21766,21815,21857,21904,21952,22005,22054,22100,22150,22195,22240,23260,23302,23343,23385,23426,23471,23521,23573,23626,23668,23710,23751,23793,23834,23877,24440,24490,24534,24578,24622,24669,24713,24757,24802,25309,25359,25409,25468,25516,25565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\52be0562697f63c1f60f36d041cf7d07\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "485,490", "startColumns": "4,4", "startOffsets": "28282,28506", "endColumns": "53,66", "endOffsets": "28331,28568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10f8b8bca7dd0586b8f58168a92d3396\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,81,352,607,613,630,631,632,633,634,635,636,637,638,639", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,3914,20702,35954,36315,37478,37567,37666,37774,37871,37959,38059,38129,38226,38336", "endLines": "5,7,10,14,33,81,352,607,613,630,631,632,633,634,635,636,637,638,639", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,3965,20750,36025,36370,37562,37661,37769,37866,37954,38054,38124,38221,38331,38420"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "66,67,64,65,81,3,78,77,76,126,127,128,129,125,130,131,132,133,134,5,102,98,99,109,111,110,103,100,101,87,89,91,93,105,107,95,97,108,114,86,88,115,90,92,112,104,113,106,94,96,28,23,25,29,24,26,9,11,14,16,31,33,19,21,35,36,8,10,13,15,30,32,18,20,39,40,50,49,44,43,51,52,46,45,120,121,122,4,118,119,59,55,60,56,61,57,83,73,70,71,72,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2941,2988,2843,2892,3484,89,3405,3355,3306,5914,5957,6000,6043,5872,6086,6129,6172,6215,6258,178,4606,4368,4422,5024,5152,5089,4665,4485,4541,3674,3797,3922,4049,4782,4903,4175,4300,4968,5330,3618,3732,5393,3864,3982,5215,4726,5270,4840,4118,4234,1285,1042,1154,1345,1097,1218,334,459,587,716,1464,1587,845,972,1654,1711,277,393,528,648,1407,1523,787,905,1817,1868,2285,2233,2019,1966,2340,2393,2129,2075,5621,5700,5783,133,5489,5553,2652,2485,2704,2538,2758,2593,3564,3227,3077,3127,3178,3524", "endColumns": "46,45,48,48,39,43,46,49,48,42,42,42,42,41,42,42,42,42,42,47,58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,54,55,59,62,56,65,59,54,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,50,55,54,51,55,52,52,61,62,53,78,82,63,44,63,67,51,52,53,54,52,53,47,44,49,50,48,39", "endOffsets": "2983,3029,2887,2936,3519,128,3447,3400,3350,5952,5995,6038,6081,5909,6124,6167,6210,6253,6296,221,4660,4417,4480,5084,5210,5147,4721,4536,4601,3727,3859,3977,4113,4835,4963,4229,4363,5019,5388,3669,3792,5442,3917,4044,5265,4777,5325,4898,4170,4295,1340,1092,1213,1402,1149,1279,388,522,643,781,1518,1648,900,1036,1706,1770,329,454,582,711,1459,1582,840,967,1863,1919,2335,2280,2070,2014,2388,2450,2187,2124,5695,5778,5842,173,5548,5616,2699,2533,2753,2588,2806,2642,3607,3267,3122,3173,3222,3559"}, "to": {"startLines": "69,70,73,74,82,93,102,103,104,105,106,107,108,109,110,111,112,113,114,117,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,196,197,198,213,214,215,220,221,222,223,224,225,228,229,230,231,232,233", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3089,3136,3337,3386,3970,4782,5372,5419,5469,5518,5561,5604,5647,5690,5732,5775,5818,5861,5904,6082,6802,6861,6915,6978,7043,7106,7169,7230,7286,7351,7409,7476,7536,7605,7663,7728,7787,7855,7911,7974,8030,8095,8149,8207,8274,8329,8385,8445,8508,8565,8631,8691,8746,8810,8872,8929,8995,9054,9122,9183,9253,9312,9378,9438,9507,9564,9628,9685,9751,9810,9878,9935,9999,10057,10124,10175,10231,10286,10338,10394,10447,10500,10562,10625,10811,10890,10973,12036,12081,12145,12499,12551,12604,12658,12713,12766,12941,12989,13034,13084,13135,13184", "endColumns": "46,45,48,48,39,43,46,49,48,42,42,42,42,41,42,42,42,42,42,47,58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,54,55,59,62,56,65,59,54,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,50,55,54,51,55,52,52,61,62,53,78,82,63,44,63,67,51,52,53,54,52,53,47,44,49,50,48,39", "endOffsets": "3131,3177,3381,3430,4005,4821,5414,5464,5513,5556,5599,5642,5685,5727,5770,5813,5856,5899,5942,6125,6856,6910,6973,7038,7101,7164,7225,7281,7346,7404,7471,7531,7600,7658,7723,7782,7850,7906,7969,8025,8090,8144,8202,8269,8324,8380,8440,8503,8560,8626,8686,8741,8805,8867,8924,8990,9049,9117,9178,9248,9307,9373,9433,9502,9559,9623,9680,9746,9805,9873,9930,9994,10052,10119,10170,10226,10281,10333,10389,10442,10495,10557,10620,10674,10885,10968,11032,12076,12140,12208,12546,12599,12653,12708,12761,12815,12984,13029,13079,13130,13179,13219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e100d358aff62c4f57607391f1e38263\\transformed\\navigation-common-2.7.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3539,3552,3558,3564,3573", "startColumns": "4,4,4,4,4", "startOffsets": "195903,196542,196786,197033,197396", "endLines": "3551,3557,3563,3566,3577", "endColumns": "24,24,24,24,24", "endOffsets": "196537,196781,197028,197161,197573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\665c7e8b6f514c6f99b6e4f872771716\\transformed\\jetified-core-1.5.0\\res\\values\\values.xml", "from": {"startLines": "4,11", "startColumns": "0,0", "startOffsets": "176,544", "endLines": "10,17", "endColumns": "8,8", "endOffsets": "543,909"}, "to": {"startLines": "2348,2355", "startColumns": "4,4", "startOffsets": "150350,150722", "endLines": "2354,2361", "endColumns": "8,8", "endOffsets": "150717,151087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5d1db041e9eb6e82b657a563f0ccac0d\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "513", "startColumns": "4", "startOffsets": "29713", "endColumns": "42", "endOffsets": "29751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfc3be0edb864b95a6a8fbfc9896f93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "515", "startColumns": "4", "startOffsets": "29816", "endColumns": "53", "endOffsets": "29865"}}]}]}