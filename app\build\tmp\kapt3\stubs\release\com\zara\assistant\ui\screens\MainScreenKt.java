package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\u001a.\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001a \u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a&\u0010\u000f\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0011H\u0003\u001a4\u0010\u0013\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H\u0003\u001a\u001a\u0010\u001a\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001b"}, d2 = {"FloatingParticles", "", "modifier", "Landroidx/compose/ui/Modifier;", "particleColor", "Landroidx/compose/ui/graphics/Color;", "particleCount", "", "FloatingParticles-bw27NRU", "(Landroidx/compose/ui/Modifier;JI)V", "MainScreen", "onNavigateToSettings", "Lkotlin/Function0;", "viewModel", "Lcom/zara/assistant/ui/viewmodel/MainViewModel;", "QuickActionsSection", "onAction", "Lkotlin/Function1;", "", "StatusSection", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "lastResponse", "isListening", "", "isSpeaking", "WelcomeSection", "app_release"})
public final class MainScreenKt {
    
    /**
     * Mind-Blowing Main Screen - The stunning centerpiece of Zara
     * Features:
     * - Gorgeous gradient backgrounds
     * - Floating neumorphism cards
     * - Smooth animations
     * - Interactive voice button
     * - Real-time status updates
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.ui.viewmodel.MainViewModel viewModel) {
    }
    
    /**
     * Welcome Section with dynamic messaging
     */
    @androidx.compose.runtime.Composable()
    private static final void WelcomeSection(androidx.compose.ui.Modifier modifier, com.zara.assistant.domain.model.VoiceState voiceState) {
    }
    
    /**
     * Status Section showing current state and responses
     */
    @androidx.compose.runtime.Composable()
    private static final void StatusSection(androidx.compose.ui.Modifier modifier, com.zara.assistant.domain.model.VoiceState voiceState, java.lang.String lastResponse, boolean isListening, boolean isSpeaking) {
    }
    
    /**
     * Quick Actions Section
     */
    @androidx.compose.runtime.Composable()
    private static final void QuickActionsSection(androidx.compose.ui.Modifier modifier, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAction) {
    }
}