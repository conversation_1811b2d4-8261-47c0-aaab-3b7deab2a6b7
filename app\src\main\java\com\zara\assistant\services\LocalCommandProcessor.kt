package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.*
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Local Command Processor - Ultra-fast local processing for system commands
 * Handles device controls, app management, and system actions without AI
 */
@Singleton
class LocalCommandProcessor @Inject constructor(
    @ApplicationContext private val context: Context,
    private val systemControlManager: SystemControlManager,
    private val performanceUtils: PerformanceUtils
) {
    
    companion object {
        private const val TAG = "LocalCommandProcessor"
    }
    
    // Command patterns for different languages
    private val englishPatterns = mapOf(
        // WiFi controls
        "turn on wifi" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "on")),
        "enable wifi" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "on")),
        "wifi on" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "on")),
        "turn off wifi" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "off")),
        "disable wifi" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "off")),
        "wifi off" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "off")),
        
        // Bluetooth controls
        "turn on bluetooth" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "on")),
        "enable bluetooth" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "on")),
        "bluetooth on" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "on")),
        "turn off bluetooth" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "off")),
        "disable bluetooth" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "off")),
        "bluetooth off" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "off")),
        
        // Mobile data controls
        "turn on mobile data" to Pair(CommandIntent.TOGGLE_MOBILE_DATA, mapOf("state" to "on")),
        "enable mobile data" to Pair(CommandIntent.TOGGLE_MOBILE_DATA, mapOf("state" to "on")),
        "turn off mobile data" to Pair(CommandIntent.TOGGLE_MOBILE_DATA, mapOf("state" to "off")),
        "disable mobile data" to Pair(CommandIntent.TOGGLE_MOBILE_DATA, mapOf("state" to "off")),
        
        // Do Not Disturb
        "turn on do not disturb" to Pair(CommandIntent.TOGGLE_DND, mapOf("state" to "on")),
        "enable do not disturb" to Pair(CommandIntent.TOGGLE_DND, mapOf("state" to "on")),
        "turn off do not disturb" to Pair(CommandIntent.TOGGLE_DND, mapOf("state" to "off")),
        "disable do not disturb" to Pair(CommandIntent.TOGGLE_DND, mapOf("state" to "off")),
        
        // Flashlight
        "turn on flashlight" to Pair(CommandIntent.TOGGLE_FLASHLIGHT, mapOf("state" to "on")),
        "flashlight on" to Pair(CommandIntent.TOGGLE_FLASHLIGHT, mapOf("state" to "on")),
        "turn off flashlight" to Pair(CommandIntent.TOGGLE_FLASHLIGHT, mapOf("state" to "off")),
        "flashlight off" to Pair(CommandIntent.TOGGLE_FLASHLIGHT, mapOf("state" to "off")),
        
        // System actions
        "go back" to Pair(CommandIntent.GO_BACK, emptyMap()),
        "go home" to Pair(CommandIntent.GO_HOME, emptyMap()),
        "show recent apps" to Pair(CommandIntent.SHOW_RECENT_APPS, emptyMap()),
        "show notifications" to Pair(CommandIntent.SHOW_NOTIFICATIONS, emptyMap()),
        "take screenshot" to Pair(CommandIntent.TAKE_SCREENSHOT, emptyMap()),
        "lock screen" to Pair(CommandIntent.LOCK_SCREEN, emptyMap()),
        
        // App controls
        "open settings" to Pair(CommandIntent.OPEN_APP, mapOf("package" to "com.android.settings")),
        "open camera" to Pair(CommandIntent.OPEN_APP, mapOf("package" to "com.android.camera2")),
        "open calculator" to Pair(CommandIntent.OPEN_APP, mapOf("package" to "com.android.calculator2")),
        "open phone" to Pair(CommandIntent.OPEN_APP, mapOf("package" to "com.android.dialer")),
        "open messages" to Pair(CommandIntent.OPEN_APP, mapOf("package" to "com.android.messaging"))
    )
    
    private val hindiPatterns = mapOf(
        // WiFi controls (Hindi)
        "वाईफाई चालू करो" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "on")),
        "वाईफाई बंद करो" to Pair(CommandIntent.TOGGLE_WIFI, mapOf("state" to "off")),
        
        // Bluetooth controls (Hindi)
        "ब्लूटूथ चालू करो" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "on")),
        "ब्लूटूथ बंद करो" to Pair(CommandIntent.TOGGLE_BLUETOOTH, mapOf("state" to "off")),
        
        // System actions (Hindi)
        "वापस जाओ" to Pair(CommandIntent.GO_BACK, emptyMap()),
        "होम जाओ" to Pair(CommandIntent.GO_HOME, emptyMap()),
        "स्क्रीनशॉट लो" to Pair(CommandIntent.TAKE_SCREENSHOT, emptyMap())
    )

    /**
     * Process voice command locally
     */
    suspend fun processCommand(command: VoiceCommand): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("LocalCommand_Process")
            
            Log.d(TAG, "🧠 Processing local command: ${command.originalText}")
            
            val normalizedText = command.originalText.lowercase(Locale.getDefault()).trim()
            
            // Try to match command patterns
            val matchResult = matchCommandPattern(normalizedText, command.language)
            
            if (matchResult != null) {
                val (intent, parameters) = matchResult
                
                // Execute the command
                val result = executeCommand(intent, parameters)
                
                performanceUtils.endMeasurement(measurementId)
                
                Log.d(TAG, "✅ Local command executed: ${result.message}")
                return@withContext result
                
            } else {
                // Check for dynamic patterns (volume, brightness)
                val dynamicResult = processDynamicCommands(normalizedText)
                
                performanceUtils.endMeasurement(measurementId)
                
                if (dynamicResult != null) {
                    Log.d(TAG, "✅ Dynamic command executed: ${dynamicResult.message}")
                    return@withContext dynamicResult
                }
                
                Log.d(TAG, "❌ No local command match found")
                return@withContext SystemControlResult(
                    success = false,
                    action = "unknown",
                    message = "Command not recognized as local system command"
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing local command: ${e.message}", e)
            return@withContext SystemControlResult(
                success = false,
                action = "error",
                message = "Error processing command: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Match command pattern based on language
     */
    private fun matchCommandPattern(text: String, language: String): Pair<CommandIntent, Map<String, String>>? {
        val patterns = when (language) {
            Constants.Voice.LANGUAGE_HINDI -> hindiPatterns + englishPatterns // Support both
            else -> englishPatterns
        }
        
        // Direct match first
        patterns[text]?.let { return it }
        
        // Fuzzy matching for partial matches
        for ((pattern, result) in patterns) {
            if (text.contains(pattern) || pattern.contains(text)) {
                return result
            }
        }
        
        return null
    }

    /**
     * Process dynamic commands (volume, brightness with values)
     */
    private suspend fun processDynamicCommands(text: String): SystemControlResult? {
        // Volume controls
        when {
            text.contains("volume") -> {
                val volumeValue = extractNumber(text)
                return if (volumeValue != null) {
                    executeCommand(CommandIntent.SET_VOLUME, mapOf("value" to volumeValue.toString()))
                } else {
                    SystemControlResult(false, "volume", "Please specify volume level (0-100)")
                }
            }
            
            text.contains("brightness") -> {
                val brightnessValue = extractNumber(text)
                return if (brightnessValue != null) {
                    executeCommand(CommandIntent.SET_BRIGHTNESS, mapOf("value" to brightnessValue.toString()))
                } else {
                    SystemControlResult(false, "brightness", "Please specify brightness level (0-100)")
                }
            }
            
            // App opening with dynamic names
            text.startsWith("open ") -> {
                val appName = text.removePrefix("open ").trim()
                return executeCommand(CommandIntent.OPEN_APP, mapOf("name" to appName))
            }
            
            text.startsWith("close ") -> {
                val appName = text.removePrefix("close ").trim()
                return executeCommand(CommandIntent.CLOSE_APP, mapOf("name" to appName))
            }
        }
        
        return null
    }

    /**
     * Extract number from text
     */
    private fun extractNumber(text: String): Int? {
        val regex = Regex("\\d+")
        val match = regex.find(text)
        return match?.value?.toIntOrNull()
    }

    /**
     * Execute system command
     */
    private suspend fun executeCommand(intent: CommandIntent, parameters: Map<String, String>): SystemControlResult {
        return try {
            when (intent) {
                CommandIntent.TOGGLE_WIFI -> {
                    val state = parameters["state"] == "on"
                    systemControlManager.setWifiEnabled(state)
                }
                
                CommandIntent.TOGGLE_BLUETOOTH -> {
                    val state = parameters["state"] == "on"
                    systemControlManager.setBluetoothEnabled(state)
                }
                
                CommandIntent.TOGGLE_MOBILE_DATA -> {
                    val state = parameters["state"] == "on"
                    systemControlManager.setMobileDataEnabled(state)
                }
                
                CommandIntent.TOGGLE_DND -> {
                    val state = parameters["state"] == "on"
                    systemControlManager.setDoNotDisturbEnabled(state)
                }
                
                CommandIntent.TOGGLE_FLASHLIGHT -> {
                    val state = parameters["state"] == "on"
                    systemControlManager.setFlashlightEnabled(state)
                }
                
                CommandIntent.SET_VOLUME -> {
                    val value = parameters["value"]?.toIntOrNull() ?: 50
                    systemControlManager.setVolume(value)
                }
                
                CommandIntent.SET_BRIGHTNESS -> {
                    val value = parameters["value"]?.toIntOrNull() ?: 50
                    systemControlManager.setBrightness(value)
                }
                
                CommandIntent.GO_BACK -> {
                    systemControlManager.performGlobalAction(android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_BACK)
                }
                
                CommandIntent.GO_HOME -> {
                    systemControlManager.performGlobalAction(android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_HOME)
                }
                
                CommandIntent.SHOW_RECENT_APPS -> {
                    systemControlManager.performGlobalAction(android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_RECENTS)
                }
                
                CommandIntent.SHOW_NOTIFICATIONS -> {
                    systemControlManager.performGlobalAction(android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS)
                }
                
                CommandIntent.TAKE_SCREENSHOT -> {
                    systemControlManager.takeScreenshot()
                }
                
                CommandIntent.LOCK_SCREEN -> {
                    systemControlManager.lockScreen()
                }
                
                CommandIntent.OPEN_APP -> {
                    val packageName = parameters["package"]
                    val appName = parameters["name"]
                    
                    if (packageName != null) {
                        systemControlManager.openApp(packageName)
                    } else if (appName != null) {
                        systemControlManager.openAppByName(appName)
                    } else {
                        SystemControlResult(false, "open_app", "App not specified")
                    }
                }
                
                CommandIntent.CLOSE_APP -> {
                    val appName = parameters["name"]
                    if (appName != null) {
                        systemControlManager.closeAppByName(appName)
                    } else {
                        SystemControlResult(false, "close_app", "App not specified")
                    }
                }
                
                else -> {
                    SystemControlResult(false, "unknown", "Command not implemented")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error executing command: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = intent.name,
                message = "Failed to execute command: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Check if command can be processed locally
     */
    fun canProcessLocally(text: String, language: String): Boolean {
        val normalizedText = text.lowercase(Locale.getDefault()).trim()
        
        // Check static patterns
        val patterns = when (language) {
            Constants.Voice.LANGUAGE_HINDI -> hindiPatterns + englishPatterns
            else -> englishPatterns
        }
        
        if (patterns.containsKey(normalizedText)) {
            return true
        }
        
        // Check dynamic patterns
        return normalizedText.contains("volume") ||
                normalizedText.contains("brightness") ||
                normalizedText.startsWith("open ") ||
                normalizedText.startsWith("close ")
    }

    /**
     * Get supported local commands
     */
    fun getSupportedCommands(): List<String> {
        return englishPatterns.keys.toList() + hindiPatterns.keys.toList()
    }
}
