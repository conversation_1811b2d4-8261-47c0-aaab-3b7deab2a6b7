package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.zara.assistant.ZaraApplication",
    rootPackage = "com.zara.assistant",
    originatingRoot = "com.zara.assistant.ZaraApplication",
    originatingRootPackage = "com.zara.assistant",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ZaraApplication",
    originatingRootSimpleNames = "ZaraApplication"
)
public class _com_zara_assistant_ZaraApplication {
}
