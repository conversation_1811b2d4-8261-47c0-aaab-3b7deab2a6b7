package com.zara.assistant.presentation.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\u001a<\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a.\u0010\f\u001a\u00020\u00012\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0011\u0010\u0012\u001a,\u0010\u0013\u001a\u00020\u00012\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u000eH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0015\u0010\u0016\u001aN\u0010\u0017\u001a\u00020\u0001*\u00020\u00182\u0006\u0010\b\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u001e\u001a\u00020\u000bH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001f\u0010 \u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006!"}, d2 = {"MindBlowingVoiceButton", "", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "size", "", "enabled", "", "ParticleEffect", "particleColor", "Landroidx/compose/ui/graphics/Color;", "particleCount", "", "ParticleEffect-bw27NRU", "(Landroidx/compose/ui/Modifier;JI)V", "VoiceWaveOverlay", "color", "VoiceWaveOverlay-mxwnekA", "(Landroidx/compose/ui/Modifier;Lcom/zara/assistant/domain/model/VoiceState;J)V", "drawMindBlowingButton", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "Landroidx/compose/ui/geometry/Size;", "buttonColor", "shadowColor", "highlightColor", "glowIntensity", "isPressed", "drawMindBlowingButton-w8FEoQs", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;JJJJFLcom/zara/assistant/domain/model/VoiceState;Z)V", "app_debug"})
public final class VoiceButtonKt {
    
    /**
     * Mind-Blowing Voice Button - The centerpiece of Zara's interface
     * Features:
     * - Stunning neumorphism design
     * - Real-time voice visualization
     * - Particle effects
     * - Smooth animations
     * - Haptic feedback
     */
    @androidx.compose.runtime.Composable()
    public static final void MindBlowingVoiceButton(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState voiceState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, float size, boolean enabled) {
    }
}