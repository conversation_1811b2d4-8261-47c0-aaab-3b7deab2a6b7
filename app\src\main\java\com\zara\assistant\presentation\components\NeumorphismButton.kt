package com.zara.assistant.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.theme.neumorphismColors

/**
 * Neumorphism Button - Beautiful soft UI button with depth effect
 */
@Composable
fun NeumorphismButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    shape: Shape = RoundedCornerShape(Constants.UI.NEUMORPHISM_CORNER_RADIUS.dp),
    elevation: Dp = Constants.UI.NEUMORPHISM_ELEVATION.dp,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable BoxScope.() -> Unit
) {
    val isPressed by interactionSource.collectIsPressedAsState()
    val neumorphismColors = MaterialTheme.neumorphismColors
    
    // Animation for press state
    val animatedElevation by animateDpAsState(
        targetValue = if (isPressed) elevation * 0.3f else elevation,
        animationSpec = tween(
            durationMillis = Constants.UI.ANIMATION_DURATION_SHORT.toInt(),
            easing = FastOutSlowInEasing
        ),
        label = "elevation"
    )
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(
            durationMillis = Constants.UI.ANIMATION_DURATION_SHORT.toInt(),
            easing = FastOutSlowInEasing
        ),
        label = "scale"
    )
    
    Box(
        modifier = modifier
            .graphicsLayer {
                scaleX = animatedScale
                scaleY = animatedScale
            }
            .neumorphismShadow(
                shape = shape,
                elevation = animatedElevation,
                lightShadowColor = neumorphismColors.lightShadow,
                darkShadowColor = neumorphismColors.darkShadow
            )
            .clip(shape)
            .background(
                color = if (isPressed) neumorphismColors.pressed else neumorphismColors.surface,
                shape = shape
            )
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled,
                onClick = onClick
            ),
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}

/**
 * Circular Neumorphism Button - Perfect for voice button
 */
@Composable
fun CircularNeumorphismButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Dp = 80.dp,
    enabled: Boolean = true,
    elevation: Dp = Constants.UI.NEUMORPHISM_ELEVATION.dp,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable BoxScope.() -> Unit
) {
    NeumorphismButton(
        onClick = onClick,
        modifier = modifier.size(size),
        enabled = enabled,
        shape = CircleShape,
        elevation = elevation,
        interactionSource = interactionSource,
        content = content
    )
}

/**
 * Neumorphism modifier for shadow effect
 */
fun Modifier.neumorphismShadow(
    shape: Shape,
    elevation: Dp,
    lightShadowColor: Color,
    darkShadowColor: Color
): Modifier = this.then(
    Modifier.drawBehind {
        drawNeumorphismShadow(
            shape = shape,
            elevation = elevation,
            lightShadowColor = lightShadowColor,
            darkShadowColor = darkShadowColor
        )
    }
)

/**
 * Draw neumorphism shadow effect
 */
private fun DrawScope.drawNeumorphismShadow(
    shape: Shape,
    elevation: Dp,
    lightShadowColor: Color,
    darkShadowColor: Color
) {
    val elevationPx = elevation.toPx()
    val shadowOffset = elevationPx * 0.5f
    
    // Create path for the shape
    val shapePath = Path()
    val shapeSize = size
    
    when (shape) {
        CircleShape -> {
            val radius = minOf(shapeSize.width, shapeSize.height) / 2f
            val center = androidx.compose.ui.geometry.Offset(
                shapeSize.width / 2f,
                shapeSize.height / 2f
            )
            shapePath.addOval(
                androidx.compose.ui.geometry.Rect(
                    center = center,
                    radius = radius
                )
            )
        }
        is RoundedCornerShape -> {
            val cornerRadius = 16.dp.toPx() // Default corner radius
            shapePath.addRoundRect(
                androidx.compose.ui.geometry.RoundRect(
                    rect = androidx.compose.ui.geometry.Rect(
                        offset = androidx.compose.ui.geometry.Offset.Zero,
                        size = shapeSize
                    ),
                    cornerRadius = androidx.compose.ui.geometry.CornerRadius(cornerRadius)
                )
            )
        }
        else -> {
            shapePath.addRect(
                androidx.compose.ui.geometry.Rect(
                    offset = androidx.compose.ui.geometry.Offset.Zero,
                    size = shapeSize
                )
            )
        }
    }
    
    // Draw dark shadow (bottom-right)
    drawPath(
        path = shapePath,
        color = darkShadowColor,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = elevationPx),
        blendMode = BlendMode.Multiply
    )
    
    // Draw light shadow (top-left)
    drawPath(
        path = shapePath,
        color = lightShadowColor,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = elevationPx),
        blendMode = BlendMode.Screen
    )
}

/**
 * Neumorphism Card - Container with neumorphism effect
 */
@Composable
fun NeumorphismCard(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(Constants.UI.NEUMORPHISM_CORNER_RADIUS.dp),
    elevation: Dp = Constants.UI.NEUMORPHISM_ELEVATION.dp,
    content: @Composable BoxScope.() -> Unit
) {
    val neumorphismColors = MaterialTheme.neumorphismColors
    
    Box(
        modifier = modifier
            .neumorphismShadow(
                shape = shape,
                elevation = elevation,
                lightShadowColor = neumorphismColors.lightShadow,
                darkShadowColor = neumorphismColors.darkShadow
            )
            .clip(shape)
            .background(
                color = neumorphismColors.surface,
                shape = shape
            )
    ) {
        content()
    }
}

/**
 * Neumorphism Surface - Basic surface with neumorphism effect
 */
@Composable
fun NeumorphismSurface(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(Constants.UI.NEUMORPHISM_CORNER_RADIUS.dp),
    elevation: Dp = Constants.UI.NEUMORPHISM_ELEVATION.dp,
    color: Color = MaterialTheme.neumorphismColors.surface,
    content: @Composable () -> Unit
) {
    val neumorphismColors = MaterialTheme.neumorphismColors
    
    Surface(
        modifier = modifier
            .neumorphismShadow(
                shape = shape,
                elevation = elevation,
                lightShadowColor = neumorphismColors.lightShadow,
                darkShadowColor = neumorphismColors.darkShadow
            ),
        shape = shape,
        color = color,
        content = content
    )
}
