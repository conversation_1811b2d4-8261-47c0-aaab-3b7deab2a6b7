package com.zara.assistant

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Inject

/**
 * Zara Application - Ultra-Fast AI Voice Assistant
 * Features:
 * - Azure Speech Services (STT/TTS)
 * - Dual AI (Cohere/Perplexity)
 * - 30-second auto-listen
 * - Hindi/English support
 * - Neumorphism UI
 * - System controls
 * - Performance optimized
 */
@HiltAndroidApp
class ZaraApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    companion object {
        // Notification channels
        const val VOICE_SERVICE_CHANNEL = "voice_service_channel"
        const val WAKE_WORD_CHANNEL = "wake_word_channel"
        const val AI_PROCESSING_CHANNEL = "ai_processing_channel"
        const val SYSTEM_CONTROL_CHANNEL = "system_control_channel"
        
        // Service IDs
        const val VOICE_SERVICE_ID = 1001
        const val WAKE_WORD_SERVICE_ID = 1002
        const val AI_SERVICE_ID = 1003
        const val ACCESSIBILITY_SERVICE_ID = 1004
    }

    override fun onCreate() {
        super.onCreate()
        
        // Initialize notification channels
        createNotificationChannels()
        
        // Initialize WorkManager with Hilt
        WorkManager.initialize(this, workManagerConfiguration)
        
        android.util.Log.d("ZaraApplication", "🚀 Zara AI Voice Assistant initialized successfully")
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Voice Service Channel
            val voiceChannel = NotificationChannel(
                VOICE_SERVICE_CHANNEL,
                "Voice Processing",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Voice recognition and speech processing"
                setShowBadge(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            // Wake Word Channel
            val wakeWordChannel = NotificationChannel(
                WAKE_WORD_CHANNEL,
                "Wake Word Detection",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Hey Zara wake word detection"
                setShowBadge(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            // AI Processing Channel
            val aiChannel = NotificationChannel(
                AI_PROCESSING_CHANNEL,
                "AI Processing",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "AI response generation"
                setShowBadge(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            // System Control Channel
            val systemChannel = NotificationChannel(
                SYSTEM_CONTROL_CHANNEL,
                "System Control",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Device and app control"
                setShowBadge(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            notificationManager.createNotificationChannels(listOf(
                voiceChannel, wakeWordChannel, aiChannel, systemChannel
            ))
        }
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(android.util.Log.INFO)
            .build()
}
