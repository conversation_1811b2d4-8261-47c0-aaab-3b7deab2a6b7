package com.zara.assistant.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zara.assistant.domain.model.*
import com.zara.assistant.services.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Main ViewModel - Manages the main screen state and voice interactions
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val zaraSTTService: ZaraSTTService,
    private val zaraTTSService: ZaraTTSService,
    private val localCommandProcessor: LocalCommandProcessor,
    private val aiOrchestrationService: AIOrchestrationService,
    private val systemControlManager: SystemControlManager
) : ViewModel() {
    
    // Voice state management
    private val _voiceState = MutableStateFlow(VoiceState.IDLE)
    val voiceState: StateFlow<VoiceState> = _voiceState.asStateFlow()
    
    private val _isListening = MutableStateFlow(false)
    val isListening: StateFlow<Boolean> = _isListening.asStateFlow()
    
    private val _isSpeaking = MutableStateFlow(false)
    val isSpeaking: StateFlow<Boolean> = _isSpeaking.asStateFlow()
    
    private val _lastResponse = MutableStateFlow<String?>(null)
    val lastResponse: StateFlow<String?> = _lastResponse.asStateFlow()
    
    private val _currentCommand = MutableStateFlow<VoiceCommand?>(null)
    val currentCommand: StateFlow<VoiceCommand?> = _currentCommand.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // Service status
    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()

    init {
        initializeServices()
        observeServiceStates()
    }

    /**
     * Initialize all voice services
     */
    private fun initializeServices() {
        viewModelScope.launch {
            try {
                // Initialize STT service
                val sttInitialized = zaraSTTService.initialize()
                if (!sttInitialized) {
                    _errorMessage.value = "Failed to initialize speech recognition"
                    return@launch
                }
                
                // Initialize TTS service
                val ttsInitialized = zaraTTSService.initialize()
                if (!ttsInitialized) {
                    _errorMessage.value = "Failed to initialize text-to-speech"
                    return@launch
                }
                
                _isInitialized.value = true
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to initialize services: ${e.message}"
            }
        }
    }

    /**
     * Observe service states
     */
    private fun observeServiceStates() {
        viewModelScope.launch {
            // Observe STT listening state
            zaraSTTService.isListeningFlow.collect { listening ->
                _isListening.value = listening
                if (listening) {
                    _voiceState.value = VoiceState.LISTENING
                } else if (_voiceState.value == VoiceState.LISTENING) {
                    _voiceState.value = VoiceState.IDLE
                }
            }
        }
        
        viewModelScope.launch {
            // Observe TTS speaking state
            zaraTTSService.isSpeakingFlow.collect { speaking ->
                _isSpeaking.value = speaking
                if (speaking) {
                    _voiceState.value = VoiceState.SPEAKING
                } else if (_voiceState.value == VoiceState.SPEAKING) {
                    _voiceState.value = VoiceState.IDLE
                }
            }
        }
    }

    /**
     * Start listening for voice input
     */
    fun startListening() {
        if (!_isInitialized.value) {
            _errorMessage.value = "Services not initialized"
            return
        }
        
        viewModelScope.launch {
            try {
                _errorMessage.value = null
                
                val success = zaraSTTService.startListening(
                    language = "auto",
                    listener = sttListener
                )
                
                if (!success) {
                    _errorMessage.value = "Failed to start listening"
                    _voiceState.value = VoiceState.ERROR
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "Error starting listening: ${e.message}"
                _voiceState.value = VoiceState.ERROR
            }
        }
    }

    /**
     * Stop listening
     */
    fun stopListening() {
        viewModelScope.launch {
            try {
                zaraSTTService.stopListening()
            } catch (e: Exception) {
                _errorMessage.value = "Error stopping listening: ${e.message}"
            }
        }
    }

    /**
     * Stop speaking
     */
    fun stopSpeaking() {
        viewModelScope.launch {
            try {
                zaraTTSService.stopSpeaking()
            } catch (e: Exception) {
                _errorMessage.value = "Error stopping speech: ${e.message}"
            }
        }
    }

    /**
     * Execute quick action
     */
    fun executeQuickAction(action: String) {
        viewModelScope.launch {
            try {
                when (action) {
                    "wifi_on" -> {
                        val result = systemControlManager.setWifiEnabled(true)
                        speak(result.message)
                    }
                    "weather" -> {
                        processAICommand("What's the weather today?")
                    }
                    "settings" -> {
                        val result = systemControlManager.openApp("com.android.settings")
                        speak(result.message)
                    }
                    "screenshot" -> {
                        val result = systemControlManager.takeScreenshot()
                        speak(result.message)
                    }
                    else -> {
                        speak("Action not recognized")
                    }
                }
            } catch (e: Exception) {
                speak("Error executing action: ${e.message}")
            }
        }
    }

    /**
     * STT Listener implementation
     */
    private val sttListener = object : ZaraSTTService.STTListener {
        override fun onListeningStarted() {
            _voiceState.value = VoiceState.LISTENING
        }

        override fun onPartialResult(text: String, language: String) {
            // Update UI with partial results if needed
        }

        override fun onFinalResult(text: String, language: String, confidence: Float) {
            viewModelScope.launch {
                _voiceState.value = VoiceState.PROCESSING
                
                val command = VoiceCommand(
                    id = java.util.UUID.randomUUID().toString(),
                    originalText = text,
                    language = language,
                    confidence = confidence,
                    category = CommandCategory.UNKNOWN,
                    intent = CommandIntent.UNKNOWN,
                    source = VoiceSource.MANUAL
                )
                
                _currentCommand.value = command
                processVoiceCommand(command)
            }
        }

        override fun onError(error: String) {
            _errorMessage.value = error
            _voiceState.value = VoiceState.ERROR
        }

        override fun onTimeout() {
            _voiceState.value = VoiceState.IDLE
        }

        override fun onListeningStopped() {
            if (_voiceState.value == VoiceState.LISTENING) {
                _voiceState.value = VoiceState.IDLE
            }
        }
    }

    /**
     * TTS Listener implementation
     */
    private val ttsListener = object : ZaraTTSService.TTSListener {
        override fun onSpeechStarted() {
            _voiceState.value = VoiceState.SPEAKING
        }

        override fun onSpeechProgress(text: String, progress: Float) {
            // Update progress if needed
        }

        override fun onSpeechCompleted() {
            _voiceState.value = VoiceState.IDLE
        }

        override fun onSpeechCanceled() {
            _voiceState.value = VoiceState.IDLE
        }

        override fun onError(error: String) {
            _errorMessage.value = error
            _voiceState.value = VoiceState.ERROR
        }
    }

    /**
     * Process voice command
     */
    private suspend fun processVoiceCommand(command: VoiceCommand) {
        try {
            // First try local command processing
            val localResult = localCommandProcessor.processCommand(command)
            
            if (localResult.success) {
                speak(localResult.message)
                return
            }
            
            // If not a local command, use AI processing
            processAICommand(command.originalText, command.language)
            
        } catch (e: Exception) {
            _errorMessage.value = "Error processing command: ${e.message}"
            speak("I'm sorry, I encountered an error processing your request.")
        }
    }

    /**
     * Process AI command
     */
    private suspend fun processAICommand(text: String, language: String = "auto") {
        try {
            val command = VoiceCommand(
                id = java.util.UUID.randomUUID().toString(),
                originalText = text,
                language = language,
                confidence = 1.0f,
                category = CommandCategory.AI_CONVERSATION,
                intent = CommandIntent.GENERAL_CHAT,
                source = VoiceSource.MANUAL
            )
            
            val result = aiOrchestrationService.processCommand(command)
            
            if (result.isSuccess) {
                val response = result.getOrNull()
                if (response != null) {
                    _lastResponse.value = response.text
                    speak(response.text)
                } else {
                    speak("I'm sorry, I couldn't process that request.")
                }
            } else {
                val error = result.exceptionOrNull()?.message ?: "Unknown error"
                _errorMessage.value = "AI processing failed: $error"
                speak("I'm sorry, I encountered an error processing your request.")
            }
            
        } catch (e: Exception) {
            _errorMessage.value = "Error in AI processing: ${e.message}"
            speak("I'm sorry, I encountered an error.")
        }
    }

    /**
     * Speak text using TTS
     */
    private suspend fun speak(text: String) {
        try {
            zaraTTSService.speak(
                text = text,
                listener = ttsListener
            )
        } catch (e: Exception) {
            _errorMessage.value = "Error speaking: ${e.message}"
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Get service status
     */
    fun getServiceStatus(): Map<String, Boolean> {
        return mapOf(
            "STT Available" to zaraSTTService.isAvailable(),
            "TTS Available" to zaraTTSService.isAvailable(),
            "Initialized" to _isInitialized.value
        )
    }

    override fun onCleared() {
        super.onCleared()
        // Cleanup services
        zaraSTTService.cleanup()
        zaraTTSService.cleanup()
    }
}
