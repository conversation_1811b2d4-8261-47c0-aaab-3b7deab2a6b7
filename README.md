# <PERSON>ara AI Voice Assistant

A sophisticated AI-powered voice assistant for Android with neumorphism UI design, built using modern Android development practices.

## Features

### 🎙️ Voice Interaction
- **Wake Word Detection**: "Hey <PERSON><PERSON>" using Picovoice Porcupine
- **Speech Recognition**: Microsoft Azure Speech Services EXCLUSIVELY (no Android STT)
- **Text-to-Speech**: Natural voice responses with customizable settings
- **Voice Visualization**: Real-time audio wave animations

### 🤖 AI Integration
- **Intelligent Command Routing**: Local processing for system commands, AI for complex queries
- **Local Command Intelligence**: Advanced pattern matching for instant system control
- **Dual AI Sources**: Cohere API for conversational AI, Perplexity API for information queries
- **Smart Command Classification**: Automatic categorization of voice commands
- **Context-Aware Responses**: Maintains conversation history and context
- **Personality Modes**: Professional, Friendly, and Casual response styles

### 📱 System Control
- **Accessibility Service**: Voice control of device settings and apps
- **Notification Management**: Read, respond to, and manage notifications
- **App Control**: Open, close, and switch between applications
- **Device Settings**: Control WiFi, Bluetooth, volume, brightness, and more

### 🎨 Neumorphism UI
- **Modern Design**: Soft UI with authentic neumorphism effects
- **Jetpack Compose**: Declarative UI with smooth animations
- **Material Design 3**: Dynamic theming with light/dark mode support
- **Voice-First Interface**: Minimal visual elements prioritizing voice interaction

### 🔒 Security & Privacy
- **Secure Storage**: Encrypted preferences and sensitive data
- **Permission Management**: Granular control over app permissions
- **Privacy Controls**: Optional conversation history and analytics
- **Data Minimization**: Only collect necessary data

## Architecture

### 🏗️ Clean Architecture
- **MVVM Pattern**: Model-View-ViewModel with Repository pattern
- **Dependency Injection**: Hilt for clean dependency management
- **Use Cases**: Business logic encapsulation
- **Repository Pattern**: Data layer abstraction

### 🔧 Technology Stack
- **Language**: Kotlin 100%
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM + Repository + Use Cases
- **DI**: Hilt
- **Database**: Room (for conversation history)
- **Networking**: Retrofit + OkHttp
- **Async**: Coroutines + Flow
- **Voice**: Picovoice Porcupine + Azure Speech Services + Android TTS

## Setup Instructions

### Prerequisites
1. Android Studio Arctic Fox or later
2. Android SDK 26+ (Android 8.0+)
3. API Keys for:
   - Cohere API
   - Perplexity API
   - Picovoice Porcupine
   - Microsoft Azure Speech Services

### Installation
1. Clone the repository
2. Create `local.properties` file in root directory:
   ```properties
   cohere_api_key=your_cohere_api_key_here
   perplexity_api_key=your_perplexity_api_key_here
   porcupine_access_key=your_porcupine_access_key_here
   azure_speech_key=your_azure_speech_key_here
   azure_speech_region=your_azure_speech_region_here
   ```
3. Add Porcupine wake word model file (`hey_zara_android.ppn`) to `assets` folder
4. Build and run the project

### Required Permissions
- **Microphone**: For voice input
- **Accessibility Service**: For device control
- **Notification Access**: For notification management
- **System Alert Window**: For overlay interface
- **Phone/SMS**: For communication features (optional)
- **Location**: For location-based responses (optional)

## Usage

### Basic Voice Commands
- **Device Control**: "Turn on WiFi", "Set brightness to 50%", "Open Settings"
- **App Management**: "Open WhatsApp", "Close Chrome", "Switch to Spotify"
- **Communication**: "Call Mom", "Send message to John", "Read my notifications"
- **Information**: "What's the weather?", "Search for restaurants nearby"
- **Entertainment**: "Play music", "Next song", "Pause"

### Wake Word Activation
1. Say "Hey Zara" to activate
2. Wait for the listening indicator
3. Speak your command clearly
4. Zara will process and respond

### Manual Activation
- Tap the central voice button to start listening
- Tap again to stop listening or speaking

## Configuration

### Voice Settings
- **Wake Word Sensitivity**: Adjust detection sensitivity
- **Speech Rate**: Control TTS speaking speed
- **Speech Pitch**: Adjust voice pitch
- **Language**: Select voice language
- **Auto Listen**: Continue listening after responses

### AI Settings
- **Personality**: Choose response personality (Professional/Friendly/Casual)
- **Response Style**: Select response length (Brief/Detailed/Conversational)
- **AI Source**: Prefer Cohere or Perplexity for different query types

### Privacy Settings
- **Conversation History**: Enable/disable conversation storage
- **Voice Data Storage**: Control voice recording retention
- **Analytics**: Share usage data for improvements

## Development

### Project Structure
```
app/
├── src/main/java/com/zara/assistant/
│   ├── core/                 # Constants and utilities
│   ├── data/                 # Data layer (repositories, APIs, database)
│   ├── domain/               # Domain layer (models, repositories, use cases)
│   ├── presentation/         # UI layer (components, theme)
│   ├── services/             # Background services
│   ├── ui/                   # Activities, screens, ViewModels
│   └── utils/                # Utility classes
├── src/test/                 # Unit tests
└── src/androidTest/          # Instrumentation tests
```

### Key Components
- **WakeWordService**: Picovoice Porcupine integration
- **ZaraVoiceService**: Clean voice processing with STT/TTS integration
- **ZaraSTTService**: Azure Speech-to-Text with Hindi/English support
- **ZaraTTSService**: Azure Text-to-Speech with natural voices
- **LocalCommandProcessor**: Local intelligence for system commands
- **AIProcessingService**: AI response generation
- **ZaraAccessibilityService**: System control
- **NotificationListenerService**: Notification management

### Testing
- **Unit Tests**: Repository and ViewModel testing
- **Integration Tests**: Service and API testing
- **UI Tests**: Compose UI testing
- **Performance Tests**: Memory and CPU monitoring

## Performance Optimization

### Memory Management
- Efficient caching with size limits
- Automatic garbage collection monitoring
- Memory usage tracking and alerts

### Battery Optimization
- Optimized wake word detection
- Efficient background processing
- Smart service lifecycle management

### Network Optimization
- Request caching and retry logic
- Fallback AI sources
- Connection pooling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow Kotlin coding conventions
4. Add tests for new features
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **Picovoice** for wake word detection technology
- **Cohere** for conversational AI capabilities
- **Perplexity** for information retrieval
- **Material Design** for design guidelines
- **Android Jetpack** for modern Android development

## Support

For support, please open an issue on GitHub or contact the development team.

---

**Zara AI Voice Assistant** - Your intelligent companion for hands-free Android interaction.
