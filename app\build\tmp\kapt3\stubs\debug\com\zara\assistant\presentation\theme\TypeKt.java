package com.zara.assistant.presentation.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\b"}, d2 = {"InterFontFamily", "Landroidx/compose/ui/text/font/SystemFontFamily;", "getInterFontFamily", "()Landroidx/compose/ui/text/font/SystemFontFamily;", "ZaraTypography", "Landroidx/compose/material3/Typography;", "getZaraTypography", "()Landroidx/compose/material3/Typography;", "app_debug"})
public final class TypeKt {
    
    /**
     * Zara Typography - Modern, clean typography for voice-first interface
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.font.SystemFontFamily InterFontFamily = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Typography ZaraTypography = null;
    
    /**
     * Zara Typography - Modern, clean typography for voice-first interface
     */
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.text.font.SystemFontFamily getInterFontFamily() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.material3.Typography getZaraTypography() {
        return null;
    }
}