package com.zara.assistant.services

import android.app.Notification
import android.content.ComponentName
import android.content.Intent
import android.os.Build
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.zara.assistant.domain.model.SystemControlResult
import com.zara.assistant.domain.repository.NotificationData
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Zara Notification Listener Service - Intelligent notification management
 * Features:
 * - Real-time notification monitoring
 * - Smart notification filtering
 * - Voice-controlled notification actions
 * - Intelligent notification summaries
 */
class ZaraNotificationListenerService : NotificationListenerService() {

    companion object {
        private const val TAG = "ZaraNotificationListener"
    }

    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val notificationCache = mutableMapOf<String, NotificationData>()

    // Notification events
    private val _notificationEvents = MutableSharedFlow<NotificationEvent>()
    val notificationEvents: SharedFlow<NotificationEvent> = _notificationEvents.asSharedFlow()

    override fun onListenerConnected() {
        super.onListenerConnected()
        Log.d(TAG, "🔌 Zara Notification Listener connected")
        
        // Load existing notifications
        serviceScope.launch {
            loadExistingNotifications()
        }
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        super.onNotificationPosted(sbn)
        
        serviceScope.launch {
            try {
                val notificationData = convertToNotificationData(sbn)
                if (notificationData != null && shouldProcessNotification(notificationData)) {
                    notificationCache[sbn.key] = notificationData
                    
                    Log.d(TAG, "📬 New notification: ${notificationData.appName} - ${notificationData.title}")
                    
                    _notificationEvents.emit(
                        NotificationEvent.NotificationPosted(notificationData)
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error processing posted notification: ${e.message}", e)
            }
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        super.onNotificationRemoved(sbn)
        
        serviceScope.launch {
            try {
                val removedNotification = notificationCache.remove(sbn.key)
                if (removedNotification != null) {
                    Log.d(TAG, "🗑️ Notification removed: ${removedNotification.appName}")
                    
                    _notificationEvents.emit(
                        NotificationEvent.NotificationRemoved(removedNotification)
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error processing removed notification: ${e.message}", e)
            }
        }
    }

    /**
     * Get all active notifications with intelligent filtering
     */
    suspend fun getActiveNotificationData(): List<NotificationData> = withContext(Dispatchers.IO) {
        try {
            val notifications = notificationCache.values.toList()
            
            // Sort by priority and timestamp
            notifications.sortedWith(compareByDescending<NotificationData> { it.isHighPriority }
                .thenByDescending { it.timestamp })
                
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting active notifications: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * Generate intelligent notification summary
     */
    suspend fun generateNotificationSummary(): String = withContext(Dispatchers.IO) {
        try {
            val notifications = notificationCache.values.filter { shouldIncludeInSummary(it) }
            
            if (notifications.isEmpty()) {
                return@withContext "You have no new notifications."
            }
            
            val summary = StringBuilder()
            val groupedByApp = notifications.groupBy { it.packageName }
            
            summary.append("You have ${notifications.size} notification${if (notifications.size > 1) "s" else ""}. ")
            
            groupedByApp.forEach { (_, appNotifications) ->
                val appName = appNotifications.first().appName
                val count = appNotifications.size
                
                if (count == 1) {
                    val notification = appNotifications.first()
                    summary.append("$appName: ${notification.title}. ")
                } else {
                    summary.append("$appName: $count notifications. ")
                }
            }
            
            summary.toString().trim()
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating notification summary: ${e.message}", e)
            "Unable to generate notification summary."
        }
    }

    /**
     * Dismiss notification by key
     */
    suspend fun dismissNotification(key: String): SystemControlResult = withContext(Dispatchers.IO) {
        return@withContext try {
            cancelNotification(key)
            notificationCache.remove(key)
            
            Log.d(TAG, "✅ Notification dismissed: $key")
            
            SystemControlResult(
                success = true,
                action = "dismiss_notification",
                message = "Notification dismissed successfully"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error dismissing notification: ${e.message}", e)
            
            SystemControlResult(
                success = false,
                action = "dismiss_notification_error",
                message = "Failed to dismiss notification: ${e.message}"
            )
        }
    }

    /**
     * Dismiss all notifications
     */
    suspend fun dismissAllNotifications(): SystemControlResult = withContext(Dispatchers.IO) {
        return@withContext try {
            val count = notificationCache.size
            
            cancelAllNotifications()
            notificationCache.clear()
            
            Log.d(TAG, "✅ All notifications dismissed: $count")
            
            SystemControlResult(
                success = true,
                action = "dismiss_all_notifications",
                message = "All $count notifications dismissed"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error dismissing all notifications: ${e.message}", e)
            
            SystemControlResult(
                success = false,
                action = "dismiss_all_notifications_error",
                message = "Failed to dismiss notifications: ${e.message}"
            )
        }
    }

    /**
     * Load existing notifications on service start
     */
    private suspend fun loadExistingNotifications() = withContext(Dispatchers.IO) {
        try {
            val systemNotifications = activeNotifications
            systemNotifications?.forEach { sbn ->
                val notificationData = convertToNotificationData(sbn)
                if (notificationData != null && shouldProcessNotification(notificationData)) {
                    notificationCache[sbn.key] = notificationData
                }
            }

            Log.d(TAG, "📋 Loaded ${notificationCache.size} existing notifications")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error loading existing notifications: ${e.message}", e)
        }
    }

    /**
     * Convert StatusBarNotification to NotificationData
     */
    private fun convertToNotificationData(sbn: StatusBarNotification): NotificationData? {
        return try {
            val notification = sbn.notification
            val extras = notification.extras
            
            NotificationData(
                key = sbn.key,
                packageName = sbn.packageName,
                appName = getAppName(sbn.packageName),
                title = extras.getCharSequence(Notification.EXTRA_TITLE)?.toString() ?: "",
                text = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: "",
                timestamp = sbn.postTime,
                isHighPriority = notification.priority >= Notification.PRIORITY_HIGH,
                hasReplyAction = hasReplyAction(notification),
                category = notification.category,
                group = notification.group
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error converting notification: ${e.message}", e)
            null
        }
    }

    /**
     * Get app name from package name
     */
    private fun getAppName(packageName: String): String {
        return try {
            val packageManager = applicationContext.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: Exception) {
            packageName // Fallback to package name
        }
    }

    /**
     * Check if notification has reply action
     */
    private fun hasReplyAction(notification: Notification): Boolean {
        return notification.actions?.any { action ->
            action.remoteInputs?.isNotEmpty() == true
        } ?: false
    }

    /**
     * Determine if notification should be processed
     */
    private fun shouldProcessNotification(notificationData: NotificationData): Boolean {
        // Filter out system notifications and ongoing notifications
        return !isSystemNotification(notificationData.packageName) &&
                notificationData.title.isNotBlank()
    }

    /**
     * Determine if notification should be included in summary
     */
    private fun shouldIncludeInSummary(notificationData: NotificationData): Boolean {
        return shouldProcessNotification(notificationData) &&
                !isOldNotification(notificationData.timestamp)
    }

    /**
     * Check if package is a system notification
     */
    private fun isSystemNotification(packageName: String): Boolean {
        val systemPackages = setOf(
            "android",
            "com.android.systemui",
            "com.android.settings"
        )
        return systemPackages.contains(packageName)
    }

    /**
     * Check if notification is too old (older than 24 hours)
     */
    private fun isOldNotification(timestamp: Long): Boolean {
        val twentyFourHours = 24 * 60 * 60 * 1000L
        return System.currentTimeMillis() - timestamp > twentyFourHours
    }

    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        Log.d(TAG, "🔌 Zara Notification Listener disconnected")
        
        // Request rebind
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            requestRebind(ComponentName(this, ZaraNotificationListenerService::class.java))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        Log.d(TAG, "🔥 Zara Notification Listener destroyed")
    }
}

/**
 * Notification Events
 */
sealed class NotificationEvent {
    data class NotificationPosted(val notification: NotificationData) : NotificationEvent()
    data class NotificationRemoved(val notification: NotificationData) : NotificationEvent()
}
