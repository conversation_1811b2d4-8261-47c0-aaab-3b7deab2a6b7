pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        // Picovoice Maven repository
        maven {
            url 'https://s01.oss.sonatype.org/content/repositories/releases/'
        }
    }
}

rootProject.name = "<PERSON>ara"
include ':app'
