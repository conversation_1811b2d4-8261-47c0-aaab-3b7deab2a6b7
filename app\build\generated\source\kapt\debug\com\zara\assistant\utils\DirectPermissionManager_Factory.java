// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DirectPermissionManager_Factory implements Factory<DirectPermissionManager> {
  private final Provider<Context> contextProvider;

  public DirectPermissionManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DirectPermissionManager get() {
    return newInstance(contextProvider.get());
  }

  public static DirectPermissionManager_Factory create(Provider<Context> contextProvider) {
    return new DirectPermissionManager_Factory(contextProvider);
  }

  public static DirectPermissionManager newInstance(Context context) {
    return new DirectPermissionManager(context);
  }
}
