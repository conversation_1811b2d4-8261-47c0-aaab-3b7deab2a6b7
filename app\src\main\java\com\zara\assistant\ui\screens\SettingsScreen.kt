package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard

/**
 * Mind-Blowing Settings Screen - Beautiful and functional settings
 * Features:
 * - Stunning neumorphism design
 * - Animated interactions
 * - Organized setting categories
 * - Real-time status updates
 * - Smooth transitions
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    onNavigateToVoiceSettings: () -> Unit,
    onNavigateToPermissions: () -> Unit,
    onNavigateToAbout: () -> Unit,
    onNavigateToCommands: () -> Unit
) {
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "settings_animation")
    
    // Background gradient animation
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "gradient_offset"
    )
    
    // Floating animation
    val floatingOffset by infiniteTransition.animateFloat(
        initialValue = -5f,
        targetValue = 5f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "floating_offset"
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF667EEA),
                        Color(0xFF764BA2),
                        Color(0xFF6B73FF)
                    ),
                    start = androidx.compose.ui.geometry.Offset(
                        gradientOffset * 1000f,
                        gradientOffset * 1000f
                    ),
                    end = androidx.compose.ui.geometry.Offset(
                        (1f - gradientOffset) * 1000f,
                        (1f - gradientOffset) * 1000f
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Top bar
            SettingsTopBar(
                onNavigateBack = onNavigateBack,
                modifier = Modifier.offset(y = floatingOffset.dp)
            )
            
            // Settings content
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
                    .offset(y = (-floatingOffset * 0.5f).dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // Settings categories
                items(settingsCategories) { category ->
                    SettingsCategory(
                        category = category,
                        onItemClick = { item ->
                            when (item.id) {
                                "voice_settings" -> onNavigateToVoiceSettings()
                                "permissions" -> onNavigateToPermissions()
                                "commands_help" -> onNavigateToCommands()
                                "about" -> onNavigateToAbout()
                                else -> { /* Handle other items */ }
                            }
                        }
                    )
                }
                
                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }
            }
        }
    }
}

/**
 * Settings top bar
 */
@Composable
private fun SettingsTopBar(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(24.dp)
            .statusBarsPadding(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        NeumorphismButton(
            onClick = onNavigateBack,
            modifier = Modifier.size(48.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Back",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Text(
            text = "Settings",
            style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        )
    }
}

/**
 * Settings category section
 */
@Composable
private fun SettingsCategory(
    category: SettingsCategory,
    onItemClick: (SettingsItem) -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "category_scale"
    )
    
    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(category.animationDelay)
        isVisible = true
    }
    
    NeumorphismCard(
        modifier = Modifier
            .fillMaxWidth()
            .scale(scale),
        elevation = 12.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Category header
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = category.icon,
                    contentDescription = category.title,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = category.title,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                )
            }
            
            if (category.description.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = category.description,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Category items
            category.items.forEach { item ->
                SettingsItemRow(
                    item = item,
                    onClick = { onItemClick(item) }
                )
                
                if (item != category.items.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * Individual settings item row
 */
@Composable
private fun SettingsItemRow(
    item: SettingsItem,
    onClick: () -> Unit
) {
    NeumorphismButton(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Item icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = item.color.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(10.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = item.icon,
                    contentDescription = item.title,
                    modifier = Modifier.size(20.dp),
                    tint = item.color
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Item details
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    )
                )
                
                if (item.subtitle.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    
                    Text(
                        text = item.subtitle,
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    )
                }
            }
            
            // Arrow icon
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Navigate",
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
            )
        }
    }
}

/**
 * Settings data classes
 */
data class SettingsCategory(
    val id: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val items: List<SettingsItem>,
    val animationDelay: Long
)

data class SettingsItem(
    val id: String,
    val title: String,
    val subtitle: String,
    val icon: ImageVector,
    val color: Color
)

/**
 * Settings categories data
 */
private val settingsCategories = listOf(
    SettingsCategory(
        id = "voice_ai",
        title = "Voice & AI",
        description = "Configure voice recognition, AI responses, and language settings",
        icon = Icons.Default.Mic,
        animationDelay = 100L,
        items = listOf(
            SettingsItem(
                id = "voice_settings",
                title = "Voice Settings",
                subtitle = "Speech rate, pitch, and voice selection",
                icon = Icons.Default.RecordVoiceOver,
                color = Color(0xFF4F46E5)
            ),
            SettingsItem(
                id = "ai_settings",
                title = "AI Configuration",
                subtitle = "Personality mode and AI preferences",
                icon = Icons.Default.Psychology,
                color = Color(0xFF7C3AED)
            ),
            SettingsItem(
                id = "language_settings",
                title = "Language & Region",
                subtitle = "Hindi, English, and auto-detection",
                icon = Icons.Default.Language,
                color = Color(0xFF059669)
            )
        )
    ),
    SettingsCategory(
        id = "system_control",
        title = "System Control",
        description = "Manage device control permissions and accessibility features",
        icon = Icons.Default.Settings,
        animationDelay = 200L,
        items = listOf(
            SettingsItem(
                id = "permissions",
                title = "Permissions",
                subtitle = "Manage app permissions and access",
                icon = Icons.Default.Security,
                color = Color(0xFFDC2626)
            ),
            SettingsItem(
                id = "accessibility",
                title = "Accessibility Service",
                subtitle = "Enable system control features",
                icon = Icons.Default.Accessibility,
                color = Color(0xFFEA580C)
            ),
            SettingsItem(
                id = "notifications",
                title = "Notification Access",
                subtitle = "Read and manage notifications",
                icon = Icons.Default.Notifications,
                color = Color(0xFF0891B2)
            )
        )
    ),
    SettingsCategory(
        id = "help_support",
        title = "Help & Support",
        description = "Get help, learn commands, and app information",
        icon = Icons.Default.Help,
        animationDelay = 300L,
        items = listOf(
            SettingsItem(
                id = "commands_help",
                title = "Voice Commands",
                subtitle = "Learn available voice commands",
                icon = Icons.Default.List,
                color = Color(0xFF8B5CF6)
            ),
            SettingsItem(
                id = "tutorial",
                title = "Tutorial",
                subtitle = "Interactive app tutorial",
                icon = Icons.Default.School,
                color = Color(0xFF06B6D4)
            ),
            SettingsItem(
                id = "about",
                title = "About Zara",
                subtitle = "App version and information",
                icon = Icons.Default.Info,
                color = Color(0xFF84CC16)
            )
        )
    )
)
