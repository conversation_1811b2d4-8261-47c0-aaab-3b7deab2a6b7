package com.zara.assistant.services;

/**
 * Zara Azure Text-to-Speech Service
 * Ultra-fast TTS with JennyNeural/GuyNeural voices and streaming audio
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0010\b\u0007\u0018\u0000 <2\u00020\u0001:\u0002<=B!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0012\u0010#\u001a\u00020\u001f2\b\u0010$\u001a\u0004\u0018\u00010%H\u0002J\u0006\u0010&\u001a\u00020\'J(\u0010(\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\u000b2\u0006\u0010*\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020\u001f2\u0006\u0010,\u001a\u00020\u001fH\u0002J\u0014\u0010-\u001a\b\u0012\u0004\u0012\u00020\u000b0.H\u0086@\u00a2\u0006\u0002\u0010/J\u000e\u00100\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010/J\u0006\u00101\u001a\u00020\rJ\u0006\u00102\u001a\u00020\rJ\u0012\u00103\u001a\u00020\u000b2\b\u00104\u001a\u0004\u0018\u00010\u000bH\u0002J/\u00105\u001a\u00020\'2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u001f2\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u001f\u00a2\u0006\u0002\u00106J\b\u00107\u001a\u00020\'H\u0002JR\u00108\u001a\u00020\r2\u0006\u0010)\u001a\u00020\u000b2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u00104\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u001f2\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u001f2\n\b\u0002\u00109\u001a\u0004\u0018\u00010\u0011H\u0086@\u00a2\u0006\u0002\u0010:J\u000e\u0010;\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010/R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0017\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\r0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/zara/assistant/services/ZaraTTSService;", "", "context", "Landroid/content/Context;", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "performanceUtils", "Lcom/zara/assistant/utils/PerformanceUtils;", "(Landroid/content/Context;Lcom/zara/assistant/utils/ApiKeyManager;Lcom/zara/assistant/utils/PerformanceUtils;)V", "_currentVoice", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isSpeaking", "", "audioConfig", "Lcom/microsoft/cognitiveservices/speech/audio/AudioConfig;", "currentListener", "Lcom/zara/assistant/services/ZaraTTSService$TTSListener;", "currentVoice", "currentVoiceFlow", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentVoiceFlow", "()Lkotlinx/coroutines/flow/StateFlow;", "isInitialized", "isSpeaking", "isSpeakingFlow", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "speechConfig", "Lcom/microsoft/cognitiveservices/speech/SpeechConfig;", "speechPitch", "", "speechRate", "speechSynthesizer", "Lcom/microsoft/cognitiveservices/speech/SpeechSynthesizer;", "calculateProgress", "audioData", "", "cleanup", "", "createSSML", "text", "voice", "rate", "pitch", "getAvailableVoices", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initialize", "isAvailable", "isCurrentlySpeaking", "selectVoiceForLanguage", "language", "setVoiceParameters", "(Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;)V", "setupSynthesisCallbacks", "speak", "listener", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;Lcom/zara/assistant/services/ZaraTTSService$TTSListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "stopSpeaking", "Companion", "TTSListener", "app_release"})
public final class ZaraTTSService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.ApiKeyManager apiKeyManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.PerformanceUtils performanceUtils = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ZaraTTS";
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechConfig speechConfig;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.audio.AudioConfig audioConfig;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechSynthesizer speechSynthesizer;
    private boolean isInitialized = false;
    private boolean isSpeaking = false;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentVoice = "en-US-JennyNeural";
    private float speechRate = 1.0F;
    private float speechPitch = 1.0F;
    @org.jetbrains.annotations.Nullable()
    private com.zara.assistant.services.ZaraTTSService.TTSListener currentListener;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isSpeaking = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSpeakingFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _currentVoice = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> currentVoiceFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.ZaraTTSService.Companion Companion = null;
    
    @javax.inject.Inject()
    public ZaraTTSService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSpeakingFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getCurrentVoiceFlow() {
        return null;
    }
    
    /**
     * Initialize Azure Speech TTS Service
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Speak text with specified parameters
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object speak(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.Nullable()
    java.lang.String voice, @org.jetbrains.annotations.Nullable()
    java.lang.String language, @org.jetbrains.annotations.Nullable()
    java.lang.Float rate, @org.jetbrains.annotations.Nullable()
    java.lang.Float pitch, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.services.ZaraTTSService.TTSListener listener, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Stop current speech
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object stopSpeaking(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Setup synthesis event callbacks
     */
    private final void setupSynthesisCallbacks() {
    }
    
    /**
     * Create SSML for advanced voice control
     */
    private final java.lang.String createSSML(java.lang.String text, java.lang.String voice, float rate, float pitch) {
        return null;
    }
    
    /**
     * Select appropriate voice for language
     */
    private final java.lang.String selectVoiceForLanguage(java.lang.String language) {
        return null;
    }
    
    /**
     * Calculate speech progress from audio data
     */
    private final float calculateProgress(byte[] audioData) {
        return 0.0F;
    }
    
    /**
     * Set voice parameters
     */
    public final void setVoiceParameters(@org.jetbrains.annotations.Nullable()
    java.lang.String voice, @org.jetbrains.annotations.Nullable()
    java.lang.Float rate, @org.jetbrains.annotations.Nullable()
    java.lang.Float pitch) {
    }
    
    /**
     * Get available voices
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAvailableVoices(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Check if TTS is available and ready
     */
    public final boolean isAvailable() {
        return false;
    }
    
    /**
     * Get current speaking state
     */
    public final boolean isCurrentlySpeaking() {
        return false;
    }
    
    /**
     * Cleanup resources
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/ZaraTTSService$Companion;", "", "()V", "TAG", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * TTS Result Listener Interface
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0003H&J\b\u0010\u0007\u001a\u00020\u0003H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u000bH&J\b\u0010\f\u001a\u00020\u0003H&\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/services/ZaraTTSService$TTSListener;", "", "onError", "", "error", "", "onSpeechCanceled", "onSpeechCompleted", "onSpeechProgress", "text", "progress", "", "onSpeechStarted", "app_release"})
    public static abstract interface TTSListener {
        
        public abstract void onSpeechStarted();
        
        public abstract void onSpeechProgress(@org.jetbrains.annotations.NotNull()
        java.lang.String text, float progress);
        
        public abstract void onSpeechCompleted();
        
        public abstract void onSpeechCanceled();
        
        public abstract void onError(@org.jetbrains.annotations.NotNull()
        java.lang.String error);
    }
}