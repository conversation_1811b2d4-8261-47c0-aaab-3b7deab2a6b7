package com.zara.assistant.services;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = ZaraVoiceService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface ZaraVoiceService_GeneratedInjector {
  void injectZaraVoiceService(ZaraVoiceService zaraVoiceService);
}
