package com.zara.assistant.domain.model;

/**
 * User Preferences Model
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b&\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B}\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\b\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0011J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\t\u0010#\u001a\u00020\u0006H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\'\u001a\u00020\bH\u00c6\u0003J\t\u0010(\u001a\u00020\bH\u00c6\u0003J\t\u0010)\u001a\u00020\u0006H\u00c6\u0003J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\u0081\u0001\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\b2\b\b\u0002\u0010\u000f\u001a\u00020\u00062\b\b\u0002\u0010\u0010\u001a\u00020\u0003H\u00c6\u0001J\t\u0010.\u001a\u00020/H\u00d6\u0001J\u0013\u00100\u001a\u00020\u00032\b\u00101\u001a\u0004\u0018\u000102H\u00d6\u0003J\t\u00103\u001a\u00020/H\u00d6\u0001J\t\u00104\u001a\u00020\u0006H\u00d6\u0001J\u0019\u00105\u001a\u0002062\u0006\u00107\u001a\u0002082\u0006\u00109\u001a\u00020/H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u0011\u0010\u000f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001cR\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0013R\u0011\u0010\u000e\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001c\u00a8\u0006:"}, d2 = {"Lcom/zara/assistant/domain/model/UserPreferences;", "Landroid/os/Parcelable;", "wakeWordEnabled", "", "autoListenEnabled", "preferredLanguage", "", "speechRate", "", "speechPitch", "personalityMode", "conversationHistoryEnabled", "voiceFeedbackEnabled", "hapticFeedbackEnabled", "wakeWordSensitivity", "preferredVoice", "darkModeEnabled", "(ZZLjava/lang/String;FFLjava/lang/String;ZZZFLjava/lang/String;Z)V", "getAutoListenEnabled", "()Z", "getConversationHistoryEnabled", "getDarkModeEnabled", "getHapticFeedbackEnabled", "getPersonalityMode", "()Ljava/lang/String;", "getPreferredLanguage", "getPreferredVoice", "getSpeechPitch", "()F", "getSpeechRate", "getVoiceFeedbackEnabled", "getWakeWordEnabled", "getWakeWordSensitivity", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_release"})
@kotlinx.parcelize.Parcelize()
public final class UserPreferences implements android.os.Parcelable {
    private final boolean wakeWordEnabled = false;
    private final boolean autoListenEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String preferredLanguage = null;
    private final float speechRate = 0.0F;
    private final float speechPitch = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String personalityMode = null;
    private final boolean conversationHistoryEnabled = false;
    private final boolean voiceFeedbackEnabled = false;
    private final boolean hapticFeedbackEnabled = false;
    private final float wakeWordSensitivity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String preferredVoice = null;
    private final boolean darkModeEnabled = false;
    
    public UserPreferences(boolean wakeWordEnabled, boolean autoListenEnabled, @org.jetbrains.annotations.NotNull()
    java.lang.String preferredLanguage, float speechRate, float speechPitch, @org.jetbrains.annotations.NotNull()
    java.lang.String personalityMode, boolean conversationHistoryEnabled, boolean voiceFeedbackEnabled, boolean hapticFeedbackEnabled, float wakeWordSensitivity, @org.jetbrains.annotations.NotNull()
    java.lang.String preferredVoice, boolean darkModeEnabled) {
        super();
    }
    
    public final boolean getWakeWordEnabled() {
        return false;
    }
    
    public final boolean getAutoListenEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPreferredLanguage() {
        return null;
    }
    
    public final float getSpeechRate() {
        return 0.0F;
    }
    
    public final float getSpeechPitch() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPersonalityMode() {
        return null;
    }
    
    public final boolean getConversationHistoryEnabled() {
        return false;
    }
    
    public final boolean getVoiceFeedbackEnabled() {
        return false;
    }
    
    public final boolean getHapticFeedbackEnabled() {
        return false;
    }
    
    public final float getWakeWordSensitivity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPreferredVoice() {
        return null;
    }
    
    public final boolean getDarkModeEnabled() {
        return false;
    }
    
    public UserPreferences() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.UserPreferences copy(boolean wakeWordEnabled, boolean autoListenEnabled, @org.jetbrains.annotations.NotNull()
    java.lang.String preferredLanguage, float speechRate, float speechPitch, @org.jetbrains.annotations.NotNull()
    java.lang.String personalityMode, boolean conversationHistoryEnabled, boolean voiceFeedbackEnabled, boolean hapticFeedbackEnabled, float wakeWordSensitivity, @org.jetbrains.annotations.NotNull()
    java.lang.String preferredVoice, boolean darkModeEnabled) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}