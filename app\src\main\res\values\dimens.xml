<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Spacing -->
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xxl">48dp</dimen>

    <!-- Margins -->
    <dimen name="margin_horizontal">16dp</dimen>
    <dimen name="margin_vertical">16dp</dimen>
    <dimen name="margin_card">16dp</dimen>
    <dimen name="margin_item">8dp</dimen>

    <!-- Padding -->
    <dimen name="padding_xs">4dp</dimen>
    <dimen name="padding_sm">8dp</dimen>
    <dimen name="padding_md">16dp</dimen>
    <dimen name="padding_lg">24dp</dimen>
    <dimen name="padding_xl">32dp</dimen>

    <!-- Corner Radius -->
    <dimen name="corner_radius_xs">4dp</dimen>
    <dimen name="corner_radius_sm">8dp</dimen>
    <dimen name="corner_radius_md">12dp</dimen>
    <dimen name="corner_radius_lg">16dp</dimen>
    <dimen name="corner_radius_xl">20dp</dimen>
    <dimen name="corner_radius_xxl">24dp</dimen>
    <dimen name="corner_radius_round">50dp</dimen>

    <!-- Neumorphism Specific -->
    <dimen name="neuro_elevation">8dp</dimen>
    <dimen name="neuro_blur_radius">16dp</dimen>
    <dimen name="neuro_offset_x">8dp</dimen>
    <dimen name="neuro_offset_y">8dp</dimen>
    <dimen name="neuro_inset_offset">-4dp</dimen>

    <!-- Voice Button -->
    <dimen name="voice_button_size">120dp</dimen>
    <dimen name="voice_button_icon_size">48dp</dimen>
    <dimen name="voice_ripple_size">200dp</dimen>

    <!-- Cards -->
    <dimen name="card_elevation">0dp</dimen>
    <dimen name="card_corner_radius">20dp</dimen>
    <dimen name="card_margin">16dp</dimen>
    <dimen name="card_padding">20dp</dimen>

    <!-- Buttons -->
    <dimen name="button_height">56dp</dimen>
    <dimen name="button_corner_radius">16dp</dimen>
    <dimen name="button_padding_horizontal">24dp</dimen>
    <dimen name="button_padding_vertical">16dp</dimen>

    <!-- Text Sizes -->
    <dimen name="text_size_xs">10sp</dimen>
    <dimen name="text_size_sm">12sp</dimen>
    <dimen name="text_size_md">14sp</dimen>
    <dimen name="text_size_lg">16sp</dimen>
    <dimen name="text_size_xl">18sp</dimen>
    <dimen name="text_size_xxl">20sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_title">32sp</dimen>

    <!-- Icon Sizes -->
    <dimen name="icon_size_xs">16dp</dimen>
    <dimen name="icon_size_sm">20dp</dimen>
    <dimen name="icon_size_md">24dp</dimen>
    <dimen name="icon_size_lg">32dp</dimen>
    <dimen name="icon_size_xl">48dp</dimen>
    <dimen name="icon_size_xxl">64dp</dimen>

    <!-- App Bar -->
    <dimen name="app_bar_height">64dp</dimen>
    <dimen name="app_bar_elevation">0dp</dimen>

    <!-- Bottom Navigation -->
    <dimen name="bottom_nav_height">80dp</dimen>
    <dimen name="bottom_nav_elevation">0dp</dimen>

    <!-- FAB -->
    <dimen name="fab_size">56dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- Dialog -->
    <dimen name="dialog_corner_radius">24dp</dimen>
    <dimen name="dialog_margin">24dp</dimen>
    <dimen name="dialog_padding">24dp</dimen>

    <!-- Bottom Sheet -->
    <dimen name="bottom_sheet_corner_radius">24dp</dimen>
    <dimen name="bottom_sheet_peek_height">200dp</dimen>

    <!-- Settings -->
    <dimen name="settings_item_height">64dp</dimen>
    <dimen name="settings_item_padding">16dp</dimen>
    <dimen name="settings_icon_size">24dp</dimen>

    <!-- Voice Visualization -->
    <dimen name="voice_wave_height">4dp</dimen>
    <dimen name="voice_wave_spacing">2dp</dimen>
    <dimen name="voice_wave_container_height">60dp</dimen>

    <!-- Accessibility -->
    <dimen name="min_touch_target">48dp</dimen>
    <dimen name="accessibility_focus_border">2dp</dimen>

    <!-- Animation -->
    <dimen name="animation_offset">16dp</dimen>
    <dimen name="ripple_radius">24dp</dimen>

    <!-- Status Bar -->
    <dimen name="status_bar_height">24dp</dimen>
    <dimen name="navigation_bar_height">48dp</dimen>

    <!-- Landscape specific -->
    <dimen name="landscape_margin">32dp</dimen>
    <dimen name="landscape_padding">24dp</dimen>
</resources>
