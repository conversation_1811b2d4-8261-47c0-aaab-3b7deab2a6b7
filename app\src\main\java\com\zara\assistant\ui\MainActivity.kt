package com.zara.assistant.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.zara.assistant.presentation.theme.ZaraTheme
import com.zara.assistant.services.WakeWordService
import com.zara.assistant.ui.screens.*
import com.zara.assistant.utils.DirectPermissionManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * MainActivity - The stunning entry point of Zara
 * Features:
 * - Beautiful theme setup
 * - Permission management
 * - Navigation setup
 * - Service initialization
 */
@AndroidEntryPoint
class MainActivity : FragmentActivity() {

    @Inject
    lateinit var directPermissionManager: DirectPermissionManager

    private var hasRequiredPermissions by mutableStateOf(false)
    private var showPermissionScreen by mutableStateOf(false)
    
    // Permission launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        hasRequiredPermissions = permissions.all { it.value }
        if (!hasRequiredPermissions) {
            showPermissionScreen = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize DirectPermissionManager
        directPermissionManager.initialize(this)

        // Check permissions
        checkPermissions()
        
        setContent {
            ZaraTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ZaraApp()
                }
            }
        }
    }

    @Composable
    private fun ZaraApp() {
        val navController = rememberNavController()
        
        // Show permission screen if needed
        if (showPermissionScreen) {
            PermissionsScreen(
                onPermissionsGranted = {
                    showPermissionScreen = false
                    hasRequiredPermissions = true
                    startServices()
                },
                onOpenSettings = {
                    openAppSettings()
                },
                directPermissionManager = directPermissionManager
            )
            return
        }
        
        // Show onboarding for first-time users
        var showOnboarding by remember { mutableStateOf(isFirstLaunch()) }
        
        if (showOnboarding) {
            OnboardingScreen(
                onComplete = {
                    showOnboarding = false
                    setFirstLaunchComplete()
                    if (hasRequiredPermissions) {
                        startServices()
                    }
                }
            )
            return
        }
        
        // Main navigation
        NavHost(
            navController = navController,
            startDestination = "main"
        ) {
            composable("main") {
                MainScreen(
                    onNavigateToSettings = {
                        navController.navigate("settings")
                    }
                )
            }
            
            composable("settings") {
                SettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onNavigateToVoiceSettings = {
                        navController.navigate("voice_settings")
                    },
                    onNavigateToPermissions = {
                        navController.navigate("permissions")
                    },
                    onNavigateToAbout = {
                        navController.navigate("about")
                    },
                    onNavigateToCommands = {
                        navController.navigate("commands")
                    }
                )
            }
            
            composable("voice_settings") {
                VoiceSettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable("permissions") {
                PermissionsScreen(
                    onPermissionsGranted = {
                        navController.popBackStack()
                        startServices()
                    },
                    onOpenSettings = {
                        openAppSettings()
                    },
                    directPermissionManager = directPermissionManager
                )
            }
            
            composable("about") {
                AboutScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable("commands") {
                CommandsHelpScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
        
        // Start services when permissions are granted
        LaunchedEffect(hasRequiredPermissions) {
            if (hasRequiredPermissions && !showOnboarding) {
                delay(1000) // Small delay for smooth transition
                startServices()
            }
        }
    }

    /**
     * Check required permissions
     */
    private fun checkPermissions() {
        val requiredPermissions = mutableListOf<String>().apply {
            add(Manifest.permission.RECORD_AUDIO)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                add(Manifest.permission.POST_NOTIFICATIONS)
            }
            
            // Optional permissions that enhance functionality
            add(Manifest.permission.CALL_PHONE)
            add(Manifest.permission.READ_PHONE_STATE)
            add(Manifest.permission.READ_CONTACTS)
        }
        
        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isNotEmpty()) {
            permissionLauncher.launch(missingPermissions.toTypedArray())
        } else {
            hasRequiredPermissions = true
        }
    }

    /**
     * Start background services
     */
    private fun startServices() {
        try {
            // Start wake word detection service
            WakeWordService.startWakeWordDetection(this)
            
        } catch (e: Exception) {
            // Handle service start error
            android.util.Log.e("MainActivity", "Failed to start services: ${e.message}")
        }
    }

    /**
     * Open app settings
     */
    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = android.net.Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    /**
     * Check if this is the first launch
     */
    private fun isFirstLaunch(): Boolean {
        val prefs = getSharedPreferences("zara_prefs", MODE_PRIVATE)
        return prefs.getBoolean("first_launch", true)
    }

    /**
     * Mark first launch as complete
     */
    private fun setFirstLaunchComplete() {
        val prefs = getSharedPreferences("zara_prefs", MODE_PRIVATE)
        prefs.edit().putBoolean("first_launch", false).apply()
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // Stop services
        try {
            WakeWordService.stopWakeWordDetection(this)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error stopping services: ${e.message}")
        }
    }

    override fun onResume() {
        super.onResume()
        
        // Re-check permissions when returning to app
        if (!hasRequiredPermissions) {
            checkPermissions()
        }
    }
}
