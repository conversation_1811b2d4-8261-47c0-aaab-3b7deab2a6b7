package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\u001aJ\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a \u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H\u0003\u001a,\u0010\u000f\u001a\u00020\u00012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0006\u0010\u0011\u001a\u00020\u0012H\u0007\u00a8\u0006\u0013"}, d2 = {"PermissionActions", "", "allPermissionsGranted", "", "isGrantingPermissions", "grantingProgress", "", "onGrantPermissions", "Lkotlin/Function0;", "onOpenSettings", "onContinue", "PermissionCard", "item", "Lcom/zara/assistant/ui/screens/PermissionItem;", "isGranted", "PermissionsScreen", "onPermissionsGranted", "directPermissionManager", "Lcom/zara/assistant/utils/DirectPermissionManager;", "app_debug"})
public final class PermissionsScreenKt {
    
    /**
     * Perfect Permissions Screen - Beautiful permission management
     * Features:
     * - Real-time permission status
     * - Direct permission granting
     * - Beautiful animations
     * - Intelligent permission flow
     * - Live status updates
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PermissionsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionsGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOpenSettings, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.DirectPermissionManager directPermissionManager) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PermissionCard(com.zara.assistant.ui.screens.PermissionItem item, boolean isGranted, boolean isGrantingPermissions) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PermissionActions(boolean allPermissionsGranted, boolean isGrantingPermissions, float grantingProgress, kotlin.jvm.functions.Function0<kotlin.Unit> onGrantPermissions, kotlin.jvm.functions.Function0<kotlin.Unit> onOpenSettings, kotlin.jvm.functions.Function0<kotlin.Unit> onContinue) {
    }
}