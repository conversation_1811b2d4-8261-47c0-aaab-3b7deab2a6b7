package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceSettingsScreen(
    onNavigateBack: () -> Unit
) {
    var speechRate by remember { mutableStateOf(1.0f) }
    var speechPitch by remember { mutableStateOf(1.0f) }
    var selectedVoice by remember { mutableStateOf("JennyNeural") }
    var wakeWordEnabled by remember { mutableStateOf(true) }
    var autoListenEnabled by remember { mutableStateOf(true) }
    
    val infiniteTransition = rememberInfiniteTransition(label = "voice_settings_animation")
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f, targetValue = 1f,
        animationSpec = infiniteRepeatable(animation = tween(8000, easing = LinearEasing), repeatMode = RepeatMode.Reverse),
        label = "gradient_offset"
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6B73FF)),
                    start = androidx.compose.ui.geometry.Offset(gradientOffset * 1000f, gradientOffset * 1000f),
                    end = androidx.compose.ui.geometry.Offset((1f - gradientOffset) * 1000f, (1f - gradientOffset) * 1000f)
                )
            )
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            // Top bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .statusBarsPadding(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                NeumorphismButton(
                    onClick = onNavigateBack,
                    modifier = Modifier.size(48.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(imageVector = Icons.Default.ArrowBack, contentDescription = "Back", tint = MaterialTheme.colorScheme.primary)
                }
                Spacer(modifier = Modifier.width(16.dp))
                Text(
                    text = "Voice Settings",
                    style = MaterialTheme.typography.headlineMedium.copy(fontWeight = FontWeight.Bold, color = Color.White)
                )
            }
            
            LazyColumn(
                modifier = Modifier.fillMaxSize().padding(horizontal = 24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(20.dp)) {
                            Text(
                                text = "Speech Settings",
                                style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.SemiBold, color = MaterialTheme.colorScheme.primary)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Text("Speech Rate: ${String.format("%.1f", speechRate)}x")
                            Slider(
                                value = speechRate,
                                onValueChange = { speechRate = it },
                                valueRange = 0.5f..2.0f,
                                modifier = Modifier.fillMaxWidth()
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            Text("Speech Pitch: ${String.format("%.1f", speechPitch)}x")
                            Slider(
                                value = speechPitch,
                                onValueChange = { speechPitch = it },
                                valueRange = 0.5f..2.0f,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
                
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(20.dp)) {
                            Text(
                                text = "Voice Selection",
                                style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.SemiBold, color = MaterialTheme.colorScheme.primary)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            listOf("JennyNeural", "GuyNeural", "Hindi Female", "Hindi Male").forEach { voice ->
                                Row(
                                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = selectedVoice == voice,
                                        onClick = { selectedVoice = voice }
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(voice)
                                }
                            }
                        }
                    }
                }
                
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(20.dp)) {
                            Text(
                                text = "Wake Word & Auto-Listen",
                                style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.SemiBold, color = MaterialTheme.colorScheme.primary)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("\"Hey Zara\" Wake Word")
                                Switch(checked = wakeWordEnabled, onCheckedChange = { wakeWordEnabled = it })
                            }
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("30-Second Auto-Listen")
                                Switch(checked = autoListenEnabled, onCheckedChange = { autoListenEnabled = it })
                            }
                        }
                    }
                }
                
                item { Spacer(modifier = Modifier.height(32.dp)) }
            }
        }
    }
}
