package com.zara.assistant.services

import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.Intent
import android.hardware.camera2.CameraManager
import android.media.AudioManager
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.SystemControlResult
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * System Control Manager - Device and system control operations
 * Handles WiFi, Bluetooth, volume, brightness, and other system settings
 */
@Singleton
class SystemControlManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val performanceUtils: PerformanceUtils
) {
    
    companion object {
        private const val TAG = "SystemControlManager"
    }
    
    private val wifiManager: WifiManager by lazy {
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    
    private val audioManager: AudioManager by lazy {
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }
    
    private val cameraManager: CameraManager by lazy {
        context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    }
    
    private var isFlashlightOn = false

    /**
     * Set WiFi enabled/disabled
     */
    suspend fun setWifiEnabled(enabled: Boolean): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_WiFi")
            
            Log.d(TAG, "📶 Setting WiFi: ${if (enabled) "ON" else "OFF"}")
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ requires user interaction for WiFi toggle
                val intent = Intent(Settings.Panel.ACTION_WIFI)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                
                performanceUtils.endMeasurement(measurementId)
                
                SystemControlResult(
                    success = true,
                    action = "wifi_panel",
                    message = "WiFi settings panel opened. Please toggle WiFi manually."
                )
            } else {
                // Legacy method for older Android versions
                @Suppress("DEPRECATION")
                val success = wifiManager.setWifiEnabled(enabled)
                
                performanceUtils.endMeasurement(measurementId)
                
                if (success) {
                    SystemControlResult(
                        success = true,
                        action = "wifi_toggle",
                        message = "WiFi ${if (enabled) "enabled" else "disabled"} successfully"
                    )
                } else {
                    SystemControlResult(
                        success = false,
                        action = "wifi_toggle",
                        message = "Failed to ${if (enabled) "enable" else "disable"} WiFi"
                    )
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error controlling WiFi: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "wifi_error",
                message = "Error controlling WiFi: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set Bluetooth enabled/disabled
     */
    suspend fun setBluetoothEnabled(enabled: Boolean): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_Bluetooth")
            
            Log.d(TAG, "📱 Setting Bluetooth: ${if (enabled) "ON" else "OFF"}")
            
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            
            if (bluetoothAdapter == null) {
                performanceUtils.endMeasurement(measurementId)
                return@withContext SystemControlResult(
                    success = false,
                    action = "bluetooth_unavailable",
                    message = "Bluetooth not available on this device"
                )
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ requires user interaction
                val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                
                performanceUtils.endMeasurement(measurementId)
                
                SystemControlResult(
                    success = true,
                    action = "bluetooth_settings",
                    message = "Bluetooth settings opened. Please toggle Bluetooth manually."
                )
            } else {
                // Legacy method
                val success = if (enabled) {
                    bluetoothAdapter.enable()
                } else {
                    bluetoothAdapter.disable()
                }
                
                performanceUtils.endMeasurement(measurementId)
                
                if (success) {
                    SystemControlResult(
                        success = true,
                        action = "bluetooth_toggle",
                        message = "Bluetooth ${if (enabled) "enabled" else "disabled"} successfully"
                    )
                } else {
                    SystemControlResult(
                        success = false,
                        action = "bluetooth_toggle",
                        message = "Failed to ${if (enabled) "enable" else "disable"} Bluetooth"
                    )
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error controlling Bluetooth: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "bluetooth_error",
                message = "Error controlling Bluetooth: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set mobile data enabled/disabled
     */
    suspend fun setMobileDataEnabled(enabled: Boolean): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_MobileData")
            
            Log.d(TAG, "📶 Setting Mobile Data: ${if (enabled) "ON" else "OFF"}")
            
            // Modern Android requires user interaction for mobile data
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
            
            performanceUtils.endMeasurement(measurementId)
            
            SystemControlResult(
                success = true,
                action = "mobile_data_settings",
                message = "Mobile data settings opened. Please toggle mobile data manually."
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error controlling mobile data: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "mobile_data_error",
                message = "Error controlling mobile data: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set Do Not Disturb enabled/disabled
     */
    suspend fun setDoNotDisturbEnabled(enabled: Boolean): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_DND")
            
            Log.d(TAG, "🔕 Setting Do Not Disturb: ${if (enabled) "ON" else "OFF"}")
            
            // Open DND settings for user interaction
            val intent = Intent(Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
            
            performanceUtils.endMeasurement(measurementId)
            
            SystemControlResult(
                success = true,
                action = "dnd_settings",
                message = "Do Not Disturb settings opened. Please configure DND manually."
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error controlling DND: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "dnd_error",
                message = "Error controlling Do Not Disturb: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set flashlight enabled/disabled
     */
    suspend fun setFlashlightEnabled(enabled: Boolean): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_Flashlight")
            
            Log.d(TAG, "🔦 Setting Flashlight: ${if (enabled) "ON" else "OFF"}")
            
            val cameraId = cameraManager.cameraIdList.firstOrNull()
            
            if (cameraId == null) {
                performanceUtils.endMeasurement(measurementId)
                return@withContext SystemControlResult(
                    success = false,
                    action = "flashlight_unavailable",
                    message = "Flashlight not available on this device"
                )
            }
            
            cameraManager.setTorchMode(cameraId, enabled)
            isFlashlightOn = enabled
            
            performanceUtils.endMeasurement(measurementId)
            
            SystemControlResult(
                success = true,
                action = "flashlight_toggle",
                message = "Flashlight ${if (enabled) "turned on" else "turned off"}"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error controlling flashlight: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "flashlight_error",
                message = "Error controlling flashlight: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set volume level (0-100)
     */
    suspend fun setVolume(level: Int): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_Volume")
            
            val clampedLevel = level.coerceIn(0, 100)
            Log.d(TAG, "🔊 Setting volume: $clampedLevel%")
            
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
            val targetVolume = (maxVolume * clampedLevel / 100.0).toInt()
            
            audioManager.setStreamVolume(
                AudioManager.STREAM_MUSIC,
                targetVolume,
                AudioManager.FLAG_SHOW_UI
            )
            
            performanceUtils.endMeasurement(measurementId)
            
            SystemControlResult(
                success = true,
                action = "volume_set",
                message = "Volume set to $clampedLevel%"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error setting volume: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "volume_error",
                message = "Error setting volume: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Set brightness level (0-100)
     */
    suspend fun setBrightness(level: Int): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_Brightness")
            
            val clampedLevel = level.coerceIn(0, 100)
            Log.d(TAG, "☀️ Setting brightness: $clampedLevel%")
            
            // Open brightness settings for user adjustment
            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
            
            performanceUtils.endMeasurement(measurementId)
            
            SystemControlResult(
                success = true,
                action = "brightness_settings",
                message = "Display settings opened. Please adjust brightness to $clampedLevel%."
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error setting brightness: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "brightness_error",
                message = "Error setting brightness: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Open app by package name
     */
    suspend fun openApp(packageName: String): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_OpenApp")
            
            Log.d(TAG, "📱 Opening app: $packageName")
            
            val intent = context.packageManager.getLaunchIntentForPackage(packageName)
            
            if (intent != null) {
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                
                performanceUtils.endMeasurement(measurementId)
                
                SystemControlResult(
                    success = true,
                    action = "app_opened",
                    message = "App opened successfully"
                )
            } else {
                performanceUtils.endMeasurement(measurementId)
                
                SystemControlResult(
                    success = false,
                    action = "app_not_found",
                    message = "App not found or not installed"
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening app: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "app_open_error",
                message = "Error opening app: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Open app by name (searches installed apps)
     */
    suspend fun openAppByName(appName: String): SystemControlResult = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("SystemControl_OpenAppByName")
            
            Log.d(TAG, "🔍 Searching for app: $appName")
            
            val packageManager = context.packageManager
            val installedApps = packageManager.getInstalledApplications(0)
            
            val matchingApp = installedApps.find { appInfo ->
                val appLabel = packageManager.getApplicationLabel(appInfo).toString()
                appLabel.contains(appName, ignoreCase = true)
            }
            
            if (matchingApp != null) {
                val intent = packageManager.getLaunchIntentForPackage(matchingApp.packageName)
                if (intent != null) {
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    context.startActivity(intent)
                    
                    performanceUtils.endMeasurement(measurementId)
                    
                    SystemControlResult(
                        success = true,
                        action = "app_opened_by_name",
                        message = "Opened ${packageManager.getApplicationLabel(matchingApp)}"
                    )
                } else {
                    performanceUtils.endMeasurement(measurementId)
                    
                    SystemControlResult(
                        success = false,
                        action = "app_no_launch_intent",
                        message = "Cannot launch $appName"
                    )
                }
            } else {
                performanceUtils.endMeasurement(measurementId)
                
                SystemControlResult(
                    success = false,
                    action = "app_not_found_by_name",
                    message = "App '$appName' not found"
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening app by name: ${e.message}", e)
            SystemControlResult(
                success = false,
                action = "app_open_by_name_error",
                message = "Error opening app: ${e.message}",
                errorCode = Constants.ErrorCodes.SYSTEM_CONTROL_ERROR.toString()
            )
        }
    }

    /**
     * Close app by name (requires accessibility service)
     */
    suspend fun closeAppByName(appName: String): SystemControlResult = withContext(Dispatchers.IO) {
        // This would require accessibility service integration
        // For now, return a message indicating the limitation
        SystemControlResult(
            success = false,
            action = "close_app_limitation",
            message = "Closing apps requires accessibility service. Please close $appName manually."
        )
    }

    /**
     * Perform global accessibility action
     */
    suspend fun performGlobalAction(action: Int): SystemControlResult = withContext(Dispatchers.IO) {
        // This would be implemented in the accessibility service
        // For now, return a placeholder
        val actionName = when (action) {
            android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_BACK -> "Back"
            android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_HOME -> "Home"
            android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_RECENTS -> "Recent Apps"
            android.accessibilityservice.AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS -> "Notifications"
            else -> "Unknown"
        }
        
        SystemControlResult(
            success = false,
            action = "accessibility_required",
            message = "$actionName action requires accessibility service to be enabled."
        )
    }

    /**
     * Take screenshot
     */
    suspend fun takeScreenshot(): SystemControlResult = withContext(Dispatchers.IO) {
        SystemControlResult(
            success = false,
            action = "screenshot_limitation",
            message = "Screenshot requires accessibility service or system permissions."
        )
    }

    /**
     * Lock screen
     */
    suspend fun lockScreen(): SystemControlResult = withContext(Dispatchers.IO) {
        SystemControlResult(
            success = false,
            action = "lock_limitation",
            message = "Screen lock requires device admin permissions."
        )
    }
}
