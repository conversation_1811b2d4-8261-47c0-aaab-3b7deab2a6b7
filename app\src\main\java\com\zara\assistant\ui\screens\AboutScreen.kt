package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(
    onNavigateBack: () -> Unit
) {
    val infiniteTransition = rememberInfiniteTransition(label = "about_animation")
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f, targetValue = 1f,
        animationSpec = infiniteRepeatable(animation = tween(8000, easing = LinearEasing), repeatMode = RepeatMode.Reverse),
        label = "gradient_offset"
    )
    
    val logoScale by infiniteTransition.animateFloat(
        initialValue = 0.9f, targetValue = 1.1f,
        animationSpec = infiniteRepeatable(animation = tween(2000, easing = FastOutSlowInEasing), repeatMode = RepeatMode.Reverse),
        label = "logo_scale"
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6B73FF)),
                    start = androidx.compose.ui.geometry.Offset(gradientOffset * 1000f, gradientOffset * 1000f),
                    end = androidx.compose.ui.geometry.Offset((1f - gradientOffset) * 1000f, (1f - gradientOffset) * 1000f)
                )
            )
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            // Top bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .statusBarsPadding(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                NeumorphismButton(
                    onClick = onNavigateBack,
                    modifier = Modifier.size(48.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(imageVector = Icons.Default.ArrowBack, contentDescription = "Back", tint = MaterialTheme.colorScheme.primary)
                }
                Spacer(modifier = Modifier.width(16.dp))
                Text(
                    text = "About Zara",
                    style = MaterialTheme.typography.headlineMedium.copy(fontWeight = FontWeight.Bold, color = Color.White)
                )
            }
            
            LazyColumn(
                modifier = Modifier.fillMaxSize().padding(horizontal = 24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 16.dp) {
                        Column(
                            modifier = Modifier.padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            NeumorphismCard(
                                modifier = Modifier.size(100.dp).scale(logoScale),
                                shape = CircleShape,
                                elevation = 20.dp
                            ) {
                                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                                    Icon(
                                        imageVector = Icons.Default.Mic,
                                        contentDescription = "Zara Logo",
                                        modifier = Modifier.size(50.dp),
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(24.dp))
                            Text(
                                text = "Zara",
                                style = MaterialTheme.typography.headlineLarge.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 48.sp,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            )
                            Text(
                                text = "AI Voice Assistant",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Version 1.0.0",
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    color = MaterialTheme.colorScheme.primary
                                )
                            )
                        }
                    }
                }
                
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(24.dp)) {
                            Text(
                                text = "Features",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontWeight = FontWeight.SemiBold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            val features = listOf(
                                "🎤 Ultra-fast voice recognition with Azure STT",
                                "🗣️ Natural speech with Azure TTS (JennyNeural/GuyNeural)",
                                "🧠 Dual AI intelligence (Cohere + Perplexity)",
                                "🌍 Hindi and English support with auto-detection",
                                "⚡ 30-second auto-listening with \"Hey Zara\" wake word",
                                "📱 Complete device control (WiFi, Bluetooth, apps)",
                                "🔔 Smart notification management",
                                "🎨 Beautiful neumorphism UI design",
                                "🔒 Privacy-first with encrypted storage",
                                "♿ Advanced accessibility integration"
                            )
                            
                            features.forEach { feature ->
                                Text(
                                    text = feature,
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        lineHeight = 20.sp
                                    ),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
                
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(24.dp)) {
                            Text(
                                text = "Technology Stack",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontWeight = FontWeight.SemiBold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            val technologies = listOf(
                                "🏗️ Kotlin & Jetpack Compose",
                                "🎯 Hilt Dependency Injection",
                                "🌊 Coroutines & Flow",
                                "☁️ Azure Cognitive Services",
                                "🤖 Cohere AI API",
                                "🔍 Perplexity AI API",
                                "🎙️ Picovoice Porcupine",
                                "♿ Android Accessibility Services",
                                "🔐 Android Security & Encryption"
                            )
                            
                            technologies.forEach { tech ->
                                Text(
                                    text = tech,
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        lineHeight = 20.sp
                                    ),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
                
                item {
                    NeumorphismCard(modifier = Modifier.fillMaxWidth(), elevation = 12.dp) {
                        Column(modifier = Modifier.padding(24.dp)) {
                            Text(
                                text = "Privacy & Security",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontWeight = FontWeight.SemiBold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Zara is designed with privacy as a core principle. All voice processing uses secure cloud services, conversation history is encrypted locally, and you have full control over your data. No voice data is stored permanently without your explicit consent.",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    lineHeight = 22.sp,
                                    textAlign = TextAlign.Justify
                                )
                            )
                        }
                    }
                }
                
                item { Spacer(modifier = Modifier.height(32.dp)) }
            }
        }
    }
}
