// Generated by Da<PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ZaraVoiceService_MembersInjector implements MembersInjector<ZaraVoiceService> {
  private final Provider<ZaraSTTService> zaraSTTServiceProvider;

  private final Provider<ZaraTTSService> zaraTTSServiceProvider;

  private final Provider<LocalCommandProcessor> localCommandProcessorProvider;

  private final Provider<AIOrchestrationService> aiOrchestrationServiceProvider;

  public ZaraVoiceService_MembersInjector(Provider<ZaraSTTService> zaraSTTServiceProvider,
      Provider<ZaraTTSService> zaraTTSServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider) {
    this.zaraSTTServiceProvider = zaraSTTServiceProvider;
    this.zaraTTSServiceProvider = zaraTTSServiceProvider;
    this.localCommandProcessorProvider = localCommandProcessorProvider;
    this.aiOrchestrationServiceProvider = aiOrchestrationServiceProvider;
  }

  public static MembersInjector<ZaraVoiceService> create(
      Provider<ZaraSTTService> zaraSTTServiceProvider,
      Provider<ZaraTTSService> zaraTTSServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider) {
    return new ZaraVoiceService_MembersInjector(zaraSTTServiceProvider, zaraTTSServiceProvider, localCommandProcessorProvider, aiOrchestrationServiceProvider);
  }

  @Override
  public void injectMembers(ZaraVoiceService instance) {
    injectZaraSTTService(instance, zaraSTTServiceProvider.get());
    injectZaraTTSService(instance, zaraTTSServiceProvider.get());
    injectLocalCommandProcessor(instance, localCommandProcessorProvider.get());
    injectAiOrchestrationService(instance, aiOrchestrationServiceProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.ZaraVoiceService.zaraSTTService")
  public static void injectZaraSTTService(ZaraVoiceService instance,
      ZaraSTTService zaraSTTService) {
    instance.zaraSTTService = zaraSTTService;
  }

  @InjectedFieldSignature("com.zara.assistant.services.ZaraVoiceService.zaraTTSService")
  public static void injectZaraTTSService(ZaraVoiceService instance,
      ZaraTTSService zaraTTSService) {
    instance.zaraTTSService = zaraTTSService;
  }

  @InjectedFieldSignature("com.zara.assistant.services.ZaraVoiceService.localCommandProcessor")
  public static void injectLocalCommandProcessor(ZaraVoiceService instance,
      LocalCommandProcessor localCommandProcessor) {
    instance.localCommandProcessor = localCommandProcessor;
  }

  @InjectedFieldSignature("com.zara.assistant.services.ZaraVoiceService.aiOrchestrationService")
  public static void injectAiOrchestrationService(ZaraVoiceService instance,
      AIOrchestrationService aiOrchestrationService) {
    instance.aiOrchestrationService = aiOrchestrationService;
  }
}
