package com.zara.assistant.domain.repository;

/**
 * Voice Repository Interface - Defines voice-related operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u000f\bf\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\tH&J\u000e\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\tH&J\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\tH&J\u001c\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u000e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\tH&J$\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\u0006\u0010\u0015\u001a\u00020\rH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0016\u0010\u0017J0\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0019\u001a\u00020\u001a2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ$\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u001b\u001a\u00020\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010 J\u001c\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010\u0011J\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b$\u0010\u0011J$\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010&\u001a\u00020\u000fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\'\u0010(\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006)"}, d2 = {"Lcom/zara/assistant/domain/repository/VoiceRepository;", "", "enableWakeWord", "Lkotlin/Result;", "", "enabled", "enableWakeWord-gIAlu-s", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getListeningState", "Lkotlinx/coroutines/flow/Flow;", "getSpeakingState", "getVoiceCommands", "", "Lcom/zara/assistant/domain/model/VoiceCommand;", "getVoiceSettings", "Lcom/zara/assistant/domain/repository/VoiceSettings;", "getVoiceSettings-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWakeWordState", "processVoiceCommand", "Lcom/zara/assistant/domain/model/AIResponse;", "command", "processVoiceCommand-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "speak", "text", "", "language", "speak-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startListening", "startListening-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "stopListening", "stopListening-IoAF18A", "stopSpeaking", "stopSpeaking-IoAF18A", "updateVoiceSettings", "settings", "updateVoiceSettings-gIAlu-s", "(Lcom/zara/assistant/domain/repository/VoiceSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface VoiceRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> getListeningState();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> getSpeakingState();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.domain.model.VoiceCommand>> getVoiceCommands();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> getWakeWordState();
    
    /**
     * Voice Repository Interface - Defines voice-related operations
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}