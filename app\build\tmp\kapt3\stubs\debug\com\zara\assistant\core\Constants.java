package com.zara.assistant.core;

/**
 * Zara Constants - Configuration for Ultra-Fast AI Voice Assistant
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\r\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u000b\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/zara/assistant/core/Constants;", "", "()V", "APP_NAME", "", "APP_VERSION", "AI", "ApiKeys", "Database", "ErrorCodes", "Network", "Permissions", "Preferences", "SystemControl", "UI", "Voice", "WakeWord", "app_debug"})
public final class Constants {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String APP_NAME = "Zara";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String APP_VERSION = "1.0.0";
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.core.Constants INSTANCE = null;
    
    private Constants() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0007\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/zara/assistant/core/Constants$AI;", "", "()V", "AI_CONNECT_TIMEOUT_MS", "", "AI_REQUEST_TIMEOUT_MS", "COHERE_BASE_URL", "", "COHERE_MODEL", "MAX_TOKENS", "", "PERPLEXITY_BASE_URL", "PERPLEXITY_MODEL", "PERSONALITY_CASUAL", "PERSONALITY_FRIENDLY", "PERSONALITY_PROFESSIONAL", "TEMPERATURE", "", "TOP_P", "app_debug"})
    public static final class AI {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String COHERE_BASE_URL = "https://api.cohere.ai/";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERPLEXITY_BASE_URL = "https://api.perplexity.ai/";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String COHERE_MODEL = "command-r-plus";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERPLEXITY_MODEL = "llama-3.1-sonar-small-128k-online";
        public static final int MAX_TOKENS = 500;
        public static final float TEMPERATURE = 0.7F;
        public static final float TOP_P = 0.9F;
        public static final long AI_REQUEST_TIMEOUT_MS = 10000L;
        public static final long AI_CONNECT_TIMEOUT_MS = 5000L;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERSONALITY_PROFESSIONAL = "professional";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERSONALITY_FRIENDLY = "friendly";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERSONALITY_CASUAL = "casual";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.AI INSTANCE = null;
        
        private AI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/zara/assistant/core/Constants$ApiKeys;", "", "()V", "AZURE_SPEECH_KEY", "", "AZURE_SPEECH_REGION", "COHERE_API_KEY", "PERPLEXITY_API_KEY", "PICOVOICE_ACCESS_KEY", "app_debug"})
    public static final class ApiKeys {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PICOVOICE_ACCESS_KEY = "/993iNPpHeHkreiaNbKwtQvwF/jeDDzFHk4K0H/FEECUqMnA5lkz6w==";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AZURE_SPEECH_KEY = "7Yd0EE1VC9fJgqET6D2K4q6FR45hc0LdHMpkrXE9t2AdDqjUgRQzJQQJ99BGACYeBjFXJ3w3AAAYACOGKJDV";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AZURE_SPEECH_REGION = "eastus";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String COHERE_API_KEY = "OkmxGVOm5PiciSYdRGeC7n6EhHpamiASce5aAbwh";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERPLEXITY_API_KEY = "plx-ZugJI0TK68UX6jYyg3QHYmhKJZThtQRWvAiZ3kgQ9YndxlF7";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.ApiKeys INSTANCE = null;
        
        private ApiKeys() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/core/Constants$Database;", "", "()V", "DATABASE_NAME", "", "DATABASE_VERSION", "", "TABLE_COMMAND_HISTORY", "TABLE_CONVERSATIONS", "TABLE_PERSONALIZATION", "TABLE_USER_PREFERENCES", "app_debug"})
    public static final class Database {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DATABASE_NAME = "zara_database";
        public static final int DATABASE_VERSION = 1;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TABLE_CONVERSATIONS = "conversations";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TABLE_USER_PREFERENCES = "user_preferences";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TABLE_COMMAND_HISTORY = "command_history";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TABLE_PERSONALIZATION = "personalization";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Database INSTANCE = null;
        
        private Database() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/core/Constants$ErrorCodes;", "", "()V", "AI_PROCESSING_ERROR", "", "NETWORK_ERROR", "PERMISSION_ERROR", "SYSTEM_CONTROL_ERROR", "TTS_ERROR", "VOICE_RECOGNITION_ERROR", "WAKE_WORD_ERROR", "app_debug"})
    public static final class ErrorCodes {
        public static final int VOICE_RECOGNITION_ERROR = 1001;
        public static final int TTS_ERROR = 1002;
        public static final int AI_PROCESSING_ERROR = 1003;
        public static final int PERMISSION_ERROR = 1004;
        public static final int NETWORK_ERROR = 1005;
        public static final int WAKE_WORD_ERROR = 1006;
        public static final int SYSTEM_CONTROL_ERROR = 1007;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.ErrorCodes INSTANCE = null;
        
        private ErrorCodes() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/core/Constants$Network;", "", "()V", "CACHE_SIZE", "", "CONNECT_TIMEOUT", "READ_TIMEOUT", "WRITE_TIMEOUT", "app_debug"})
    public static final class Network {
        public static final long CONNECT_TIMEOUT = 10L;
        public static final long READ_TIMEOUT = 30L;
        public static final long WRITE_TIMEOUT = 15L;
        public static final long CACHE_SIZE = 10485760L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Network INSTANCE = null;
        
        private Network() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/core/Constants$Permissions;", "", "()V", "BIND_ACCESSIBILITY_SERVICE", "", "BIND_NOTIFICATION_LISTENER_SERVICE", "CALL_PHONE", "READ_CONTACTS", "READ_PHONE_STATE", "RECORD_AUDIO", "SYSTEM_ALERT_WINDOW", "app_debug"})
    public static final class Permissions {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String RECORD_AUDIO = "android.permission.RECORD_AUDIO";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CALL_PHONE = "android.permission.CALL_PHONE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String READ_PHONE_STATE = "android.permission.READ_PHONE_STATE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String READ_CONTACTS = "android.permission.READ_CONTACTS";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SYSTEM_ALERT_WINDOW = "android.permission.SYSTEM_ALERT_WINDOW";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BIND_NOTIFICATION_LISTENER_SERVICE = "android.permission.BIND_NOTIFICATION_LISTENER_SERVICE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BIND_ACCESSIBILITY_SERVICE = "android.permission.BIND_ACCESSIBILITY_SERVICE";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Permissions INSTANCE = null;
        
        private Permissions() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/zara/assistant/core/Constants$Preferences;", "", "()V", "KEY_AUTO_LISTEN_ENABLED", "", "KEY_CONVERSATION_HISTORY", "KEY_DARK_MODE", "KEY_FIRST_LAUNCH", "KEY_HAPTIC_FEEDBACK", "KEY_PERSONALITY_MODE", "KEY_PREFERRED_LANGUAGE", "KEY_PREFERRED_VOICE", "KEY_SPEECH_PITCH", "KEY_SPEECH_RATE", "KEY_VOICE_FEEDBACK", "KEY_WAKE_WORD_ENABLED", "KEY_WAKE_WORD_SENSITIVITY", "PREFS_NAME", "app_debug"})
    public static final class Preferences {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PREFS_NAME = "zara_preferences";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_FIRST_LAUNCH = "first_launch";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_WAKE_WORD_ENABLED = "wake_word_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_AUTO_LISTEN_ENABLED = "auto_listen_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_PREFERRED_LANGUAGE = "preferred_language";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_SPEECH_RATE = "speech_rate";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_SPEECH_PITCH = "speech_pitch";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_PERSONALITY_MODE = "personality_mode";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_CONVERSATION_HISTORY = "conversation_history";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_VOICE_FEEDBACK = "voice_feedback";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_HAPTIC_FEEDBACK = "haptic_feedback";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_WAKE_WORD_SENSITIVITY = "wake_word_sensitivity";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_PREFERRED_VOICE = "preferred_voice";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEY_DARK_MODE = "dark_mode";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Preferences INSTANCE = null;
        
        private Preferences() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0013\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/zara/assistant/core/Constants$SystemControl;", "", "()V", "ACTION_BACK", "", "ACTION_HOME", "ACTION_NOTIFICATIONS", "ACTION_QUICK_SETTINGS", "ACTION_RECENT", "CATEGORY_APP", "CATEGORY_COMMUNICATION", "CATEGORY_DEVICE", "CATEGORY_INFORMATION", "CATEGORY_MEDIA", "CONTROL_BLUETOOTH", "CONTROL_BRIGHTNESS", "CONTROL_DND", "CONTROL_FLASHLIGHT", "CONTROL_HOTSPOT", "CONTROL_MOBILE_DATA", "CONTROL_NFC", "CONTROL_VOLUME", "CONTROL_WIFI", "app_debug"})
    public static final class SystemControl {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CATEGORY_DEVICE = "device";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CATEGORY_APP = "app";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CATEGORY_COMMUNICATION = "communication";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CATEGORY_MEDIA = "media";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CATEGORY_INFORMATION = "information";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_WIFI = "wifi";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_BLUETOOTH = "bluetooth";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_MOBILE_DATA = "mobile_data";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_HOTSPOT = "hotspot";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_NFC = "nfc";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_DND = "do_not_disturb";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_BRIGHTNESS = "brightness";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_VOLUME = "volume";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONTROL_FLASHLIGHT = "flashlight";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACTION_BACK = "back";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACTION_HOME = "home";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACTION_RECENT = "recent";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACTION_NOTIFICATIONS = "notifications";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACTION_QUICK_SETTINGS = "quick_settings";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.SystemControl INSTANCE = null;
        
        private SystemControl() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/zara/assistant/core/Constants$UI;", "", "()V", "ANIMATION_DURATION_LONG", "", "ANIMATION_DURATION_MEDIUM", "ANIMATION_DURATION_SHORT", "NEUMORPHISM_BLUR_RADIUS", "", "NEUMORPHISM_CORNER_RADIUS", "NEUMORPHISM_ELEVATION", "VOICE_ANIMATION_DURATION", "VOICE_BARS_COUNT", "", "VOICE_BAR_SPACING", "VOICE_BAR_WIDTH", "app_debug"})
    public static final class UI {
        public static final long ANIMATION_DURATION_SHORT = 200L;
        public static final long ANIMATION_DURATION_MEDIUM = 400L;
        public static final long ANIMATION_DURATION_LONG = 600L;
        public static final int VOICE_BARS_COUNT = 32;
        public static final float VOICE_BAR_WIDTH = 4.0F;
        public static final float VOICE_BAR_SPACING = 2.0F;
        public static final long VOICE_ANIMATION_DURATION = 100L;
        public static final float NEUMORPHISM_ELEVATION = 8.0F;
        public static final float NEUMORPHISM_CORNER_RADIUS = 16.0F;
        public static final float NEUMORPHISM_BLUR_RADIUS = 20.0F;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.UI INSTANCE = null;
        
        private UI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0010\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/zara/assistant/core/Constants$Voice;", "", "()V", "AUDIO_ENCODING", "", "AUTO_LISTEN_TIMEOUT_MS", "", "AZURE_SPEECH_REGION_DEFAULT", "", "CHANNEL_CONFIG", "DEFAULT_SPEECH_PITCH", "", "DEFAULT_SPEECH_RATE", "LANGUAGE_AUTO", "LANGUAGE_ENGLISH", "LANGUAGE_HINDI", "LISTENING_TIMEOUT_MS", "MAX_SPEECH_PITCH", "MAX_SPEECH_RATE", "MIN_SPEECH_PITCH", "MIN_SPEECH_RATE", "SAMPLE_RATE", "TTS_TIMEOUT_MS", "VOICE_GUY_NEURAL", "VOICE_HINDI_FEMALE", "VOICE_HINDI_MALE", "VOICE_JENNY_NEURAL", "app_debug"})
    public static final class Voice {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AZURE_SPEECH_REGION_DEFAULT = "eastus";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LANGUAGE_AUTO = "auto";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LANGUAGE_ENGLISH = "en-US";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LANGUAGE_HINDI = "hi-IN";
        public static final float DEFAULT_SPEECH_RATE = 1.0F;
        public static final float DEFAULT_SPEECH_PITCH = 1.0F;
        public static final float MIN_SPEECH_RATE = 0.5F;
        public static final float MAX_SPEECH_RATE = 2.0F;
        public static final float MIN_SPEECH_PITCH = 0.5F;
        public static final float MAX_SPEECH_PITCH = 2.0F;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_JENNY_NEURAL = "en-US-JennyNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_GUY_NEURAL = "en-US-GuyNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_HINDI_FEMALE = "hi-IN-SwaraNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_HINDI_MALE = "hi-IN-MadhurNeural";
        public static final long LISTENING_TIMEOUT_MS = 30000L;
        public static final long AUTO_LISTEN_TIMEOUT_MS = 30000L;
        public static final long TTS_TIMEOUT_MS = 15000L;
        public static final int SAMPLE_RATE = 16000;
        public static final int AUDIO_ENCODING = 16;
        public static final int CHANNEL_CONFIG = 1;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Voice INSTANCE = null;
        
        private Voice() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/zara/assistant/core/Constants$WakeWord;", "", "()V", "DEFAULT_SENSITIVITY", "", "MAX_SENSITIVITY", "MIN_SENSITIVITY", "WAKE_WORD_FILE", "", "app_debug"})
    public static final class WakeWord {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WAKE_WORD_FILE = "hey_zara_android.ppn";
        public static final float DEFAULT_SENSITIVITY = 0.5F;
        public static final float MIN_SENSITIVITY = 0.1F;
        public static final float MAX_SENSITIVITY = 1.0F;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.WakeWord INSTANCE = null;
        
        private WakeWord() {
            super();
        }
    }
}