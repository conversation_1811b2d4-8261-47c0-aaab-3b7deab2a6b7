{"logs": [{"outputFile": "com.zara.assistant.app-mergeReleaseResources-83:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76f8ef271629eccbc242979ade06d14f\\transformed\\core-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "29,30,31,32,33,34,35,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2830,2928,3030,3128,3232,3336,3438,12526", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "2923,3025,3123,3227,3331,3433,3550,12622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,12366", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,12448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10f8b8bca7dd0586b8f58168a92d3396\\transformed\\biometric-1.1.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,380,512,643,771,901,1035,1136,1271,1404", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "159,252,375,507,638,766,896,1030,1131,1266,1399,1522"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3744,3947,4225,4348,4480,4611,4739,4869,5003,5104,5239,5372", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "3848,4035,4343,4475,4606,4734,4864,4998,5099,5234,5367,5490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3093826e12afe4e65cbc1e4cc9cbb375\\transformed\\jetified-foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12895,12985", "endColumns": "89,91", "endOffsets": "12980,13072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c1dbd90dee8ec981efff90fba80648e\\transformed\\jetified-ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,991,1062,1143,1229,1302,1382,1452", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,986,1057,1138,1224,1297,1377,1447,1565"}, "to": {"startLines": "36,37,39,41,42,53,54,111,112,113,114,115,116,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3555,3656,3853,4040,4139,5495,5572,11879,11971,12056,12128,12199,12280,12453,12627,12707,12777", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "3651,3739,3942,4134,4220,5567,5659,11966,12051,12123,12194,12275,12361,12521,12702,12772,12890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a81feca8f62c4cb5733ffcc676aee47\\transformed\\jetified-material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4678,4769,4878,4966,5049,5146,5250,5343,5440,5528,5636,5733,5835,5973,6063,6171", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4673,4764,4873,4961,5044,5141,5245,5338,5435,5523,5631,5728,5830,5968,6058,6166,6265"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5664,5783,5903,6024,6144,6243,6341,6456,6601,6721,6859,6944,7044,7137,7235,7352,7479,7584,7719,7853,7994,8164,8299,8422,8549,8677,8771,8869,8990,9118,9215,9318,9427,9566,9711,9820,9920,10005,10098,10193,10287,10378,10487,10575,10658,10755,10859,10952,11049,11137,11245,11342,11444,11582,11672,11780", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "5778,5898,6019,6139,6238,6336,6451,6596,6716,6854,6939,7039,7132,7230,7347,7474,7579,7714,7848,7989,8159,8294,8417,8544,8672,8766,8864,8985,9113,9210,9313,9422,9561,9706,9815,9915,10000,10093,10188,10282,10373,10482,10570,10653,10750,10854,10947,11044,11132,11240,11337,11439,11577,11667,11775,11874"}}]}]}