kotlin.Annotationandroid.os.Parcelable2kotlinx.serialization.internal.GeneratedSerializerandroid.app.Servicekotlin.Enumandroid.app.Application$androidx.work.Configuration.Provider8android.service.notification.NotificationListenerService1android.accessibilityservice.AccessibilityService%android.app.admin.DeviceAdminReceiverandroid.os.Binder#androidx.activity.ComponentActivityandroidx.lifecycle.ViewModel-com.zara.assistant.services.NotificationEvent&androidx.fragment.app.FragmentActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               