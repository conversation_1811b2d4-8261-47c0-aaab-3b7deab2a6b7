// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.AIOrchestrationService;
import com.zara.assistant.services.CohereAIService;
import com.zara.assistant.services.PerplexityAIService;
import com.zara.assistant.utils.ApiKeyManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideAIOrchestrationServiceFactory implements Factory<AIOrchestrationService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  private final Provider<CohereAIService> cohereAIServiceProvider;

  private final Provider<PerplexityAIService> perplexityAIServiceProvider;

  public ServiceModule_ProvideAIOrchestrationServiceFactory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider,
      Provider<CohereAIService> cohereAIServiceProvider,
      Provider<PerplexityAIService> perplexityAIServiceProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
    this.cohereAIServiceProvider = cohereAIServiceProvider;
    this.perplexityAIServiceProvider = perplexityAIServiceProvider;
  }

  @Override
  public AIOrchestrationService get() {
    return provideAIOrchestrationService(contextProvider.get(), apiKeyManagerProvider.get(), performanceUtilsProvider.get(), cohereAIServiceProvider.get(), perplexityAIServiceProvider.get());
  }

  public static ServiceModule_ProvideAIOrchestrationServiceFactory create(
      Provider<Context> contextProvider, Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider,
      Provider<CohereAIService> cohereAIServiceProvider,
      Provider<PerplexityAIService> perplexityAIServiceProvider) {
    return new ServiceModule_ProvideAIOrchestrationServiceFactory(contextProvider, apiKeyManagerProvider, performanceUtilsProvider, cohereAIServiceProvider, perplexityAIServiceProvider);
  }

  public static AIOrchestrationService provideAIOrchestrationService(Context context,
      ApiKeyManager apiKeyManager, PerformanceUtils performanceUtils,
      CohereAIService cohereAIService, PerplexityAIService perplexityAIService) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideAIOrchestrationService(context, apiKeyManager, performanceUtils, cohereAIService, perplexityAIService));
  }
}
