package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.presentation.components.*
import com.zara.assistant.presentation.theme.neumorphismColors
import com.zara.assistant.ui.viewmodel.MainViewModel

/**
 * Mind-Blowing Main Screen - The stunning centerpiece of Zara
 * Features:
 * - Gorgeous gradient backgrounds
 * - Floating neumorphism cards
 * - Smooth animations
 * - Interactive voice button
 * - Real-time status updates
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onNavigateToSettings: () -> Unit,
    viewModel: MainViewModel = hiltViewModel()
) {
    val voiceState by viewModel.voiceState.collectAsState()
    val lastResponse by viewModel.lastResponse.collectAsState()
    val isListening by viewModel.isListening.collectAsState()
    val isSpeaking by viewModel.isSpeaking.collectAsState()
    
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "main_screen_animation")
    
    // Background gradient animation
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "gradient_offset"
    )
    
    // Floating animation for cards
    val floatingOffset by infiniteTransition.animateFloat(
        initialValue = -5f,
        targetValue = 5f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "floating_offset"
    )
    
    // Scale animation for entrance
    var isVisible by remember { mutableStateOf(false) }
    val scaleAnimation by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "entrance_scale"
    )
    
    LaunchedEffect(Unit) {
        isVisible = true
    }
    
    // Dynamic background based on voice state
    val backgroundColors = when (voiceState) {
        VoiceState.LISTENING -> listOf(
            Color(0xFF667EEA),
            Color(0xFF764BA2),
            Color(0xFF6B73FF)
        )
        VoiceState.SPEAKING -> listOf(
            Color(0xFF11998E),
            Color(0xFF38EF7D),
            Color(0xFF00C9FF)
        )
        VoiceState.PROCESSING -> listOf(
            Color(0xFF8360C3),
            Color(0xFF2EBFA5),
            Color(0xFF9C27B0)
        )
        VoiceState.ERROR -> listOf(
            Color(0xFFFF5722),
            Color(0xFFFF9800),
            Color(0xFFE91E63)
        )
        else -> listOf(
            Color(0xFF2196F3),
            Color(0xFF21CBF3),
            Color(0xFF2196F3)
        )
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = backgroundColors,
                    start = androidx.compose.ui.geometry.Offset(
                        gradientOffset * 1000f,
                        gradientOffset * 1000f
                    ),
                    end = androidx.compose.ui.geometry.Offset(
                        (1f - gradientOffset) * 1000f,
                        (1f - gradientOffset) * 1000f
                    )
                )
            )
            .scale(scaleAnimation)
    ) {
        // Top bar with settings
        TopAppBar(
            title = {
                Text(
                    text = "Zara",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                )
            },
            actions = {
                NeumorphismButton(
                    onClick = onNavigateToSettings,
                    modifier = Modifier.size(48.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "Settings",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            ),
            modifier = Modifier.statusBarsPadding()
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Spacer(modifier = Modifier.height(60.dp))
            
            // Welcome message
            WelcomeSection(
                modifier = Modifier.offset(y = floatingOffset.dp),
                voiceState = voiceState
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Main voice button
            MindBlowingVoiceButton(
                voiceState = voiceState,
                onClick = {
                    when (voiceState) {
                        VoiceState.IDLE -> viewModel.startListening()
                        VoiceState.LISTENING -> viewModel.stopListening()
                        VoiceState.SPEAKING -> viewModel.stopSpeaking()
                        else -> { /* Do nothing during processing */ }
                    }
                },
                modifier = Modifier.offset(y = (-floatingOffset).dp),
                size = 220f
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Status and response section
            StatusSection(
                modifier = Modifier.offset(y = floatingOffset.dp),
                voiceState = voiceState,
                lastResponse = lastResponse,
                isListening = isListening,
                isSpeaking = isSpeaking
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Quick actions
            QuickActionsSection(
                modifier = Modifier.offset(y = (-floatingOffset * 0.5f).dp),
                onAction = { action ->
                    viewModel.executeQuickAction(action)
                }
            )
            
            Spacer(modifier = Modifier.height(40.dp))
        }
        
        // Floating particles effect
        if (voiceState == VoiceState.LISTENING || voiceState == VoiceState.SPEAKING) {
            FloatingParticles(
                modifier = Modifier.fillMaxSize(),
                particleColor = Color.White.copy(alpha = 0.3f)
            )
        }
    }
}

/**
 * Welcome Section with dynamic messaging
 */
@Composable
private fun WelcomeSection(
    modifier: Modifier = Modifier,
    voiceState: VoiceState
) {
    val welcomeMessage = when (voiceState) {
        VoiceState.LISTENING -> "I'm listening..."
        VoiceState.SPEAKING -> "Speaking..."
        VoiceState.PROCESSING -> "Thinking..."
        VoiceState.ERROR -> "Something went wrong"
        else -> "Hi! I'm Zara, your AI assistant"
    }
    
    val subMessage = when (voiceState) {
        VoiceState.IDLE -> "Tap the button or say \"Hey Zara\" to start"
        VoiceState.LISTENING -> "Speak your command clearly"
        VoiceState.SPEAKING -> "Playing response..."
        VoiceState.PROCESSING -> "Processing your request..."
        VoiceState.ERROR -> "Please try again"
    }
    
    NeumorphismCard(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = 12.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = welcomeMessage,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                ),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = subMessage,
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                ),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Status Section showing current state and responses
 */
@Composable
private fun StatusSection(
    modifier: Modifier = Modifier,
    voiceState: VoiceState,
    lastResponse: String?,
    isListening: Boolean,
    isSpeaking: Boolean
) {
    if (lastResponse != null) {
        NeumorphismCard(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            elevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    VoiceStateIndicator(
                        voiceState = voiceState,
                        size = 16.dp
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = "Zara's Response",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = lastResponse,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        lineHeight = 24.sp
                    )
                )
            }
        }
    }
}

/**
 * Quick Actions Section
 */
@Composable
private fun QuickActionsSection(
    modifier: Modifier = Modifier,
    onAction: (String) -> Unit
) {
    val quickActions = listOf(
        "Turn on WiFi" to "wifi_on",
        "What's the weather?" to "weather",
        "Open Settings" to "settings",
        "Take a screenshot" to "screenshot"
    )
    
    NeumorphismCard(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = 6.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Quick Actions",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            quickActions.chunked(2).forEach { rowActions ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    rowActions.forEach { (label, action) ->
                        NeumorphismButton(
                            onClick = { onAction(action) },
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = label,
                                style = MaterialTheme.typography.labelMedium,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
                
                if (rowActions != quickActions.chunked(2).last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * Floating Particles Effect
 */
@Composable
private fun FloatingParticles(
    modifier: Modifier = Modifier,
    particleColor: Color,
    particleCount: Int = 15
) {
    // Implementation would create floating particle animation
    // This is a placeholder for the particle system
    Box(modifier = modifier)
}
