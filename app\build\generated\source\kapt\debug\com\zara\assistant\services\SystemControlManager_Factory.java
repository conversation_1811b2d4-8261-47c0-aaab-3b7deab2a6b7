// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemControlManager_Factory implements Factory<SystemControlManager> {
  private final Provider<Context> contextProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public SystemControlManager_Factory(Provider<Context> contextProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public SystemControlManager get() {
    return newInstance(contextProvider.get(), performanceUtilsProvider.get());
  }

  public static SystemControlManager_Factory create(Provider<Context> contextProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new SystemControlManager_Factory(contextProvider, performanceUtilsProvider);
  }

  public static SystemControlManager newInstance(Context context,
      PerformanceUtils performanceUtils) {
    return new SystemControlManager(context, performanceUtils);
  }
}
