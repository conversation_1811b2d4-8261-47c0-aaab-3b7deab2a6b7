package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * AI Repository Interface - Handles AI processing and responses
 */
interface AIRepository {
    
    /**
     * Process voice command with appropriate AI source
     */
    suspend fun processCommand(command: VoiceCommand): Result<AIResponse>
    
    /**
     * Get conversational response from Cohere
     */
    suspend fun getCohereResponse(
        message: String,
        conversationHistory: List<Conversation> = emptyList(),
        personalityMode: String = "friendly"
    ): Result<AIResponse>
    
    /**
     * Get information response from Perplexity
     */
    suspend fun getPerplexityResponse(
        query: String,
        context: String? = null
    ): Result<AIResponse>
    
    /**
     * Classify command category and intent
     */
    suspend fun classifyCommand(text: String, language: String): CommandClassification
    
    /**
     * Get conversation history
     */
    fun getConversationHistory(limit: Int = 10): Flow<List<Conversation>>
    
    /**
     * Save conversation
     */
    suspend fun saveConversation(conversation: Conversation)
    
    /**
     * Clear conversation history
     */
    suspend fun clearConversationHistory()
    
    /**
     * Get AI health status
     */
    suspend fun getAIHealthStatus(): AIHealthStatus
}







/**
 * Command Classification Result
 */
data class CommandClassification(
    val category: CommandCategory,
    val intent: CommandIntent,
    val confidence: Float,
    val parameters: Map<String, String> = emptyMap()
)

/**
 * AI Health Status
 */
data class AIHealthStatus(
    val cohereAvailable: Boolean,
    val perplexityAvailable: Boolean,
    val localProcessingAvailable: Boolean,
    val averageResponseTime: Long,
    val errorRate: Float
)
