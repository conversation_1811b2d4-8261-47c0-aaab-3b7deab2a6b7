// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WakeWordService_MembersInjector implements MembersInjector<WakeWordService> {
  private final Provider<ZaraVoiceService> zaraVoiceServiceProvider;

  public WakeWordService_MembersInjector(Provider<ZaraVoiceService> zaraVoiceServiceProvider) {
    this.zaraVoiceServiceProvider = zaraVoiceServiceProvider;
  }

  public static MembersInjector<WakeWordService> create(
      Provider<ZaraVoiceService> zaraVoiceServiceProvider) {
    return new WakeWordService_MembersInjector(zaraVoiceServiceProvider);
  }

  @Override
  public void injectMembers(WakeWordService instance) {
    injectZaraVoiceService(instance, zaraVoiceServiceProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.WakeWordService.zaraVoiceService")
  public static void injectZaraVoiceService(WakeWordService instance,
      ZaraVoiceService zaraVoiceService) {
    instance.zaraVoiceService = zaraVoiceService;
  }
}
