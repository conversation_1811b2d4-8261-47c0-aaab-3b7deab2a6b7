package com.zara.assistant.domain.repository;

/**
 * System Control Repository Interface - Defines system control operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0010\bf\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u001c\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u000bJ$\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\r\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\bJ\"\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u000bJ\u001c\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u000bJ\"\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00100\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0018\u0010\u000bJ\u001c\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u000bJ\u000e\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00140\u001cH&J$\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001e\u0010\bJ$\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010 \u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b!\u0010\bJ$\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010#\u001a\u00020$H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010&J,\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\r\u001a\u00020\u00062\u0006\u0010(\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010*J$\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010,\u001a\u00020-H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b.\u0010/J$\u00100\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u00101\u001a\u00020$H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u0010&J$\u00103\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010,\u001a\u00020-H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u0010/J$\u00105\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010,\u001a\u00020-H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010/J$\u00107\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u00101\u001a\u00020$H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b8\u0010&J$\u00109\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010,\u001a\u00020-H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010/J\u001c\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b<\u0010\u000b\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006="}, d2 = {"Lcom/zara/assistant/domain/repository/SystemControlRepository;", "", "closeApp", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/SystemControlResult;", "packageName", "", "closeApp-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "dismissAllNotifications", "dismissAllNotifications-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "dismissNotification", "key", "dismissNotification-gIAlu-s", "getActiveNotifications", "", "Lcom/zara/assistant/domain/repository/NotificationData;", "getActiveNotifications-IoAF18A", "getDeviceState", "Lcom/zara/assistant/domain/model/DeviceState;", "getDeviceState-IoAF18A", "getInstalledApps", "Lcom/zara/assistant/domain/model/AppInfo;", "getInstalledApps-IoAF18A", "lockScreen", "lockScreen-IoAF18A", "observeDeviceState", "Lkotlinx/coroutines/flow/Flow;", "openApp", "openApp-gIAlu-s", "openAppByName", "appName", "openAppByName-gIAlu-s", "performGlobalAction", "action", "", "performGlobalAction-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "replyToNotification", "message", "replyToNotification-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setBluetoothEnabled", "enabled", "", "setBluetoothEnabled-gIAlu-s", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setBrightness", "level", "setBrightness-gIAlu-s", "setFlashlightEnabled", "setFlashlightEnabled-gIAlu-s", "setMobileDataEnabled", "setMobileDataEnabled-gIAlu-s", "setVolume", "setVolume-gIAlu-s", "setWifiEnabled", "setWifiEnabled-gIAlu-s", "takeScreenshot", "takeScreenshot-IoAF18A", "app_debug"})
public abstract interface SystemControlRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.DeviceState> observeDeviceState();
}