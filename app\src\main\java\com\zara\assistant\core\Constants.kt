package com.zara.assistant.core

/**
 * Zara Constants - Configuration for Ultra-Fast AI Voice Assistant
 */
object Constants {
    
    // App Information
    const val APP_NAME = "Zara"
    const val APP_VERSION = "1.0.0"
    
    // Voice Processing
    object Voice {
        // Azure Speech Services
        const val AZURE_SPEECH_REGION_DEFAULT = "eastus"
        
        // Supported Languages
        const val LANGUAGE_AUTO = "auto"
        const val LANGUAGE_ENGLISH = "en-US"
        const val LANGUAGE_HINDI = "hi-IN"
        
        // Voice Settings
        const val DEFAULT_SPEECH_RATE = 1.0f
        const val DEFAULT_SPEECH_PITCH = 1.0f
        const val MIN_SPEECH_RATE = 0.5f
        const val MAX_SPEECH_RATE = 2.0f
        const val MIN_SPEECH_PITCH = 0.5f
        const val MAX_SPEECH_PITCH = 2.0f
        
        // Azure TTS Voices
        const val VOICE_JENNY_NEURAL = "en-US-JennyNeural"
        const val VOICE_GUY_NEURAL = "en-US-GuyNeural"
        const val VOICE_HINDI_FEMALE = "hi-IN-SwaraNeural"
        const val VOICE_HINDI_MALE = "hi-IN-MadhurNeural"
        
        // Timeouts (30 seconds as per user preference)
        const val LISTENING_TIMEOUT_MS = 30000L
        const val AUTO_LISTEN_TIMEOUT_MS = 30000L
        const val TTS_TIMEOUT_MS = 15000L
        
        // Audio Configuration
        const val SAMPLE_RATE = 16000
        const val AUDIO_ENCODING = 16
        const val CHANNEL_CONFIG = 1
    }
    
    // Wake Word Detection
    object WakeWord {
        const val WAKE_WORD_FILE = "hey_zara_android.ppn"
        const val DEFAULT_SENSITIVITY = 0.5f
        const val MIN_SENSITIVITY = 0.1f
        const val MAX_SENSITIVITY = 1.0f
    }
    
    // AI Processing
    object AI {
        // API Endpoints
        const val COHERE_BASE_URL = "https://api.cohere.ai/"
        const val PERPLEXITY_BASE_URL = "https://api.perplexity.ai/"
        
        // Model Configuration
        const val COHERE_MODEL = "command-r-plus"
        const val PERPLEXITY_MODEL = "llama-3.1-sonar-small-128k-online"
        
        // Response Settings
        const val MAX_TOKENS = 500
        const val TEMPERATURE = 0.7f
        const val TOP_P = 0.9f
        
        // Timeouts
        const val AI_REQUEST_TIMEOUT_MS = 10000L
        const val AI_CONNECT_TIMEOUT_MS = 5000L
        
        // Personality Modes
        const val PERSONALITY_PROFESSIONAL = "professional"
        const val PERSONALITY_FRIENDLY = "friendly"
        const val PERSONALITY_CASUAL = "casual"
    }
    
    // System Control
    object SystemControl {
        // Command Categories
        const val CATEGORY_DEVICE = "device"
        const val CATEGORY_APP = "app"
        const val CATEGORY_COMMUNICATION = "communication"
        const val CATEGORY_MEDIA = "media"
        const val CATEGORY_INFORMATION = "information"
        
        // Device Controls
        const val CONTROL_WIFI = "wifi"
        const val CONTROL_BLUETOOTH = "bluetooth"
        const val CONTROL_MOBILE_DATA = "mobile_data"
        const val CONTROL_HOTSPOT = "hotspot"
        const val CONTROL_NFC = "nfc"
        const val CONTROL_DND = "do_not_disturb"
        const val CONTROL_BRIGHTNESS = "brightness"
        const val CONTROL_VOLUME = "volume"
        const val CONTROL_FLASHLIGHT = "flashlight"
        
        // Accessibility Actions
        const val ACTION_BACK = "back"
        const val ACTION_HOME = "home"
        const val ACTION_RECENT = "recent"
        const val ACTION_NOTIFICATIONS = "notifications"
        const val ACTION_QUICK_SETTINGS = "quick_settings"
    }
    
    // Database
    object Database {
        const val DATABASE_NAME = "zara_database"
        const val DATABASE_VERSION = 1
        
        // Table Names
        const val TABLE_CONVERSATIONS = "conversations"
        const val TABLE_USER_PREFERENCES = "user_preferences"
        const val TABLE_COMMAND_HISTORY = "command_history"
        const val TABLE_PERSONALIZATION = "personalization"
    }
    
    // Preferences
    object Preferences {
        const val PREFS_NAME = "zara_preferences"
        
        // Keys
        const val KEY_FIRST_LAUNCH = "first_launch"
        const val KEY_WAKE_WORD_ENABLED = "wake_word_enabled"
        const val KEY_AUTO_LISTEN_ENABLED = "auto_listen_enabled"
        const val KEY_PREFERRED_LANGUAGE = "preferred_language"
        const val KEY_SPEECH_RATE = "speech_rate"
        const val KEY_SPEECH_PITCH = "speech_pitch"
        const val KEY_PERSONALITY_MODE = "personality_mode"
        const val KEY_CONVERSATION_HISTORY = "conversation_history"
        const val KEY_VOICE_FEEDBACK = "voice_feedback"
        const val KEY_HAPTIC_FEEDBACK = "haptic_feedback"
        const val KEY_WAKE_WORD_SENSITIVITY = "wake_word_sensitivity"
        const val KEY_PREFERRED_VOICE = "preferred_voice"
        const val KEY_DARK_MODE = "dark_mode"
    }
    
    // Permissions
    object Permissions {
        const val RECORD_AUDIO = android.Manifest.permission.RECORD_AUDIO
        const val CALL_PHONE = android.Manifest.permission.CALL_PHONE
        const val READ_PHONE_STATE = android.Manifest.permission.READ_PHONE_STATE
        const val READ_CONTACTS = android.Manifest.permission.READ_CONTACTS
        const val SYSTEM_ALERT_WINDOW = android.Manifest.permission.SYSTEM_ALERT_WINDOW
        const val BIND_NOTIFICATION_LISTENER_SERVICE = "android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
        const val BIND_ACCESSIBILITY_SERVICE = "android.permission.BIND_ACCESSIBILITY_SERVICE"
    }
    
    // Network
    object Network {
        const val CONNECT_TIMEOUT = 10L
        const val READ_TIMEOUT = 30L
        const val WRITE_TIMEOUT = 15L
        const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
    }
    
    // UI
    object UI {
        // Animation Durations
        const val ANIMATION_DURATION_SHORT = 200L
        const val ANIMATION_DURATION_MEDIUM = 400L
        const val ANIMATION_DURATION_LONG = 600L
        
        // Voice Visualization
        const val VOICE_BARS_COUNT = 32
        const val VOICE_BAR_WIDTH = 4f
        const val VOICE_BAR_SPACING = 2f
        const val VOICE_ANIMATION_DURATION = 100L
        
        // Neumorphism
        const val NEUMORPHISM_ELEVATION = 8f
        const val NEUMORPHISM_CORNER_RADIUS = 16f
        const val NEUMORPHISM_BLUR_RADIUS = 20f
    }
    
    // Error Codes
    object ErrorCodes {
        const val VOICE_RECOGNITION_ERROR = 1001
        const val TTS_ERROR = 1002
        const val AI_PROCESSING_ERROR = 1003
        const val PERMISSION_ERROR = 1004
        const val NETWORK_ERROR = 1005
        const val WAKE_WORD_ERROR = 1006
        const val SYSTEM_CONTROL_ERROR = 1007
    }

    // API Keys from local.properties
    object ApiKeys {
        const val PICOVOICE_ACCESS_KEY = "/993iNPpHeHkreiaNbKwtQvwF/jeDDzFHk4K0H/FEECUqMnA5lkz6w=="
        const val AZURE_SPEECH_KEY = "7Yd0EE1VC9fJgqET6D2K4q6FR45hc0LdHMpkrXE9t2AdDqjUgRQzJQQJ99BGACYeBjFXJ3w3AAAYACOGKJDV"
        const val AZURE_SPEECH_REGION = "eastus"
        const val COHERE_API_KEY = "OkmxGVOm5PiciSYdRGeC7n6EhHpamiASce5aAbwh"
        const val PERPLEXITY_API_KEY = "plx-ZugJI0TK68UX6jYyg3QHYmhKJZThtQRWvAiZ3kgQ9YndxlF7"
    }
}
