Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x1FE8E
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210286019, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DD0  000210068E24 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0B0  00021006A225 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD214E0000 ntdll.dll
7FFD1FD50000 KERNEL32.DLL
7FFD1EBB0000 KERNELBASE.dll
7FFD20A10000 USER32.dll
7FFD1F190000 win32u.dll
7FFD20D20000 GDI32.dll
7FFD1E9C0000 gdi32full.dll
7FFD1EB00000 msvcp_win.dll
7FFD1EFA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD20C60000 advapi32.dll
7FFD1FE30000 msvcrt.dll
7FFD20860000 sechost.dll
7FFD20740000 RPCRT4.dll
7FFD1DD30000 CRYPTBASE.DLL
7FFD1F0F0000 bcryptPrimitives.dll
7FFD1F300000 IMM32.DLL
