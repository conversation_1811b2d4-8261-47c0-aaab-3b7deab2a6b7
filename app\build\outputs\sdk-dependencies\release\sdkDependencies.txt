# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "v@;\261Y\237n*\334\233\2007.\373&t\270\313\355\342\260\361\346\350\\J\224\341\f{\224\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.6.1"
  }
  digests {
    sha256: "6\207\270\210H^\255\023\030\321\027\023\374\325\357gJ\254\251m\305)\271\231\0200\273\356\302S\221#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.6.1"
  }
  digests {
    sha256: "m\242\032\325\002\322\030h\304\270I\f\250\345\020\230w\\\233\2429}\211mrNxi\276\320\260;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\273\365C\036\177\204\344\203E=\253\265\246\315]\030\365SP-\245/\f\213\351\252\225U6\300\035\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.7.0"
  }
  digests {
    sha256: "\343\033\320\351+\320\263\0336\004H\312Z[\200\374\037cP{\264\356\216\233\323\000CtI\321x\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.1.0-rc01"
  }
  digests {
    sha256: "K5\346\340\362\335&\032m\300]}\030y\265j\032\204Su\241\244\226\3433\202\360\215\233\344IB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\257\330\273\265Xy\343\347]\353\030\235\331\rU\001\347YHqR\351\v\3648\372\256\273\000\341\350\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.6.1"
  }
  digests {
    sha256: "_\310\r\263\023\016\3756\204\235\244\266\305\314Q\004\235`\006\205k,\230\365\263q\032\377C<H\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.6.1"
  }
  digests {
    sha256: "&|Ko\200\330s\"\357\bXSX\335\377z57C\363\033\314\215\361PV\267i\262%\036#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.6.1"
  }
  digests {
    sha256: "]z\237\277\212\177vX\342\336o\020\267\210\177\363\326\3265\373sa/\b\211n\240S\341\220J\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\275\n\240\217MT%\266nx\320\311\vj\375+\031\223T\002\315W\207\345\367\241>D6G\020\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\037BM\v\332\3331\241\035\342\026c\205]3\250\204\206/]\207?[vW\034\243\332(\277[\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\037\320\341\234\363%/\363t/3\264#\261\016yx\355\272\333\271wr\275\034\366\033\206E\fN\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\a\243R\\j\327\334s\211\245\341\347B\361\351\211+\303\255As8\325\255^\267c\250\367\246\036\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.6.1"
  }
  digests {
    sha256: "@2\223h\205Dd\263\333\245\323\222\217\213\2756PA\206>\205\024\231\257\310\307U\224d\276\376^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-graphics"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-graphics-android"
    version: "1.6.1"
  }
  digests {
    sha256: "s\325\312\030{\"\346\036\211\310\343\221)\270Bq\234Z\fK\003\237U\266\343B& \361n\n\025"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\365}H\212\361\207\025j\336Q\301\0177\355x\245dyR]\235rr\027\023\245\304\024\3254\372\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.1"
  }
  digests {
    sha256: "\202\260G\200\355\242\033\n^\352\002GT\025\210z\034\255\3736\2137\022!\256\264\a\003\300g\3638"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.02.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\335\3557\212\220\360V \264=\214T\273I\202A)\356\220\266\377\213\006\276(\236\234\374\273\343\177\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.6.1"
  }
  digests {
    sha256: "c\260\023~\306h\267\324\357\372>\203\360\300\316\246\202\227\363i\300\022\256\354\004\350\v\345K\302RG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.6.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\305\273\251\035\254\037\346jO\226c\v\003\314\370\210\361\\Z\373\376d\037\033\322\304f\334\213\025s^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.2.0"
  }
  digests {
    sha256: "\020\372\315\350\366o\221\326\032H\310\337\354\343\264\223\3260\202/1\034\322\006C\2424\026\344\361\352\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.4"
  }
  digests {
    sha256: "\211\a\242\201\271\377\035\223b\237\247\365A\3415N$\333\361\215\216.\310&_q>\200\264\203s\350"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.4"
  }
  digests {
    sha256: "\027lI\377#\245\3032L\201\314\230\333z}\004\330\240\235\032\203w\305\365\016w\r\234\247 \346="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.4"
  }
  digests {
    sha256: "W\324\350\261\356F\251\357\220\177?\227\260.=Vxb5\254/-\225k\257F\374\330\341\371\177o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.4"
  }
  digests {
    sha256: "\320\261F\371\261\252\331i\332P\25264T>\203\360\272F\025\211O\331$\301\214wk\350\031\2673"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.4"
  }
  digests {
    sha256: ")a\3262`s\016\016,\v\325\207Y\226\306cN|\361c\366q\375\360UNGV|\227\no"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-android"
    version: "2.48"
  }
  digests {
    sha256: "$v\217Z\'\306\r\310\261\211G(\206\177\304\307\r\372X\243\204\331\377`\311!,\344\212!7\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger"
    version: "2.48"
  }
  digests {
    sha256: "\037\242&\322\264\240,\310\tP\372MI\244\2425\314\216\316\324\231\265\201\3745\212UDj\203\365y"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger-lint-aar"
    version: "2.48"
  }
  digests {
    sha256: "\346!\301\003\277a\264Vo?}\274\200U\253\314\356\361X\201\001\235\024\233\206z\331\355\364\a\250\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-core"
    version: "2.48"
  }
  digests {
    sha256: "\312\202\2453\v7%/=\336\321\345\316\005\367D\263T\253\021\266\307\344\233\332\301\b\226\305\0313a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation-compose"
    version: "1.1.0"
  }
  digests {
    sha256: "\"1\324\\\331|\r\354\344L\270Z\270.r\336\275\277\366\277\244)I>\214td\235\022\031/E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation"
    version: "1.1.0"
  }
  digests {
    sha256: "F\241\321$\030\367zuO\311\034\\(\254\277\004\034x\366N\240\203\361\372\2234\235\216C\366\352\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-work"
    version: "1.1.0"
  }
  digests {
    sha256: "G\326\267\227==D\241COP\tQ\364q\363q\2612\316\242\244G\232\177RO\3261\343ry"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-common"
    version: "1.1.0"
  }
  digests {
    sha256: "k\t\b\037\325\241\r\345i\264\276dc\261F\366\004[\261\211\031\2770\232\377\006I\200\2661\003q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite"
    version: "2.14.0"
  }
  digests {
    sha256: "p\235\270\037\277\272F\033\036\322~\234\036\203\201|-|\306\004i\aN\2526\310\217\270\333\352H\206"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite-api"
    version: "2.14.0"
  }
  digests {
    sha256: "\035\037\377\376\235\025\226\230\202\310V$\2320\030\314\322\031\223\031\341\225\246;{Xi\237\310\313\203o"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite-support"
    version: "0.4.4"
  }
  digests {
    sha256: "\034\035\020\270\316\310^\373\350\002NuVA\313\004O\037\020\342\004\333w?\206\2142}9\217\305\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite-metadata"
    version: "0.4.4"
  }
  digests {
    sha256: "\214Kk\256\341fQ{\317F\"\217D\020d\017\366\314\375\200\001\264\006G\003\214\232\220<\361^}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "2.5.8"
  }
  digests {
    sha256: "\275e]\2455\337\036\353\226\374\210t\320]\017p) \235\351P\226\344\370K\320|fh\212!\r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.flatbuffers"
    artifactId: "flatbuffers-java"
    version: "1.12.0"
  }
  digests {
    sha256: "?\214\b\213M\320J\230Xr\037.\026%\b\311M\260\335\206\371a\343\006\356c\357.\332\207\033\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite-task-text"
    version: "0.4.4"
  }
  digests {
    sha256: "\351\316\331\317\243\365\002\317\230\324\001:I\354\374B0C\355\032#<P\253\265\352\307\a1\2323\217"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.tensorflow"
    artifactId: "tensorflow-lite-task-base"
    version: "0.4.4"
  }
  digests {
    sha256: "\213\025\231\333\025\323\372zX\366\207\316\343\206\000\370\314\341u\270h\226\334\274\000B\323\036\353\024^_"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.microsoft.cognitiveservices.speech"
    artifactId: "client-sdk"
    version: "1.43.0"
  }
  digests {
    sha256: "\240@\321B\005i\201\361}\203\271\316\250-\333/\312\362C/\373\324K\243\346\005\t\314\253\036 N"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "ai.picovoice"
    artifactId: "porcupine-android"
    version: "3.0.1"
  }
  digests {
    sha256: "\342(\357\230\336\360\327)\344\022Pf\017\224\332\310?-\256\031\374\205p\240\231.U$\306\343\341\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "ai.picovoice"
    artifactId: "android-voice-processor"
    version: "1.0.2"
  }
  digests {
    sha256: "o\"Z\227X\277\333\252q\222\250)y%\276f\246s\261\263.\252\244\355n\340\256\252Wtc\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.32.0"
  }
  digests {
    sha256: "\264\324\r\243\371\331\267\221h\271\317\r\252\a2\000\3509\356|\215x\213\351\233<\370u\356\255\302D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json"
    version: "1.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "\037r\274\253\2225d%\221\373Z\253\250n\0241p\036\360\016\377\245\364\005M5\204=\307\225\021v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "\3545\022\214O\271\004N\231\273\317\a\360^\362T;\372mB\003\rz\255\253\256\254\377\261nr\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.0.0"
  }
  digests {
    sha256: "o\232\307 \202\272\333wdG\315\325y\301l%\301\201\'\030\\k\251\362\317\253\222FG>-\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.0.0"
  }
  digests {
    sha256: "(\376\242\2156[J\357M^\2072\376Q\3623\345\200\322\004,\324\247\nk):Q\215\256\307\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.0.0"
  }
  digests {
    sha256: "\310\020PUB\000\025\223\aQ\2315[\031\331\213\205^\3346\331W[\327d\030\003!\342\n\222\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.0.0"
  }
  digests {
    sha256: "\255T\3363J\320\364\245:[\244\030v\371\026\2041\232\214\3639c\025\267g\265\021\002\244\023\213\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.security"
    artifactId: "security-crypto"
    version: "1.1.0-alpha06"
  }
  digests {
    sha256: "\227a\021\027\v?\023\322\360\371E}\237\237W\223G\024<\266\\\005Be\220m3\307a\212L*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.crypto.tink"
    artifactId: "tink-android"
    version: "1.8.0"
  }
  digests {
    sha256: "^\376\217\034\266\347X\024\256\006y=_\370\331\260\036o\210\357\227\354\300b\321N\274\'\002}\277x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie-compose"
    version: "6.1.0"
  }
  digests {
    sha256: "N$\315\'\001\367\r\203\374.+\371\343\r\202w\345\340v\3010\214\356\322\274PS\020\257\327\256\271"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.1.0"
  }
  digests {
    sha256: "Di\322\024.\n\017\353\246\321\224R\206\030\202L\346\302}C\236\031!+{\336\250\354\303\005\217\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.3.0"
  }
  digests {
    sha256: "\357\304\236\243\366zH\225\332\220R \252\303\023\345Y\366\376{\252N%\327sF\321\262x\365\370a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.2"
  }
  digests {
    sha256: "\207p\301\200\020>\v\214\004\240~\264\305\221S\257c\233\t\354\242]\352\351\275\315\257\206\235\036[k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.3.0"
  }
  digests {
    sha256: "R[\020}\316\275\305\240\301\320Sia\3513\271\017\217\006\022\356\375%\377~\213d\331%\354\245\360"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.3.0"
  }
  digests {
    sha256: "\326o\016+\371\253\020X\376\320f\205\302\352\354SW\367\224\r@Z\r8\370\241\262\324q \202\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-view"
    version: "1.3.0"
  }
  digests {
    sha256: "2z|\303\f,\207\335\340\006(5,\367s\213\203\226\353Cw\021B\203\317\304<\216\264\203m\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-video"
    version: "1.3.0"
  }
  digests {
    sha256: "3\250\363\v\371Cp\017\310\3736\261\rgo\020!\237.Ua\005\207\004d&\342q\357\361#\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.biometric"
    artifactId: "biometric"
    version: "1.1.0"
  }
  digests {
    sha256: "\'\f{}\231\224-^\301\335\210YNFH\376\263=\2161\330\303\302\253#!\324\235\232\275\374\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-systemuicontroller"
    version: "0.32.0"
  }
  digests {
    sha256: ")\244(;\bp\"*\206\261\347|\342W\300\363\005\200\323\277\031\376\254cY\361vPh5\301\233"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-splashscreen"
    version: "1.0.1"
  }
  digests {
    sha256: "%\310\023\256w\225\311\235\322\006(RpC\370\320B^\262\350&\346\304,\267z\313\266\300\035\336h"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.benchmark"
    artifactId: "benchmark-macro-junit4"
    version: "1.2.0"
  }
  digests {
    sha256: "@B\272\352#\216\034\305?\313\035\005\032\r\200\207A\232v\312B\232C2\240\270CN\313\315M\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.benchmark"
    artifactId: "benchmark-common"
    version: "1.2.0"
  }
  digests {
    sha256: "WZ\261\260\t3,\227\3156^\366(\362tD&dXcHl(\306Ky\332\020\260\223\233j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "monitor"
    version: "1.6.1"
  }
  digests {
    sha256: ")\205\316\205V\230\233\257|\2044.\177hw\023\3007\243\232\222.aM\032=\337\034\243wpy"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\300uI(\357\376\031h\303\251\247\265]\035\374|\353\036\036|\237?\t\371\212\375BC\037q$\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.1.0-rc01"
  }
  digests {
    sha256: "k\242\274\240\374\334^\255\312\212\330r\213.:\2273\301\364\037u{\211o`V\315\'O\275\017\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-perfetto-handshake"
    version: "1.0.0"
  }
  digests {
    sha256: "/.\345\305\205a\327,\253\351 |Cf\237\351-l\023\206\370\301\302bD\211\346\2536{\022\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-perfetto"
    version: "1.0.0"
  }
  digests {
    sha256: "\251\242\037\264\025\314BIr\026\000OJ5\334E\363\025\216K\245\352\032\001\264I\242\001\200\262Z^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-perfetto-binary"
    version: "1.0.0"
  }
  digests {
    sha256: "\343h\t\320T \'XH\206\207\354`\355\201\243\313\330{\320\'\250\177\005\252?dE\036m0\230"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.wire"
    artifactId: "wire-runtime"
    version: "4.7.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.wire"
    artifactId: "wire-runtime-jvm"
    version: "4.7.0"
  }
  digests {
    sha256: "e\036\207\214\235\333D2{\255\3126.K\\\224$\276\177\356\311 =j\270\225f5\206@\266\277"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-bom"
    version: "3.3.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.benchmark"
    artifactId: "benchmark-macro"
    version: "1.2.0"
  }
  digests {
    sha256: "\237,\210\031\254\261\373\260GGP!\257\254\021\354W\346M\313j\202)\241-b\377m\364\242\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "core"
    version: "1.5.0"
  }
  digests {
    sha256: ",\006q\\\r\bC\316\342\024:\270\2732+\263\363MRGc\004\002\374\214\033j\016\257\241[\237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test.services"
    artifactId: "storage"
    version: "1.4.2"
  }
  digests {
    sha256: "\263Ha\360\315\222\f\261\b\237\b\303\362~Xe\267\371 (L\304_N\321.\370\326\230\r\254H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test.uiautomator"
    artifactId: "uiautomator"
    version: "2.2.0"
  }
  digests {
    sha256: "(8\351\331a\333\377\357\273\322)\242\275Oo\202\254O\262F)u\206*\236u\351\3552Z1\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "junit"
    artifactId: "junit"
    version: "4.13.2"
  }
  digests {
    sha256: "\216I[cDi\326O\270\254\3724\225\240e\313\254\310\240\377\365\\\341\343\020\a\276L\026\334W\323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.hamcrest"
    artifactId: "hamcrest-core"
    version: "1.3"
  }
  digests {
    sha256: "f\375\357\221\351s\223H\337z\tj\243\204\245h_N\207U\204\314\350\223\206\247\244rQ\304\330\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "rules"
    version: "1.5.0"
  }
  digests {
    sha256: "\335dY)\306>$\364\033\350\346\365{\260o0];\002\355\033*}\226\026G\002z\341\331\341\177"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "runner"
    version: "1.5.0"
  }
  digests {
    sha256: "\263\305\252\275T[l&\005(\310\364\347\311\217sb\206\024\352=]\363\247\244J\362\023o\330r\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 76
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 24
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 25
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 26
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 44
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 45
}
library_dependencies {
  library_index: 27
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 28
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 26
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 19
  library_dep_index: 26
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 31
  library_dep_index: 9
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 24
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 19
  library_dep_index: 26
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 32
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 50
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 65
  library_dep_index: 61
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 66
}
library_dependencies {
  library_index: 38
  library_dep_index: 39
  library_dep_index: 5
  library_dep_index: 29
  library_dep_index: 43
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 39
  library_dep_index: 49
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 48
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 49
  library_dep_index: 38
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 41
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 44
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 44
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 40
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 6
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 46
  library_dep_index: 14
}
library_dependencies {
  library_index: 49
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 39
  library_dep_index: 38
}
library_dependencies {
  library_index: 50
  library_dep_index: 8
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 53
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 65
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 4
  library_dep_index: 74
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 4
  library_dep_index: 70
  library_dep_index: 72
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 68
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 5
  library_dep_index: 4
  library_dep_index: 68
  library_dep_index: 70
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 66
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 77
  library_dep_index: 39
  library_dep_index: 6
  library_dep_index: 78
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 84
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 87
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 77
}
library_dependencies {
  library_index: 79
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 80
  library_dep_index: 79
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 84
  library_dep_index: 39
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 85
  library_dep_index: 41
  library_dep_index: 86
  library_dep_index: 0
}
library_dependencies {
  library_index: 85
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 30
}
library_dependencies {
  library_index: 86
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 83
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
}
library_dependencies {
  library_index: 88
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 89
  library_dep_index: 95
  library_dep_index: 36
  library_dep_index: 63
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 74
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 73
  library_dep_index: 90
  library_dep_index: 96
  library_dep_index: 37
  library_dep_index: 64
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 75
  library_dep_index: 67
  library_dep_index: 70
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 57
  library_dep_index: 71
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 92
  library_dep_index: 94
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 58
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 91
  library_dep_index: 93
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 89
  library_dep_index: 93
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 32
  library_dep_index: 53
  library_dep_index: 4
  library_dep_index: 91
  library_dep_index: 89
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 49
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 74
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 32
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 24
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 4
}
library_dependencies {
  library_index: 97
  library_dep_index: 49
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 31
  library_dep_index: 98
  library_dep_index: 0
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 100
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 99
  library_dep_index: 97
  library_dep_index: 101
  library_dep_index: 100
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 101
  library_dep_index: 98
}
library_dependencies {
  library_index: 100
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 43
  library_dep_index: 40
  library_dep_index: 48
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 99
  library_dep_index: 97
  library_dep_index: 101
  library_dep_index: 98
}
library_dependencies {
  library_index: 101
  library_dep_index: 38
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 43
  library_dep_index: 100
  library_dep_index: 0
  library_dep_index: 100
  library_dep_index: 99
  library_dep_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 39
  library_dep_index: 6
  library_dep_index: 84
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 104
  library_dep_index: 0
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 106
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 104
}
library_dependencies {
  library_index: 108
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 109
  library_dep_index: 31
  library_dep_index: 97
  library_dep_index: 0
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 0
}
library_dependencies {
  library_index: 110
  library_dep_index: 6
  library_dep_index: 111
  library_dep_index: 112
  library_dep_index: 102
}
library_dependencies {
  library_index: 111
  library_dep_index: 106
}
library_dependencies {
  library_index: 112
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 44
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 46
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 118
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
  library_dep_index: 115
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 114
  library_dep_index: 115
}
library_dependencies {
  library_index: 114
  library_dep_index: 6
  library_dep_index: 2
  library_dep_index: 113
  library_dep_index: 115
}
library_dependencies {
  library_index: 115
  library_dep_index: 9
  library_dep_index: 18
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 114
  library_dep_index: 113
}
library_dependencies {
  library_index: 116
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 6
  library_dep_index: 116
  library_dep_index: 0
  library_dep_index: 116
}
library_dependencies {
  library_index: 118
  library_dep_index: 112
  library_dep_index: 112
}
library_dependencies {
  library_index: 119
  library_dep_index: 120
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
  library_dep_index: 2
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 123
  library_dep_index: 119
  library_dep_index: 124
}
library_dependencies {
  library_index: 125
  library_dep_index: 120
  library_dep_index: 2
}
library_dependencies {
  library_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 128
  library_dep_index: 126
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
}
library_dependencies {
  library_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 136
  library_dep_index: 8
}
library_dependencies {
  library_index: 137
  library_dep_index: 49
  library_dep_index: 66
  library_dep_index: 20
  library_dep_index: 138
  library_dep_index: 2
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
}
library_dependencies {
  library_index: 139
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 140
  library_dep_index: 141
}
library_dependencies {
  library_index: 141
  library_dep_index: 0
  library_dep_index: 142
  library_dep_index: 4
  library_dep_index: 143
}
library_dependencies {
  library_index: 142
  library_dep_index: 143
  library_dep_index: 141
  library_dep_index: 140
  library_dep_index: 144
}
library_dependencies {
  library_index: 143
  library_dep_index: 144
}
library_dependencies {
  library_index: 144
  library_dep_index: 0
  library_dep_index: 142
  library_dep_index: 4
}
library_dependencies {
  library_index: 145
  library_dep_index: 0
  library_dep_index: 146
  library_dep_index: 148
}
library_dependencies {
  library_index: 146
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 6
  library_dep_index: 147
}
library_dependencies {
  library_index: 147
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 6
}
library_dependencies {
  library_index: 148
  library_dep_index: 0
  library_dep_index: 147
}
library_dependencies {
  library_index: 149
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 150
}
library_dependencies {
  library_index: 150
  library_dep_index: 124
}
library_dependencies {
  library_index: 151
  library_dep_index: 88
  library_dep_index: 66
  library_dep_index: 36
  library_dep_index: 152
  library_dep_index: 2
}
library_dependencies {
  library_index: 152
  library_dep_index: 77
  library_dep_index: 121
}
library_dependencies {
  library_index: 153
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 154
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 155
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 156
  library_dep_index: 157
  library_dep_index: 158
  library_dep_index: 159
}
library_dependencies {
  library_index: 154
  library_dep_index: 6
}
library_dependencies {
  library_index: 156
  library_dep_index: 6
  library_dep_index: 153
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 155
  library_dep_index: 14
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 158
  library_dep_index: 159
}
library_dependencies {
  library_index: 157
  library_dep_index: 153
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 19
  library_dep_index: 155
  library_dep_index: 14
  library_dep_index: 156
  library_dep_index: 153
  library_dep_index: 158
  library_dep_index: 159
}
library_dependencies {
  library_index: 158
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 77
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 159
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 19
  library_dep_index: 155
  library_dep_index: 14
  library_dep_index: 156
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 159
}
library_dependencies {
  library_index: 159
  library_dep_index: 6
  library_dep_index: 153
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 155
  library_dep_index: 156
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 158
}
library_dependencies {
  library_index: 160
  library_dep_index: 39
  library_dep_index: 77
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 84
}
library_dependencies {
  library_index: 161
  library_dep_index: 5
  library_dep_index: 36
  library_dep_index: 20
  library_dep_index: 2
}
library_dependencies {
  library_index: 162
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 163
  library_dep_index: 6
  library_dep_index: 164
  library_dep_index: 174
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 0
  library_dep_index: 164
  library_dep_index: 174
}
library_dependencies {
  library_index: 164
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 165
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 171
  library_dep_index: 0
  library_dep_index: 174
  library_dep_index: 163
}
library_dependencies {
  library_index: 165
  library_dep_index: 6
  library_dep_index: 166
  library_dep_index: 47
}
library_dependencies {
  library_index: 166
  library_dep_index: 6
  library_dep_index: 9
}
library_dependencies {
  library_index: 167
  library_dep_index: 47
  library_dep_index: 0
}
library_dependencies {
  library_index: 168
  library_dep_index: 6
  library_dep_index: 169
  library_dep_index: 170
}
library_dependencies {
  library_index: 169
  library_dep_index: 6
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 170
  library_dep_index: 168
}
library_dependencies {
  library_index: 170
  library_dep_index: 169
  library_dep_index: 168
}
library_dependencies {
  library_index: 171
  library_dep_index: 172
}
library_dependencies {
  library_index: 172
  library_dep_index: 2
  library_dep_index: 121
  library_dep_index: 173
  library_dep_index: 4
}
library_dependencies {
  library_index: 173
  library_dep_index: 121
}
library_dependencies {
  library_index: 174
  library_dep_index: 6
  library_dep_index: 164
  library_dep_index: 8
  library_dep_index: 48
  library_dep_index: 175
  library_dep_index: 177
  library_dep_index: 167
  library_dep_index: 169
  library_dep_index: 170
  library_dep_index: 168
  library_dep_index: 171
  library_dep_index: 178
  library_dep_index: 0
  library_dep_index: 164
  library_dep_index: 163
}
library_dependencies {
  library_index: 175
  library_dep_index: 6
  library_dep_index: 165
  library_dep_index: 176
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 13
}
library_dependencies {
  library_index: 176
  library_dep_index: 6
  library_dep_index: 165
  library_dep_index: 107
  library_dep_index: 166
}
library_dependencies {
  library_index: 178
  library_dep_index: 179
}
library_dependencies {
  library_index: 180
  library_dep_index: 181
  library_dep_index: 166
}
library_dependencies {
  library_index: 181
  library_dep_index: 6
  library_dep_index: 166
  library_dep_index: 165
  library_dep_index: 176
  library_dep_index: 47
  library_dep_index: 178
}
library_dependencies {
  library_index: 182
  library_dep_index: 0
  library_dep_index: 183
}
library_dependencies {
  library_index: 183
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 77
  dependency_index: 29
  dependency_index: 49
  dependency_index: 88
  dependency_index: 36
  dependency_index: 63
  dependency_index: 95
  dependency_index: 89
  dependency_index: 97
  dependency_index: 31
  dependency_index: 28
  dependency_index: 102
  dependency_index: 108
  dependency_index: 110
  dependency_index: 115
  dependency_index: 113
  dependency_index: 119
  dependency_index: 123
  dependency_index: 120
  dependency_index: 125
  dependency_index: 126
  dependency_index: 128
  dependency_index: 129
  dependency_index: 132
  dependency_index: 20
  dependency_index: 118
  dependency_index: 134
  dependency_index: 135
  dependency_index: 137
  dependency_index: 124
  dependency_index: 140
  dependency_index: 145
  dependency_index: 149
  dependency_index: 68
  dependency_index: 72
  dependency_index: 151
  dependency_index: 153
  dependency_index: 156
  dependency_index: 157
  dependency_index: 158
  dependency_index: 160
  dependency_index: 161
  dependency_index: 162
  dependency_index: 163
  dependency_index: 182
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://s01.oss.sonatype.org/content/repositories/releases/"
  }
}
