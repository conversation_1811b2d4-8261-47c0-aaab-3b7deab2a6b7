<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <!-- Pressed state - inset shadow effect -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="@dimen/corner_radius_lg" />
                    <solid android:color="@color/neuro_surface_dark" />
                </shape>
            </item>
            <item android:left="2dp" android:top="2dp" android:right="2dp" android:bottom="2dp">
                <shape android:shape="rectangle">
                    <corners android:radius="@dimen/corner_radius_lg" />
                    <gradient
                        android:startColor="@color/neuro_dark_shadow"
                        android:endColor="@color/neuro_dark_highlight"
                        android:angle="135" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <!-- Normal state - raised effect -->
        <layer-list>
            <!-- Dark shadow (bottom-right) -->
            <item android:left="8dp" android:top="8dp">
                <shape android:shape="rectangle">
                    <corners android:radius="@dimen/corner_radius_lg" />
                    <solid android:color="@color/neuro_dark_shadow" />
                </shape>
            </item>
            <!-- Light highlight (top-left) -->
            <item android:right="8dp" android:bottom="8dp">
                <shape android:shape="rectangle">
                    <corners android:radius="@dimen/corner_radius_lg" />
                    <solid android:color="@color/neuro_dark_highlight" />
                </shape>
            </item>
            <!-- Main surface -->
            <item android:left="4dp" android:top="4dp" android:right="4dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <corners android:radius="@dimen/corner_radius_lg" />
                    <solid android:color="@color/neuro_surface_dark" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
