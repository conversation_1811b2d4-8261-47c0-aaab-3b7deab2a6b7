// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import android.content.SharedPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata({
    "com.zara.assistant.di.RegularPrefs",
    "dagger.hilt.android.qualifiers.ApplicationContext"
})
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideRegularSharedPreferencesFactory implements Factory<SharedPreferences> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideRegularSharedPreferencesFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SharedPreferences get() {
    return provideRegularSharedPreferences(contextProvider.get());
  }

  public static AppModule_ProvideRegularSharedPreferencesFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_ProvideRegularSharedPreferencesFactory(contextProvider);
  }

  public static SharedPreferences provideRegularSharedPreferences(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideRegularSharedPreferences(context));
  }
}
