package com.zara.assistant.ui.viewmodel;

/**
 * Main ViewModel - Manages the main screen state and voice interactions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B/\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0006\u0010*\u001a\u00020+J\u000e\u0010,\u001a\u00020+2\u0006\u0010-\u001a\u00020\u0011J\u0012\u0010.\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00130/J\b\u00100\u001a\u00020+H\u0002J\b\u00101\u001a\u00020+H\u0002J\b\u00102\u001a\u00020+H\u0014J \u00103\u001a\u00020+2\u0006\u00104\u001a\u00020\u00112\b\b\u0002\u00105\u001a\u00020\u0011H\u0082@\u00a2\u0006\u0002\u00106J\u0016\u00107\u001a\u00020+2\u0006\u00108\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u00109J\u0016\u0010:\u001a\u00020+2\u0006\u00104\u001a\u00020\u0011H\u0082@\u00a2\u0006\u0002\u0010;J\u0006\u0010<\u001a\u00020+J\u0006\u0010=\u001a\u00020+J\u0006\u0010>\u001a\u00020+R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00130\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0019\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001cR\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00130\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001cR\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00130\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001cR\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00130\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001cR\u0019\u0010\"\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001cR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00180\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001cR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/zara/assistant/ui/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "zaraSTTService", "Lcom/zara/assistant/services/ZaraSTTService;", "zaraTTSService", "Lcom/zara/assistant/services/ZaraTTSService;", "localCommandProcessor", "Lcom/zara/assistant/services/LocalCommandProcessor;", "aiOrchestrationService", "Lcom/zara/assistant/services/AIOrchestrationService;", "systemControlManager", "Lcom/zara/assistant/services/SystemControlManager;", "(Lcom/zara/assistant/services/ZaraSTTService;Lcom/zara/assistant/services/ZaraTTSService;Lcom/zara/assistant/services/LocalCommandProcessor;Lcom/zara/assistant/services/AIOrchestrationService;Lcom/zara/assistant/services/SystemControlManager;)V", "_currentCommand", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/domain/model/VoiceCommand;", "_errorMessage", "", "_isInitialized", "", "_isListening", "_isSpeaking", "_lastResponse", "_voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "currentCommand", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentCommand", "()Lkotlinx/coroutines/flow/StateFlow;", "errorMessage", "getErrorMessage", "isInitialized", "isListening", "isSpeaking", "lastResponse", "getLastResponse", "sttListener", "Lcom/zara/assistant/services/ZaraSTTService$STTListener;", "ttsListener", "Lcom/zara/assistant/services/ZaraTTSService$TTSListener;", "voiceState", "getVoiceState", "clearError", "", "executeQuickAction", "action", "getServiceStatus", "", "initializeServices", "observeServiceStates", "onCleared", "processAICommand", "text", "language", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processVoiceCommand", "command", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "speak", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startListening", "stopListening", "stopSpeaking", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraSTTService zaraSTTService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraTTSService zaraTTSService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.LocalCommandProcessor localCommandProcessor = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.AIOrchestrationService aiOrchestrationService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.SystemControlManager systemControlManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.VoiceState> _voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceState> voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isListening = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListening = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isSpeaking = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSpeaking = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _lastResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> lastResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.VoiceCommand> _currentCommand = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceCommand> currentCommand = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isInitialized = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isInitialized = null;
    
    /**
     * STT Listener implementation
     */
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraSTTService.STTListener sttListener = null;
    
    /**
     * TTS Listener implementation
     */
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraTTSService.TTSListener ttsListener = null;
    
    @javax.inject.Inject()
    public MainViewModel(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.ZaraSTTService zaraSTTService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.ZaraTTSService zaraTTSService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.LocalCommandProcessor localCommandProcessor, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.AIOrchestrationService aiOrchestrationService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.SystemControlManager systemControlManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceState> getVoiceState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListening() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSpeaking() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getLastResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceCommand> getCurrentCommand() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isInitialized() {
        return null;
    }
    
    /**
     * Initialize all voice services
     */
    private final void initializeServices() {
    }
    
    /**
     * Observe service states
     */
    private final void observeServiceStates() {
    }
    
    /**
     * Start listening for voice input
     */
    public final void startListening() {
    }
    
    /**
     * Stop listening
     */
    public final void stopListening() {
    }
    
    /**
     * Stop speaking
     */
    public final void stopSpeaking() {
    }
    
    /**
     * Execute quick action
     */
    public final void executeQuickAction(@org.jetbrains.annotations.NotNull()
    java.lang.String action) {
    }
    
    /**
     * Process voice command
     */
    private final java.lang.Object processVoiceCommand(com.zara.assistant.domain.model.VoiceCommand command, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Process AI command
     */
    private final java.lang.Object processAICommand(java.lang.String text, java.lang.String language, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Speak text using TTS
     */
    private final java.lang.Object speak(java.lang.String text, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear error message
     */
    public final void clearError() {
    }
    
    /**
     * Get service status
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Boolean> getServiceStatus() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}