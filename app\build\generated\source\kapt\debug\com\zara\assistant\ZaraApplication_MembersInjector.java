// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant;

import androidx.hilt.work.HiltWorkerFactory;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ZaraApplication_MembersInjector implements MembersInjector<ZaraApplication> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  public ZaraApplication_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
  }

  public static MembersInjector<ZaraApplication> create(
      Provider<HiltWorkerFactory> workerFactoryProvider) {
    return new ZaraApplication_MembersInjector(workerFactoryProvider);
  }

  @Override
  public void injectMembers(ZaraApplication instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.ZaraApplication.workerFactory")
  public static void injectWorkerFactory(ZaraApplication instance,
      HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }
}
