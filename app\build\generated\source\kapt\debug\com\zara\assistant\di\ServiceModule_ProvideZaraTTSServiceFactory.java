// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.ZaraTTSService;
import com.zara.assistant.utils.ApiKeyManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideZaraTTSServiceFactory implements Factory<ZaraTTSService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public ServiceModule_ProvideZaraTTSServiceFactory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public ZaraTTSService get() {
    return provideZaraTTSService(contextProvider.get(), apiKeyManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static ServiceModule_ProvideZaraTTSServiceFactory create(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new ServiceModule_ProvideZaraTTSServiceFactory(contextProvider, apiKeyManagerProvider, performanceUtilsProvider);
  }

  public static ZaraTTSService provideZaraTTSService(Context context, ApiKeyManager apiKeyManager,
      PerformanceUtils performanceUtils) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideZaraTTSService(context, apiKeyManager, performanceUtils));
  }
}
