"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\release\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2dad437ddc61bad9487be99cb49aa94\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ab5bc586c3f73924a8269e87f31a8b4\\transformed\\jetified-hilt-work-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed64443e3ed900b504df75b0b30a8a2a\\transformed\\jetified-hilt-navigation-compose-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35ae88de4bfe9b94a57a0161f458c98c\\transformed\\jetified-hilt-navigation-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\136706c33c8ae265daeac08e5fa307b1\\transformed\\jetified-hilt-android-2.48-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a798033affcfcac2533386495cf649c0\\transformed\\biometric-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\506abe15e49e3a6e2fd46e5d31078cd0\\transformed\\fragment-1.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\269f79903e0ac1e9dbb9245f53948462\\transformed\\navigation-common-2.7.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\797c2803dfbe6a77b871331d1999ff86\\transformed\\navigation-runtime-2.7.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2a1aeffd3619d6a30d71d25ebb412afb\\transformed\\navigation-common-ktx-2.7.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\96b6de6438e036de76716e2a2d6b9e94\\transformed\\navigation-runtime-ktx-2.7.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3fcf32a24b4e4702dc1ac5ba4edb5f9b\\transformed\\jetified-navigation-compose-2.7.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0565e082ba63c211bc146eafbee20034\\transformed\\jetified-activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c4a60077bb5d98f06a870710c850e2cc\\transformed\\jetified-activity-compose-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5c8c663aec5c06648d6f5820b32a999f\\transformed\\jetified-activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfba13bf21a1c1f365f659a9c35ec078\\transformed\\jetified-camera-video-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\67b3ef893ba37cf8975d840a9ccc37f3\\transformed\\jetified-camera-view-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\427246d21875627923a9d0be9cfce1d6\\transformed\\jetified-camera-lifecycle-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\896da30c66342b90891268a32fe5e380\\transformed\\jetified-camera-camera2-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5025bae349d04ea97d21564385ad1ac8\\transformed\\jetified-camera-core-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\09eddb9d27b1eedda0f5e6ee28912ff6\\transformed\\jetified-appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b49ac6e64e370f04a14891968641dbbe\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb323367e8886c57c4331dbf5f3d5f9\\transformed\\jetified-porcupine-android-3.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a2a84446943d106fc95bd0bddef29dda\\transformed\\jetified-android-voice-processor-1.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3b68a1f9d9c4ecfb42959d370d6341a\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0d05bfa008496647a65ab9a65e8328f\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d0191c8dd434314755d59f24a5a70918\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\922acc685f6a0ab799cd0d2130617511\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d06ca7550fa85377de9cf431823b2bbd\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\92817de81ac9f184d134537ee457d97e\\transformed\\core-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd9808dd55bc3417022a475e0a315637\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bac25062ca0c2c348fac078ca6587853\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a8107af42e55b7ec9c631fa9f01edcd3\\transformed\\work-runtime-ktx-2.9.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\434bf53d9afec4e34c70dcdba9e89f03\\transformed\\work-runtime-2.9.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\04d13ddec2b30698b6d38188374eaf88\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ccd80a4d87915ee84104ccacce2dab4\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed64d7d9c825014429f8d20357756639\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90c30ff8f209b32fa638b72840d76a78\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\432375cbcd5bd9b8eb5876fb5c859666\\transformed\\jetified-lifecycle-livedata-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1a1945d5c0927bb0cf9ba9d0e3a356e5\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4b0be7bc851af3b3bc132b3670a5c72\\transformed\\jetified-lifecycle-viewmodel-compose-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa3c30c665d8b3c9bc28652befe8a603\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecc91e355f59d55de77d8ceb48421c66\\transformed\\jetified-core-ktx-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.room\\room-common\\2.6.1\\ff1b9580850a9b7eef56554e356628d225785265\\room-common-2.6.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cfef959dda4235377b801f7e4c45fd40\\transformed\\room-runtime-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad0de7858229a13d43e2ed4dbbc0fd17\\transformed\\jetified-room-ktx-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6a181b5849e855771960065258ac0e02\\transformed\\jetified-datastore-preferences-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5354dc686822c52105b3315059987b5c\\transformed\\jetified-core-splashscreen-1.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3030cfcd9e1497c55c7f0fbdaf31c10\\transformed\\jetified-benchmark-macro-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7146b92c6712c7ec060566d68d9879d7\\transformed\\jetified-benchmark-macro-junit4-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c1325f2724cfa77b36646b25cf78a1af\\transformed\\jetified-kotlin-parcelize-runtime-1.9.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf5e3e721758e5d0929f6d776f85e05f\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8e0271087a926cd01d1672b804960b0e\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f6f5bbd62e2ec27aa9c827aa4f3e10b0\\transformed\\jetified-material3-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bd1f1638210b7299fb1a21bd1e301c0\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5728992e1c989e6180861a309bb07da\\transformed\\jetified-converter-gson-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\966cb0c2f6879a5cd3d538cf1413b6d2\\transformed\\jetified-retrofit-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9029f3866d4348372290a5dfc3a892b7\\transformed\\jetified-logging-interceptor-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2befd71f9525f1577fd1b5c7063a6cb9\\transformed\\jetified-okhttp-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\215f558912eff8d399cc29dc28d09b5f\\transformed\\jetified-datastore-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\203d434c84e0afabe4426a58d518817c\\transformed\\jetified-animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\48735a73b2fa0a7a8e07c4a0c7d4626c\\transformed\\jetified-animation-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29055b84c0338f7d1e2283436cba85ea\\transformed\\jetified-foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90b20f7ba7291d09541dad0abe170633\\transformed\\jetified-material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c6510f229451af03683c062aed1df21\\transformed\\jetified-material-icons-extended-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcc9e85e492fe65738119b5a105855d4\\transformed\\jetified-material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dee12a112a3fcdc0a7021040d1cca8ef\\transformed\\jetified-foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dd9224cb4377b400c7c9cf6b436151d6\\transformed\\jetified-animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ddfe2220a30a8415c5b06a380de2816\\transformed\\jetified-ui-util-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\666a77c8cbd495d61c236262a772f4d5\\transformed\\jetified-ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8f3334839ac076dd8dc07b4672a3c211\\transformed\\jetified-ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7009eb0957495692037eefc9a73c578e\\transformed\\jetified-ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d36670b78fc111f1db94dd4f6461351e\\transformed\\jetified-ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3de17c53d53f4310371000bfa681e19c\\transformed\\jetified-ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01a8b970ab096e7d6fcb31058116697d\\transformed\\jetified-ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0ff3f2cd28b1d766756248e78743e79\\transformed\\jetified-runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\23f3f6e491b199be64de8b5ec05fdf59\\transformed\\jetified-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6a0b118967cdf9807cd57c8703e991e3\\transformed\\jetified-datastore-preferences-core-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e6b88965326ed52dd7f0315351a9c85c\\transformed\\jetified-datastore-core-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc14425d4670d9bdc8a7f293fcce53d5\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\89dcfa33d217913434d7a22ae53c6cca\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a67efd0300d9952ae40652d63d589fe\\transformed\\jetified-accompanist-permissions-0.32.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c3668e4462a016438decc9bce793cbd\\transformed\\jetified-lottie-compose-6.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9430aabfe4caadbc59e97205d574a4e8\\transformed\\jetified-accompanist-systemuicontroller-0.32.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4a41c51924791d433d7ac93f8c04d997\\transformed\\jetified-wire-runtime-jvm-4.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bafa24b4831cc97b84d95ac3b006a67c\\transformed\\jetified-okio-jvm-3.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9fff7662257a384a85f7fc54dae38b11\\transformed\\jetified-kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\84ef3e1654c77ec4384ca43096d4d4fb\\transformed\\jetified-kotlinx-serialization-core-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0607090ff5d2f05bd0edd327fb72e01\\transformed\\jetified-kotlinx-serialization-json-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e63850005c814515e7f2e53ed0b4c7e1\\transformed\\jetified-kotlin-android-extensions-runtime-1.9.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbebc0008da0123a837011fff7d95913\\transformed\\jetified-security-crypto-1.1.0-alpha06-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31b909452a07eca086e07444a6d79e74\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97c62994b0579865e82435bcaf9f0972\\transformed\\sqlite-framework-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\83d09ed26b2ce027f2d30c1e844aa9c0\\transformed\\sqlite-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\91ec885c22f989d5f3d8b8a6cc98d6d5\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\51b46e4fa76cc2cb77f8232b0b94c6ad\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d61fdaf6bb2acfcf3fb427872033eb06\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a54aafbf5066e5fa981e1e4fb23e918\\transformed\\jetified-collection-jvm-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc01b6964519e9a9d53b403738b12d8c\\transformed\\jetified-annotation-jvm-1.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\da1b48b95d5765e3210958380f7e1ac8\\transformed\\jetified-kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a1fe2124669d9ce8631d65d4f1e23da9\\transformed\\jetified-kotlin-stdlib-1.9.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71bb1562a00c577becd9e8158b73fdc1\\transformed\\jetified-tensorflow-lite-support-0.4.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b34275f27d8202fb87880e0f31c80cde\\transformed\\jetified-tensorflow-lite-2.14.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ae18b506b306d479b18d4cc7eb6799bc\\transformed\\jetified-tensorflow-lite-metadata-0.4.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0dc8d3bfc7642c3225764b9413b6fcfc\\transformed\\jetified-tensorflow-lite-task-text-0.4.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\96d487edfb5a47e9e9b392e5e9c72b3d\\transformed\\jetified-client-sdk-1.43.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6c6d6c75b7641cfbdff8b210a8138414\\transformed\\jetified-gson-2.10.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\55aab0fc1f25efd1c08b1508a902d3de\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0ac16666caec9f4e16ebdfbe20952c99\\transformed\\jetified-listenablefuture-1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4980e87257cb2fa04483b96581f0276d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e770b3089b3f38fb1d82aad268a0d4e4\\transformed\\jetified-hilt-core-2.48.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc5a9c0691fc941c037f763af6cb1a5d\\transformed\\jetified-dagger-2.48.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d05cbf7654dc920c6b8aa8e4e7db64cb\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc287d7e4f4dea5d9ed681b56ea1825\\transformed\\jetified-dagger-lint-aar-2.48-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\817bb1281e412b1ab4f161ab58e3a9b8\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f18dbb11d37ce8f981c2f434328aa18c\\transformed\\jetified-hilt-common-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bad89529ace42ea965e319baf10870ea\\transformed\\jetified-tensorflow-lite-api-2.14.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\453c6291c444c8293cdebeb2c355b50b\\transformed\\jetified-checker-qual-2.5.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6e0cd236a55ce0f5508cd454722fc93a\\transformed\\jetified-flatbuffers-java-1.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c407a00475607fa7d72b668666c5222\\transformed\\jetified-tensorflow-lite-task-base-0.4.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71c865d6027b1855a636c924ff6e9be0\\transformed\\jetified-lottie-6.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d359022ec85dd8f117281ec46e3b6485\\transformed\\uiautomator-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c43281a24904eddd41b913a30382486a\\transformed\\jetified-junit-4.13.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2369a72096ebf0be31bcdc765215f9b6\\transformed\\jetified-hamcrest-core-1.3.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\build\\tmp\\kapt3\\incrementalData\\release" "-jvm-target" "1.8" "-module-name" "app_release" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-annotation-processing-gradle\\1.9.22\\366b6f8a7b7811a120730dc9ad70600c0d141031\\kotlin-annotation-processing-gradle-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-parcelize-compiler\\1.9.22\\222c989e288e9a99c8579de44a0fe61230ddbfc5\\kotlin-parcelize-compiler-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-serialization-compiler-plugin-embeddable\\1.9.22\\42ec4c04d70432e25e0e546c93db4de7df94cf96\\kotlin-serialization-compiler-plugin-embeddable-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compiler-embeddable\\1.9.22\\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\\kotlin-compiler-embeddable-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.22\\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\\kotlin-stdlib-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-script-runtime\\1.9.22\\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\\kotlin-script-runtime-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-reflect\\1.6.10\\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\\kotlin-reflect-1.6.10.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-daemon-embeddable\\1.9.22\\20e2c5df715f3240c765cfc222530e2796542021\\kotlin-daemon-embeddable-1.9.22.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.intellij.deps\\trove4j\\1.0.20200330\\3afb14d5f9ceb459d724e907a21145e8ff394f02\\trove4j-1.0.20200330.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar" "-P" "plugin:org.jetbrains.kotlin.kapt3:configuration=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" "-P" "plugin:androidx.compose.plugins.idea:enabled=true" "-Xallow-unstable-dependencies" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\build\\generated\\source\\buildConfig\\release\\com\\zara\\assistant\\BuildConfig.java" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\core\\Constants.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\di\\AppModule.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\di\\ServiceModule.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\model\\AIResponse.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\model\\SystemControlResult.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\model\\VoiceCommand.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\repository\\AIRepository.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\repository\\SystemControlRepository.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\domain\\repository\\VoiceRepository.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\components\\NeumorphismButton.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\components\\NeumorphismCard.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\components\\VoiceButton.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\components\\VoiceVisualization.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\theme\\Theme.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\presentation\\theme\\Type.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\AIOrchestrationService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\CohereAIService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\LocalCommandProcessor.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\NotificationListenerService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\PerplexityAIService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\SystemControlManager.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\WakeWordService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\ZaraAccessibilityService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\ZaraDeviceAdminReceiver.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\ZaraSTTService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\ZaraTTSService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\services\\ZaraVoiceService.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\MainActivity.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\AboutScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\CommandsHelpScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\MainScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\OnboardingScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\PermissionsScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\SettingsScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\screens\\VoiceSettingsScreen.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ui\\viewmodel\\MainViewModel.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\utils\\ApiKeyManager.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\utils\\DirectPermissionManager.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\utils\\PerformanceUtils.kt" "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\java\\com\\zara\\assistant\\ZaraApplication.kt"