package com.zara.assistant.services;

/**
 * Zara Wake Word Service - "Hey Zara" detection using Picovoice Porcupine
 * Ultra-fast wake word detection with configurable sensitivity
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u000b\b\u0007\u0018\u0000 A2\u00020\u0001:\u0002ABB\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010%\u001a\u00020&H\u0082@\u00a2\u0006\u0002\u0010\'J\b\u0010(\u001a\u00020)H\u0002J\u0006\u0010*\u001a\u00020\u000fJ\u0016\u0010+\u001a\u00020\u00052\u0006\u0010,\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010-J\u0006\u0010.\u001a\u00020\u0005J\u0006\u0010/\u001a\u00020\u0005J\u0014\u00100\u001a\u0004\u0018\u0001012\b\u00102\u001a\u0004\u0018\u000103H\u0016J\b\u00104\u001a\u000205H\u0016J\b\u00106\u001a\u000205H\u0016J\"\u00107\u001a\u0002082\b\u00102\u001a\u0004\u0018\u0001032\u0006\u00109\u001a\u0002082\u0006\u0010:\u001a\u000208H\u0016J\u0010\u0010;\u001a\u0002052\b\u0010<\u001a\u0004\u0018\u00010$J\b\u0010=\u001a\u000205H\u0002J\u0010\u0010>\u001a\u0002052\u0006\u0010,\u001a\u00020\u000fH\u0002J\b\u0010?\u001a\u000205H\u0002J\u0010\u0010@\u001a\u0002052\u0006\u0010,\u001a\u00020\u000fH\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\b\u001a\u00020\t8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0014R\u001e\u0010\u0015\u001a\u00020\u00168\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010!\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0014R\u0010\u0010#\u001a\u0004\u0018\u00010$X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006C"}, d2 = {"Lcom/zara/assistant/services/WakeWordService;", "Landroid/app/Service;", "()V", "_isListening", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_wakeWordDetected", "", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "getApiKeyManager", "()Lcom/zara/assistant/utils/ApiKeyManager;", "setApiKeyManager", "(Lcom/zara/assistant/utils/ApiKeyManager;)V", "currentSensitivity", "", "isInitialized", "isListening", "isListeningFlow", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "performanceUtils", "Lcom/zara/assistant/utils/PerformanceUtils;", "getPerformanceUtils", "()Lcom/zara/assistant/utils/PerformanceUtils;", "setPerformanceUtils", "(Lcom/zara/assistant/utils/PerformanceUtils;)V", "porcupineManager", "Lai/picovoice/porcupine/PorcupineManager;", "porcupineWakeWordCallback", "Lai/picovoice/porcupine/PorcupineManagerCallback;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "wakeWordDetectedFlow", "getWakeWordDetectedFlow", "wakeWordListener", "Lcom/zara/assistant/services/WakeWordService$WakeWordListener;", "copyPPNFileFromAssets", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNotification", "Landroid/app/Notification;", "getCurrentSensitivity", "initializePorcupine", "sensitivity", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isAvailable", "isCurrentlyListening", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "", "onDestroy", "onStartCommand", "", "flags", "startId", "setWakeWordListener", "listener", "startVoiceProcessing", "startWakeWordDetection", "stopWakeWordDetection", "updateSensitivity", "Companion", "WakeWordListener", "app_release"})
public final class WakeWordService extends android.app.Service {
    private static final int NOTIFICATION_ID = 1002;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_WAKE_WORD = "START_WAKE_WORD";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_WAKE_WORD = "STOP_WAKE_WORD";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SET_SENSITIVITY = "SET_SENSITIVITY";
    @javax.inject.Inject()
    public com.zara.assistant.utils.ApiKeyManager apiKeyManager;
    @javax.inject.Inject()
    public com.zara.assistant.utils.PerformanceUtils performanceUtils;
    @org.jetbrains.annotations.Nullable()
    private ai.picovoice.porcupine.PorcupineManager porcupineManager;
    private boolean isInitialized = false;
    private boolean isListening = false;
    private float currentSensitivity = 0.5F;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isListening = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Long> _wakeWordDetected = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> wakeWordDetectedFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.Nullable()
    private com.zara.assistant.services.WakeWordService.WakeWordListener wakeWordListener;
    
    /**
     * Porcupine wake word callback
     */
    @org.jetbrains.annotations.NotNull()
    private final ai.picovoice.porcupine.PorcupineManagerCallback porcupineWakeWordCallback = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.WakeWordService.Companion Companion = null;
    
    public WakeWordService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.ApiKeyManager getApiKeyManager() {
        return null;
    }
    
    public final void setApiKeyManager(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.PerformanceUtils getPerformanceUtils() {
        return null;
    }
    
    public final void setPerformanceUtils(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getWakeWordDetectedFlow() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * Initialize Porcupine wake word detection
     */
    private final java.lang.Object initializePorcupine(float sensitivity, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Copy PPN file from assets to internal storage
     */
    private final java.lang.Object copyPPNFileFromAssets(kotlin.coroutines.Continuation<java.lang.Object> $completion) {
        return null;
    }
    
    /**
     * Start wake word detection
     */
    private final void startWakeWordDetection(float sensitivity) {
    }
    
    /**
     * Stop wake word detection
     */
    private final void stopWakeWordDetection() {
    }
    
    /**
     * Update sensitivity
     */
    private final void updateSensitivity(float sensitivity) {
    }
    
    /**
     * Start voice processing when wake word is detected
     */
    private final void startVoiceProcessing() {
    }
    
    /**
     * Create notification for foreground service
     */
    private final android.app.Notification createNotification() {
        return null;
    }
    
    /**
     * Set wake word listener
     */
    public final void setWakeWordListener(@org.jetbrains.annotations.Nullable()
    com.zara.assistant.services.WakeWordService.WakeWordListener listener) {
    }
    
    /**
     * Check if wake word detection is available
     */
    public final boolean isAvailable() {
        return false;
    }
    
    /**
     * Get current listening state
     */
    public final boolean isCurrentlyListening() {
        return false;
    }
    
    /**
     * Get current sensitivity
     */
    public final float getCurrentSensitivity() {
        return 0.0F;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eJ\u0018\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eJ\u000e\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/zara/assistant/services/WakeWordService$Companion;", "", "()V", "ACTION_SET_SENSITIVITY", "", "ACTION_START_WAKE_WORD", "ACTION_STOP_WAKE_WORD", "NOTIFICATION_ID", "", "setSensitivity", "", "context", "Landroid/content/Context;", "sensitivity", "", "startWakeWordDetection", "stopWakeWordDetection", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void startWakeWordDetection(@org.jetbrains.annotations.NotNull()
        android.content.Context context, float sensitivity) {
        }
        
        public final void stopWakeWordDetection(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
        
        public final void setSensitivity(@org.jetbrains.annotations.NotNull()
        android.content.Context context, float sensitivity) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0003H&\u00a8\u0006\u0007"}, d2 = {"Lcom/zara/assistant/services/WakeWordService$WakeWordListener;", "", "onError", "", "error", "", "onWakeWordDetected", "app_release"})
    public static abstract interface WakeWordListener {
        
        public abstract void onWakeWordDetected();
        
        public abstract void onError(@org.jetbrains.annotations.NotNull()
        java.lang.String error);
    }
}