// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.utils.ApiKeyManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PerplexityAIService_Factory implements Factory<PerplexityAIService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public PerplexityAIService_Factory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public PerplexityAIService get() {
    return newInstance(contextProvider.get(), apiKeyManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static PerplexityAIService_Factory create(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new PerplexityAIService_Factory(contextProvider, apiKeyManagerProvider, performanceUtilsProvider);
  }

  public static PerplexityAIService newInstance(Context context, ApiKeyManager apiKeyManager,
      PerformanceUtils performanceUtils) {
    return new PerplexityAIService(context, apiKeyManager, performanceUtils);
  }
}
