package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a\u001a\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00022\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a\u0012\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a0\u0010\r\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0007\"\u0014\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"permissionItems", "", "Lcom/zara/assistant/ui/screens/PermissionItem;", "PermissionActions", "", "onGrantPermissions", "Lkotlin/Function0;", "onOpenSettings", "modifier", "Landroidx/compose/ui/Modifier;", "PermissionCard", "permission", "PermissionsHeader", "PermissionsScreen", "onPermissionsGranted", "directPermissionManager", "Lcom/zara/assistant/utils/DirectPermissionManager;", "app_release"})
public final class PermissionsScreenKt {
    
    /**
     * Permission items list
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.zara.assistant.ui.screens.PermissionItem> permissionItems = null;
    
    /**
     * Beautiful Permissions Screen - Elegant permission management with direct granting
     * Features:
     * - Stunning visual design
     * - Direct permission granting
     * - Real-time permission status
     * - Interactive permission cards
     * - Smooth animations
     * - Intelligent permission flow
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PermissionsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionsGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOpenSettings, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.utils.DirectPermissionManager directPermissionManager) {
    }
    
    /**
     * Permissions header with title and description
     */
    @androidx.compose.runtime.Composable()
    private static final void PermissionsHeader(androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Individual permission card
     */
    @androidx.compose.runtime.Composable()
    private static final void PermissionCard(com.zara.assistant.ui.screens.PermissionItem permission, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Permission action buttons
     */
    @androidx.compose.runtime.Composable()
    private static final void PermissionActions(kotlin.jvm.functions.Function0<kotlin.Unit> onGrantPermissions, kotlin.jvm.functions.Function0<kotlin.Unit> onOpenSettings, androidx.compose.ui.Modifier modifier) {
    }
}