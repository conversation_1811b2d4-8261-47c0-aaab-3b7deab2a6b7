{"logs": [{"outputFile": "com.zara.assistant.app-mergeReleaseResources-83:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a81feca8f62c4cb5733ffcc676aee47\\transformed\\jetified-material3-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4649,4736,4834,4914,4998,5098,5201,5299,5399,5486,5592,5691,5794,5912,5992,6092", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4644,4731,4829,4909,4993,5093,5196,5294,5394,5481,5587,5686,5789,5907,5987,6087,6181"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5640,5754,5866,5991,6103,6198,6297,6413,6552,6672,6814,6899,7003,7097,7197,7311,7439,7548,7683,7815,7945,8124,8250,8372,8498,8633,8728,8824,8951,9081,9182,9287,9394,9529,9670,9779,9881,9956,10053,10149,10234,10321,10419,10499,10583,10683,10786,10884,10984,11071,11177,11276,11379,11497,11577,11677", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "5749,5861,5986,6098,6193,6292,6408,6547,6667,6809,6894,6998,7092,7192,7306,7434,7543,7678,7810,7940,8119,8245,8367,8493,8628,8723,8819,8946,9076,9177,9282,9389,9524,9665,9774,9876,9951,10048,10144,10229,10316,10414,10494,10578,10678,10781,10879,10979,11066,11172,11271,11374,11492,11572,11672,11766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3093826e12afe4e65cbc1e4cc9cbb375\\transformed\\jetified-foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12762,12847", "endColumns": "84,84", "endOffsets": "12842,12927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,12247", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,12323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76f8ef271629eccbc242979ade06d14f\\transformed\\core-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2788,2882,2985,3082,3184,3286,3384,12403", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "2877,2980,3077,3179,3281,3379,3501,12499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c1dbd90dee8ec981efff90fba80648e\\transformed\\jetified-ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "36,37,39,41,42,53,54,111,112,113,114,115,116,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3506,3598,3793,3972,4071,5453,5539,11771,11858,11944,12012,12081,12164,12328,12504,12580,12646", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "3593,3675,3881,4066,4153,5534,5635,11853,11939,12007,12076,12159,12242,12398,12575,12641,12757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10f8b8bca7dd0586b8f58168a92d3396\\transformed\\biometric-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,254,379,506,637,780,915,1057,1154,1291,1429", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "163,249,374,501,632,775,910,1052,1149,1286,1424,1544"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3680,3886,4158,4283,4410,4541,4684,4819,4961,5058,5195,5333", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "3788,3967,4278,4405,4536,4679,4814,4956,5053,5190,5328,5448"}}]}]}