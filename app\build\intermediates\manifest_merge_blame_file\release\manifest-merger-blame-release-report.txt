1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zara.assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Essential Audio and Speech Permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
14
15    <!-- Communication Permissions -->
16    <uses-permission android:name="android.permission.CALL_PHONE" />
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-69
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-66
17    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-75
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-72
18    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
18-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.READ_CALL_LOG" />
19-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:5-72
19-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:22-69
20    <uses-permission android:name="android.permission.READ_CONTACTS" />
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-72
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-69
21    <uses-permission android:name="android.permission.SEND_SMS" />
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-67
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-64
22    <uses-permission android:name="android.permission.READ_SMS" />
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-64
23
24    <!-- Network Permissions -->
25    <uses-permission android:name="android.permission.INTERNET" />
25-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:5-67
25-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:22-64
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-79
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-76
27
28    <!-- System Control Permissions -->
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:5-78
29-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:22-75
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-77
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-88
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-85
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-88
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-85
34
35    <!-- Connectivity Control -->
36    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
36-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:5-76
36-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:22-73
37    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
37-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:5-76
37-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH" />
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:5-68
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:22-65
39    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-74
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-71
40    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
40-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-76
40-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-73
41    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
41-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:5-73
41-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:22-70
42    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:5-78
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:22-75
43    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
43-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:5-79
43-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:22-76
44
45    <!-- Device Features -->
46    <uses-permission android:name="android.permission.CAMERA" />
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:5-65
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:22-62
47    <uses-permission android:name="android.permission.FLASHLIGHT" />
47-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:5-69
47-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:22-66
48    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
48-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-79
48-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-76
49    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-81
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-78
50
51    <!-- Notification and Accessibility -->
52    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
52-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-47:47
52-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-90
53    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
53-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-77
53-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-74
54    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:5-50:47
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:22-82
55    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
56
57    <!-- Storage -->
58    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-80
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-77
59    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-81
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-78
60
61    <!-- Hardware Features -->
62    <uses-feature
62-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:58:5-60:35
63        android:name="android.hardware.microphone"
63-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:9-51
64        android:required="true" />
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:60:9-32
65    <uses-feature
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:61:5-63:36
66        android:name="android.hardware.camera"
66-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:62:9-47
67        android:required="false" />
67-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:9-33
68    <uses-feature
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-66:36
69        android:name="android.hardware.camera.flash"
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:65:9-53
70        android:required="false" />
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:66:9-33
71    <uses-feature
71-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-69:36
72        android:name="android.hardware.bluetooth"
72-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:9-50
73        android:required="false" />
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:9-33
74    <uses-feature
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:70:5-72:36
75        android:name="android.hardware.wifi"
75-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:71:9-45
76        android:required="false" />
76-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:72:9-33
77
78    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
78-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
78-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
79    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
80    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
81    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
81-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:23:5-25:53
81-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:24:9-61
82    <uses-permission android:name="android.permission.REORDER_TASKS" />
82-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:5-72
82-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:22-69
83
84    <permission
84-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
85        android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
85-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
86        android:protectionLevel="signature" />
86-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
87
88    <uses-permission android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
89
90    <queries>
90-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:24:5-28:15
91        <package android:name="androidx.test.orchestrator" />
91-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:9-62
91-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:18-59
92        <package android:name="androidx.test.services" />
92-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:9-58
92-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:18-55
93        <package android:name="com.google.android.apps.common.testing.services" />
93-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:9-83
93-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:18-80
94    </queries>
95
96    <application
96-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-166:19
97        android:name="com.zara.assistant.ZaraApplication"
97-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:75:9-40
98        android:allowBackup="true"
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:76:9-35
99        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
99-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
100        android:dataExtractionRules="@xml/data_extraction_rules"
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:77:9-65
101        android:extractNativeLibs="false"
102        android:fullBackupContent="@xml/backup_rules"
102-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:9-54
103        android:hardwareAccelerated="true"
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:9-43
104        android:icon="@mipmap/ic_launcher"
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:9-43
105        android:label="@string/app_name"
105-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:80:9-41
106        android:largeHeap="true"
106-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:9-33
107        android:networkSecurityConfig="@xml/network_security_config"
107-->[androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:42:18-78
108        android:roundIcon="@mipmap/ic_launcher_round"
108-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:9-54
109        android:supportsRtl="true"
109-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:82:9-35
110        android:theme="@style/Theme.Zara" >
110-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:83:9-42
111
112        <!-- Main Activity -->
113        <activity
113-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:89:9-103:20
114            android:name="com.zara.assistant.ui.MainActivity"
114-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:90:13-44
115            android:exported="true"
115-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:13-36
116            android:launchMode="singleTop"
116-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:94:13-43
117            android:screenOrientation="portrait"
117-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:13-49
118            android:theme="@style/Theme.Zara.NoActionBar" >
118-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:13-58
119            <intent-filter>
119-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:95:13-98:29
120                <action android:name="android.intent.action.MAIN" />
120-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:17-69
120-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:25-66
121
122                <category android:name="android.intent.category.LAUNCHER" />
122-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
122-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
123            </intent-filter>
124            <intent-filter>
124-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:99:13-102:29
125                <action android:name="android.intent.action.VOICE_COMMAND" />
125-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:17-78
125-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:25-75
126
127                <category android:name="android.intent.category.DEFAULT" />
127-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:17-76
127-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:27-73
128            </intent-filter>
129        </activity>
130
131        <!-- Voice Service -->
132        <service
132-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:106:9-110:58
133            android:name="com.zara.assistant.services.ZaraVoiceService"
133-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:107:13-54
134            android:enabled="true"
134-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:13-35
135            android:exported="false"
135-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:109:13-37
136            android:foregroundServiceType="microphone" />
136-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:13-55
137
138        <!-- Wake Word Service -->
139        <service
139-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:113:9-117:58
140            android:name="com.zara.assistant.services.WakeWordService"
140-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:13-53
141            android:enabled="true"
141-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:115:13-35
142            android:exported="false"
142-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:116:13-37
143            android:foregroundServiceType="microphone" />
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:117:13-55
144
145        <!-- Accessibility Service -->
146        <service
146-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:9-130:19
147            android:name="com.zara.assistant.services.ZaraAccessibilityService"
147-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:13-62
148            android:exported="true"
148-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:13-36
149            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
149-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:13-79
150            <intent-filter>
150-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:13-126:29
151                <action android:name="android.accessibilityservice.AccessibilityService" />
151-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:17-92
151-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:25-89
152            </intent-filter>
153
154            <meta-data
154-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:127:13-129:72
155                android:name="android.accessibilityservice"
155-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:128:17-60
156                android:resource="@xml/accessibility_service_config" />
156-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:129:17-69
157        </service>
158
159        <!-- Notification Listener Service -->
160        <service
160-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:133:9-140:19
161            android:name="com.zara.assistant.services.NotificationListenerService"
161-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:13-65
162            android:exported="true"
162-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-36
163            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
163-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-87
164            <intent-filter>
164-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:13-139:29
165                <action android:name="android.service.notification.NotificationListenerService" />
165-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:17-99
165-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:25-96
166            </intent-filter>
167        </service>
168
169        <!-- Device Admin Receiver -->
170        <receiver
170-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:143:9-153:20
171            android:name="com.zara.assistant.services.ZaraDeviceAdminReceiver"
171-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:144:13-61
172            android:exported="true"
172-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:13-36
173            android:permission="android.permission.BIND_DEVICE_ADMIN" >
173-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:13-70
174            <meta-data
174-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:147:13-149:56
175                android:name="android.app.device_admin"
175-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:148:17-56
176                android:resource="@xml/device_admin" />
176-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:149:17-53
177
178            <intent-filter>
178-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:150:13-152:29
179                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:17-82
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:25-79
180            </intent-filter>
181        </receiver>
182
183        <!-- File Provider -->
184        <provider
185            android:name="androidx.core.content.FileProvider"
185-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:157:13-62
186            android:authorities="com.zara.assistant.fileprovider"
186-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:13-64
187            android:exported="false"
187-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:159:13-37
188            android:grantUriPermissions="true" >
188-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:160:13-47
189            <meta-data
189-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:13-163:54
190                android:name="android.support.FILE_PROVIDER_PATHS"
190-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:17-67
191                android:resource="@xml/file_paths" />
191-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:163:17-51
192        </provider>
193
194        <service
194-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
195            android:name="androidx.camera.core.impl.MetadataHolderService"
195-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
196            android:enabled="false"
196-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
197            android:exported="false" >
197-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
198            <meta-data
198-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
199                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
199-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
200                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
200-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
201        </service>
202
203        <provider
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
204            android:name="androidx.startup.InitializationProvider"
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
205            android:authorities="com.zara.assistant.androidx-startup"
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
207            <meta-data
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
208                android:name="androidx.work.WorkManagerInitializer"
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
209                android:value="androidx.startup" />
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
210            <meta-data
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.emoji2.text.EmojiCompatInitializer"
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
212                android:value="androidx.startup" />
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
214-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
215                android:value="androidx.startup" />
215-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
216            <meta-data
216-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
217                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
217-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
218                android:value="androidx.startup" />
218-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
219            <meta-data
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
221                android:value="androidx.startup" />
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
222        </provider>
223
224        <service
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
225            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
227            android:enabled="@bool/enable_system_alarm_service_default"
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
228            android:exported="false" />
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
229        <service
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
230            android:name="androidx.work.impl.background.systemjob.SystemJobService"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
232            android:enabled="@bool/enable_system_job_service_default"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
233            android:exported="true"
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
234            android:permission="android.permission.BIND_JOB_SERVICE" />
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
235        <service
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
236            android:name="androidx.work.impl.foreground.SystemForegroundService"
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
238            android:enabled="@bool/enable_system_foreground_service_default"
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
239            android:exported="false" />
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
240
241        <receiver
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
242            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
244            android:enabled="true"
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
245            android:exported="false" />
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
246        <receiver
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
247            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
249            android:enabled="false"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
250            android:exported="false" >
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
251            <intent-filter>
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
252                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
253                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
254            </intent-filter>
255        </receiver>
256        <receiver
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
257            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
258            android:directBootAware="false"
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
259            android:enabled="false"
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
260            android:exported="false" >
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
261            <intent-filter>
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
262                <action android:name="android.intent.action.BATTERY_OKAY" />
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
263                <action android:name="android.intent.action.BATTERY_LOW" />
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
264            </intent-filter>
265        </receiver>
266        <receiver
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
267            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
269            android:enabled="false"
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
270            android:exported="false" >
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
271            <intent-filter>
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
272                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
273                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
274            </intent-filter>
275        </receiver>
276        <receiver
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
277            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
278            android:directBootAware="false"
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
279            android:enabled="false"
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
280            android:exported="false" >
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
281            <intent-filter>
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
282                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
283            </intent-filter>
284        </receiver>
285        <receiver
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
286            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
287            android:directBootAware="false"
287-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
288            android:enabled="false"
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
289            android:exported="false" >
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
290            <intent-filter>
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
291                <action android:name="android.intent.action.BOOT_COMPLETED" />
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
292                <action android:name="android.intent.action.TIME_SET" />
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
293                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
294            </intent-filter>
295        </receiver>
296        <receiver
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
297            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
298            android:directBootAware="false"
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
299            android:enabled="@bool/enable_system_alarm_service_default"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
300            android:exported="false" >
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
301            <intent-filter>
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
302                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
303            </intent-filter>
304        </receiver>
305        <receiver
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
306            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
308            android:enabled="true"
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
309            android:exported="true"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
310            android:permission="android.permission.DUMP" >
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
311            <intent-filter>
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
312                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
313            </intent-filter>
314        </receiver> <!-- Activity used to block background content while benchmarks are running -->
315        <activity
315-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:32:9-36:20
316            android:name="androidx.benchmark.IsolationActivity"
316-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:33:13-64
317            android:exported="true"
317-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:34:13-36
318            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
318-->[androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:35:13-77
319        </activity>
320        <activity
320-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:27:9-34:20
321            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
321-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:28:13-99
322            android:exported="true"
322-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:29:13-36
323            android:theme="@style/WhiteBackgroundTheme" >
323-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:30:13-56
324            <intent-filter android:priority="-100" >
324-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
324-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
325                <category android:name="android.intent.category.LAUNCHER" />
325-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
325-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
326            </intent-filter>
327        </activity>
328        <activity
328-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:35:9-42:20
329            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
329-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:36:13-95
330            android:exported="true"
330-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:37:13-36
331            android:theme="@style/WhiteBackgroundTheme" >
331-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:38:13-56
332            <intent-filter android:priority="-100" >
332-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
332-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
333                <category android:name="android.intent.category.LAUNCHER" />
333-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
333-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
334            </intent-filter>
335        </activity>
336        <activity
336-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:43:9-50:20
337            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
337-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:44:13-103
338            android:exported="true"
338-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:45:13-36
339            android:theme="@style/WhiteBackgroundDialogTheme" >
339-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:46:13-62
340            <intent-filter android:priority="-100" >
340-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
340-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
341                <category android:name="android.intent.category.LAUNCHER" />
341-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
341-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
342            </intent-filter>
343        </activity>
344
345        <service
345-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
346            android:name="androidx.room.MultiInstanceInvalidationService"
346-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
347            android:directBootAware="true"
347-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
348            android:exported="false" />
348-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
349
350        <receiver
350-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
351            android:name="androidx.tracing.perfetto.TracingReceiver"
351-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
352            android:directBootAware="false"
352-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
353            android:enabled="true"
353-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
354            android:exported="true"
354-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
355            android:permission="android.permission.DUMP" >
355-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
356
357            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
358            <intent-filter>
358-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
359                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
359-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
359-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
360                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
360-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
360-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
361                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
361-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
361-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
362            </intent-filter>
363        </receiver>
364        <receiver
364-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
365            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
365-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
366            android:directBootAware="false"
366-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
367            android:enabled="false"
367-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
368            android:exported="false" >
368-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
369        </receiver>
370        <receiver
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
371            android:name="androidx.profileinstaller.ProfileInstallReceiver"
371-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
372            android:directBootAware="false"
372-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
373            android:enabled="true"
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
374            android:exported="true"
374-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
375            android:permission="android.permission.DUMP" >
375-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
377                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
377-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
377-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
378            </intent-filter>
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
380                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
380-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
380-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
383                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
383-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
383-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
386                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
386-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
386-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
387            </intent-filter>
388        </receiver>
389
390        <provider
390-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
391            android:name="com.microsoft.cognitiveservices.speech.util.InternalContentProvider"
391-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
392            android:authorities="com.zara.assistant.MicrosoftCognitiveServicesSpeech.InternalContentProvider"
392-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
393            android:exported="false" />
393-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
394    </application>
395
396</manifest>
