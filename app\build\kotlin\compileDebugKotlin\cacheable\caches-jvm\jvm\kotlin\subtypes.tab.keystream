kotlin.Annotation-com.zara.assistant.services.NotificationEvent2kotlinx.serialization.internal.GeneratedSerializerandroid.app.Servicekotlin.Enumandroid.app.Application$androidx.work.Configuration.Providerandroid.os.Parcelable8android.service.notification.NotificationListenerService1android.accessibilityservice.AccessibilityService%android.app.admin.DeviceAdminReceiverandroid.os.Binder&androidx.fragment.app.FragmentActivityandroidx.lifecycle.ViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   