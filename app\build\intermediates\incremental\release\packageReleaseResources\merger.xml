<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res"><file name="bottom_sheet_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\bottom_sheet_background_dark.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\dialog_background_dark.xml" qualifiers="" type="drawable"/><file name="fab_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\fab_background.xml" qualifiers="" type="drawable"/><file name="fab_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\fab_background_dark.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_microphone" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_microphone.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_voice_assistant" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_voice_assistant.xml" qualifiers="" type="drawable"/><file name="ic_zara_logo" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\ic_zara_logo.xml" qualifiers="" type="drawable"/><file name="neuro_button_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_button_background.xml" qualifiers="" type="drawable"/><file name="neuro_button_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_button_background_dark.xml" qualifiers="" type="drawable"/><file name="neuro_card_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_card_background.xml" qualifiers="" type="drawable"/><file name="neuro_card_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_card_background_dark.xml" qualifiers="" type="drawable"/><file name="neuro_settings_item_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_settings_item_background.xml" qualifiers="" type="drawable"/><file name="neuro_settings_item_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\neuro_settings_item_background_dark.xml" qualifiers="" type="drawable"/><file name="slider_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\slider_background.xml" qualifiers="" type="drawable"/><file name="slider_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\slider_background_dark.xml" qualifiers="" type="drawable"/><file name="switch_thumb" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\switch_thumb.xml" qualifiers="" type="drawable"/><file name="switch_thumb_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\switch_thumb_dark.xml" qualifiers="" type="drawable"/><file name="switch_track" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\switch_track.xml" qualifiers="" type="drawable"/><file name="switch_track_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\switch_track_dark.xml" qualifiers="" type="drawable"/><file name="voice_button_background" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\voice_button_background.xml" qualifiers="" type="drawable"/><file name="voice_button_background_dark" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\drawable\voice_button_background_dark.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\values\colors.xml" qualifiers=""><color name="deep_teal">#006B5C</color><color name="soft_coral">#FF6B6B</color><color name="luminous_blue">#4FC3F7</color><color name="md_theme_light_primary">#006B5C</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#7FF8E6</color><color name="md_theme_light_onPrimaryContainer">#002019</color><color name="md_theme_light_secondary">#4A635F</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#CCE8E2</color><color name="md_theme_light_onSecondaryContainer">#05201C</color><color name="md_theme_light_tertiary">#426277</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#C6E7FF</color><color name="md_theme_light_onTertiaryContainer">#001E2E</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#F5FFF9</color><color name="md_theme_light_onBackground">#161D1A</color><color name="md_theme_light_surface">#F5FFF9</color><color name="md_theme_light_onSurface">#161D1A</color><color name="md_theme_light_surfaceVariant">#DAE5E1</color><color name="md_theme_light_onSurfaceVariant">#3F4946</color><color name="md_theme_light_outline">#6F7976</color><color name="md_theme_light_outlineVariant">#BEC9C5</color><color name="neuro_background">#E6E7EE</color><color name="neuro_background_dark">#2C2C34</color><color name="neuro_light_shadow">#A3B1C6</color><color name="neuro_light_highlight">#FFFFFF</color><color name="neuro_surface_light">#E6E7EE</color><color name="neuro_surface_elevated_light">#F0F1F8</color><color name="neuro_dark_shadow">#1A1A20</color><color name="neuro_dark_highlight">#3E3E48</color><color name="neuro_surface_dark">#2C2C34</color><color name="neuro_surface_elevated_dark">#363640</color><color name="text_primary_light">#1C1C1E</color><color name="text_secondary_light">#6D6D80</color><color name="text_tertiary_light">#8E8E93</color><color name="text_primary_dark">#FFFFFF</color><color name="text_secondary_dark">#AEAEB2</color><color name="text_tertiary_dark">#8E8E93</color><color name="accent_success">#34C759</color><color name="accent_warning">#FF9500</color><color name="accent_error">#FF3B30</color><color name="accent_info">#007AFF</color><color name="voice_listening">#4FC3F7</color><color name="voice_processing">#FF6B6B</color><color name="voice_speaking">#006B5C</color><color name="voice_idle">#8E8E93</color><color name="gradient_start">#4FC3F7</color><color name="gradient_middle">#006B5C</color><color name="gradient_end">#FF6B6B</color><color name="black">#000000</color><color name="white">#FFFFFF</color><color name="transparent">#00000000</color><color name="md_theme_dark_primary">#63DBCA</color><color name="md_theme_dark_onPrimary">#003730</color><color name="md_theme_dark_primaryContainer">#005146</color><color name="md_theme_dark_onPrimaryContainer">#7FF8E6</color><color name="md_theme_dark_secondary">#B0CCC6</color><color name="md_theme_dark_onSecondary">#1C3531</color><color name="md_theme_dark_secondaryContainer">#334B47</color><color name="md_theme_dark_onSecondaryContainer">#CCE8E2</color><color name="md_theme_dark_tertiary">#B0CBE3</color><color name="md_theme_dark_onTertiary">#1B3447</color><color name="md_theme_dark_tertiaryContainer">#324A60</color><color name="md_theme_dark_onTertiaryContainer">#CCE7FF</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_background">#0E1512</color><color name="md_theme_dark_onBackground">#DEE5E1</color><color name="md_theme_dark_surface">#0E1512</color><color name="md_theme_dark_onSurface">#DEE5E1</color><color name="md_theme_dark_surfaceVariant">#3F4946</color><color name="md_theme_dark_onSurfaceVariant">#BEC9C5</color><color name="md_theme_dark_outline">#889390</color><color name="md_theme_dark_inverseOnSurface">#0E1512</color><color name="md_theme_dark_inverseSurface">#DEE5E1</color><color name="md_theme_dark_inversePrimary">#006B5C</color><color name="md_theme_dark_shadow">#000000</color><color name="md_theme_dark_surfaceTint">#63DBCA</color><color name="md_theme_dark_outlineVariant">#3F4946</color><color name="md_theme_dark_scrim">#000000</color><color name="surface">@color/md_theme_light_surface</color><color name="surface_dark">@color/md_theme_dark_surface</color><color name="outline_variant">@color/md_theme_light_outlineVariant</color><color name="outline_variant_dark">@color/md_theme_dark_outlineVariant</color><color name="primary">@color/md_theme_light_primary</color><color name="gray_50">#FAFAFA</color><color name="gray_100">#F5F5F5</color><color name="gray_200">#EEEEEE</color><color name="gray_300">#E0E0E0</color><color name="gray_400">#BDBDBD</color><color name="gray_500">#9E9E9E</color><color name="gray_600">#757575</color><color name="gray_700">#616161</color><color name="gray_800">#424242</color><color name="gray_900">#212121</color></file><file path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="spacing_xs">4dp</dimen><dimen name="spacing_sm">8dp</dimen><dimen name="spacing_md">16dp</dimen><dimen name="spacing_lg">24dp</dimen><dimen name="spacing_xl">32dp</dimen><dimen name="spacing_xxl">48dp</dimen><dimen name="margin_horizontal">16dp</dimen><dimen name="margin_vertical">16dp</dimen><dimen name="margin_card">16dp</dimen><dimen name="margin_item">8dp</dimen><dimen name="padding_xs">4dp</dimen><dimen name="padding_sm">8dp</dimen><dimen name="padding_md">16dp</dimen><dimen name="padding_lg">24dp</dimen><dimen name="padding_xl">32dp</dimen><dimen name="corner_radius_xs">4dp</dimen><dimen name="corner_radius_sm">8dp</dimen><dimen name="corner_radius_md">12dp</dimen><dimen name="corner_radius_lg">16dp</dimen><dimen name="corner_radius_xl">20dp</dimen><dimen name="corner_radius_xxl">24dp</dimen><dimen name="corner_radius_round">50dp</dimen><dimen name="neuro_elevation">8dp</dimen><dimen name="neuro_blur_radius">16dp</dimen><dimen name="neuro_offset_x">8dp</dimen><dimen name="neuro_offset_y">8dp</dimen><dimen name="neuro_inset_offset">-4dp</dimen><dimen name="voice_button_size">120dp</dimen><dimen name="voice_button_icon_size">48dp</dimen><dimen name="voice_ripple_size">200dp</dimen><dimen name="card_elevation">0dp</dimen><dimen name="card_corner_radius">20dp</dimen><dimen name="card_margin">16dp</dimen><dimen name="card_padding">20dp</dimen><dimen name="button_height">56dp</dimen><dimen name="button_corner_radius">16dp</dimen><dimen name="button_padding_horizontal">24dp</dimen><dimen name="button_padding_vertical">16dp</dimen><dimen name="text_size_xs">10sp</dimen><dimen name="text_size_sm">12sp</dimen><dimen name="text_size_md">14sp</dimen><dimen name="text_size_lg">16sp</dimen><dimen name="text_size_xl">18sp</dimen><dimen name="text_size_xxl">20sp</dimen><dimen name="text_size_headline">24sp</dimen><dimen name="text_size_title">32sp</dimen><dimen name="icon_size_xs">16dp</dimen><dimen name="icon_size_sm">20dp</dimen><dimen name="icon_size_md">24dp</dimen><dimen name="icon_size_lg">32dp</dimen><dimen name="icon_size_xl">48dp</dimen><dimen name="icon_size_xxl">64dp</dimen><dimen name="app_bar_height">64dp</dimen><dimen name="app_bar_elevation">0dp</dimen><dimen name="bottom_nav_height">80dp</dimen><dimen name="bottom_nav_elevation">0dp</dimen><dimen name="fab_size">56dp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="dialog_corner_radius">24dp</dimen><dimen name="dialog_margin">24dp</dimen><dimen name="dialog_padding">24dp</dimen><dimen name="bottom_sheet_corner_radius">24dp</dimen><dimen name="bottom_sheet_peek_height">200dp</dimen><dimen name="settings_item_height">64dp</dimen><dimen name="settings_item_padding">16dp</dimen><dimen name="settings_icon_size">24dp</dimen><dimen name="voice_wave_height">4dp</dimen><dimen name="voice_wave_spacing">2dp</dimen><dimen name="voice_wave_container_height">60dp</dimen><dimen name="min_touch_target">48dp</dimen><dimen name="accessibility_focus_border">2dp</dimen><dimen name="animation_offset">16dp</dimen><dimen name="ripple_radius">24dp</dimen><dimen name="status_bar_height">24dp</dimen><dimen name="navigation_bar_height">48dp</dimen><dimen name="landscape_margin">32dp</dimen><dimen name="landscape_padding">24dp</dimen></file><file path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Zara</string><string name="app_description">Your intelligent AI voice assistant</string><string name="welcome_message">Hi, I\'m Zara, your AI assistant. How can I help you today?</string><string name="tap_to_speak">Tap to speak</string><string name="listening">Listening...</string><string name="processing">Processing...</string><string name="speaking">Speaking...</string><string name="wake_word_active">Wake word detection active</string><string name="wake_word_inactive">Wake word detection inactive</string><string name="voice_idle">Ready to listen</string><string name="voice_listening_wake">Say \"Hey Zara\" to start</string><string name="voice_listening_manual">Listening for your command</string><string name="voice_processing_ai">Thinking about your request</string><string name="voice_speaking_response">Zara is responding</string><string name="voice_error">Sorry, I didn\'t catch that</string><string name="permission_microphone_title">Microphone Access</string><string name="permission_microphone_description">Zara needs microphone access to listen to your voice commands</string><string name="permission_accessibility_title">Accessibility Service</string><string name="permission_accessibility_description">Allow Zara to control your device and apps through voice commands</string><string name="permission_notification_title">Notification Access</string><string name="permission_notification_description">Let Zara read and respond to your notifications</string><string name="permission_overlay_title">Display Over Apps</string><string name="permission_overlay_description">Allow Zara to show voice interface over other apps</string><string name="permission_phone_title">Phone Access</string><string name="permission_phone_description">Enable Zara to make calls and send messages for you</string><string name="permission_location_title">Location Access</string><string name="permission_location_description">Help Zara provide location-based assistance</string><string name="settings">Settings</string><string name="voice_settings">Voice Settings</string><string name="wake_word_settings">Wake Word</string><string name="ai_settings">AI Settings</string><string name="privacy_settings">Privacy</string><string name="accessibility_settings">Accessibility</string><string name="about">About</string><string name="wake_word_sensitivity">Wake Word Sensitivity</string><string name="speech_rate">Speech Rate</string><string name="speech_pitch">Speech Pitch</string><string name="auto_listen_after_response">Auto Listen After Response</string><string name="ai_personality">AI Personality</string><string name="ai_response_style">Response Style</string><string name="conversation_history">Conversation History</string><string name="voice_data_storage">Voice Data Storage</string><string name="analytics_enabled">Analytics</string><string name="clear_data">Clear All Data</string><string name="export_data">Export Data</string><string name="wake_word_enabled">Wake word detection enabled</string><string name="wake_word_disabled">Wake word detection disabled</string><string name="wake_word_sensitivity_low">Low</string><string name="wake_word_sensitivity_medium">Medium</string><string name="wake_word_sensitivity_high">High</string><string name="voice_language">Voice Language</string><string name="ai_response_brief">Brief</string><string name="ai_response_detailed">Detailed</string><string name="ai_response_conversational">Conversational</string><string name="ai_personality_professional">Professional</string><string name="ai_personality_friendly">Friendly</string><string name="ai_personality_casual">Casual</string><string name="data_collection">Data Collection</string><string name="accessibility_service_description">Zara Accessibility Service allows voice control of your device, including opening apps, adjusting settings, and interacting with UI elements.</string><string name="accessibility_service_summary">Voice control for your Android device</string><string name="wake_word_service_title">Zara is listening</string><string name="wake_word_service_description">Say \"Hey Zara\" to start a conversation</string><string name="voice_processing_notification">Processing your request...</string><string name="error_microphone_permission">Microphone permission is required for voice commands</string><string name="error_network_connection">Please check your internet connection</string><string name="error_ai_service_unavailable">AI service is temporarily unavailable</string><string name="error_speech_recognition">Speech recognition failed</string><string name="error_text_to_speech">Text-to-speech is not available</string><string name="error_accessibility_service">Accessibility service is not enabled</string><string name="error_notification_access">Notification access is not granted</string><string name="commands_help_title">Voice Commands</string><string name="commands_device_control">Device Control</string><string name="commands_apps">App Management</string><string name="commands_communication">Communication</string><string name="commands_information">Information</string><string name="commands_entertainment">Entertainment</string><string name="cmd_turn_on_wifi">\"Turn on WiFi\"</string><string name="cmd_turn_off_bluetooth">\"Turn off Bluetooth\"</string><string name="cmd_increase_volume">\"Increase volume\"</string><string name="cmd_set_brightness">\"Set brightness to 50%\"</string><string name="cmd_enable_airplane_mode">\"Enable airplane mode\"</string><string name="cmd_open_app">\"Open [app name]\"</string><string name="cmd_close_app">\"Close [app name]\"</string><string name="cmd_switch_app">\"Switch to [app name]\"</string><string name="cmd_call_contact">\"Call [contact name]\"</string><string name="cmd_send_message">\"Send message to [contact]\"</string><string name="cmd_read_notifications">\"Read my notifications\"</string><string name="cmd_weather">\"What\'s the weather like?\"</string><string name="cmd_time">\"What time is it?\"</string><string name="cmd_news">\"Tell me the news\"</string><string name="cmd_search">\"Search for [query]\"</string><string name="cmd_play_music">\"Play music\"</string><string name="cmd_next_song">\"Next song\"</string><string name="cmd_pause_music">\"Pause music\"</string><string name="response_wifi_enabled">WiFi has been turned on</string><string name="response_bluetooth_disabled">Bluetooth has been turned off</string><string name="response_volume_changed">Volume adjusted</string><string name="response_app_opened">Opening %1$s</string><string name="response_calling_contact">Calling %1$s</string><string name="response_no_notifications">You have no new notifications</string><string name="version">Version</string><string name="build_date">Build Date</string><string name="developer">Developed by</string><string name="privacy_policy">Privacy Policy</string><string name="terms_of_service">Terms of Service</string><string name="open_source_licenses">Open Source Licenses</string><string name="enable">Enable</string><string name="disable">Disable</string><string name="grant">Grant</string><string name="deny">Deny</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="save">Save</string><string name="reset">Reset</string><string name="delete">Delete</string><string name="share">Share</string><string name="copy">Copy</string><string name="retry">Retry</string><string name="close">Close</string><string name="done">Done</string><string name="next">Next</string><string name="previous">Previous</string><string name="continue_text">Continue</string><string name="skip">Skip</string><string name="stop">Stop</string></file><file path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Zara" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/deep_teal</item>
        <item name="colorPrimaryDark">@color/deep_teal</item>
        <item name="colorAccent">@color/soft_coral</item>

        
        <item name="android:colorBackground">@color/neuro_background</item>
        <item name="android:textColorPrimary">@color/text_primary_light</item>
        <item name="android:textColorSecondary">@color/text_secondary_light</item>
        
        
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style><style name="Theme.Zara.NoActionBar" parent="Theme.Zara">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="NeumorphismButton">
        <item name="android:background">@drawable/neuro_button_background</item>
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingHorizontal">24dp</item>
        <item name="android:gravity">center</item>
    </style><style name="NeumorphismCard">
        <item name="android:background">@drawable/neuro_card_background</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:padding">16dp</item>
    </style><style name="VoiceButton" parent="NeumorphismButton">
        <item name="android:background">@drawable/voice_button_background</item>
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:textSize">0sp</item>
    </style><style name="SettingsItem">
        <item name="android:background">@drawable/neuro_settings_item_background</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">4dp</item>
    </style><style name="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@android:color/black</item>
    </style><style name="TextAppearance.Zara" parent="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style><style name="TextAppearance.Zara.Headline1" parent="TextAppearance.Zara">
        <item name="android:textSize">32sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.Zara.Headline2" parent="TextAppearance.Zara">
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.Zara.Body1" parent="TextAppearance.Zara">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.Zara.Body2" parent="TextAppearance.Zara">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style><style name="TextAppearance.Zara.Caption" parent="TextAppearance.Zara">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/text_tertiary_light</item>
    </style><style name="VoiceStateText" parent="TextAppearance.Zara.Body1">
        <item name="android:textAlignment">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style><style name="NeumorphismSwitch">
        <item name="android:thumb">@drawable/switch_thumb</item>
        <item name="android:track">@drawable/switch_track</item>
    </style><style name="NeumorphismSlider">
        <item name="android:background">@drawable/slider_background</item>
    </style><style name="NeumorphismDialog">
        <item name="android:background">@drawable/dialog_background</item>
        <item name="android:windowBackground">@color/transparent</item>
    </style><style name="NeumorphismBottomSheet">
        <item name="android:background">@drawable/bottom_sheet_background</item>
    </style><style name="NeumorphismFAB">
        <item name="android:background">@drawable/fab_background</item>
        <item name="android:elevation">0dp</item>
    </style></file><file path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Zara" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/luminous_blue</item>
        <item name="colorPrimaryDark">@color/deep_teal</item>
        <item name="colorAccent">@color/soft_coral</item>

        
        <item name="android:colorBackground">@color/neuro_background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary_dark</item>
        <item name="android:textColorSecondary">@color/text_secondary_dark</item>
        
        
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style><style name="NeumorphismButton">
        <item name="android:background">@drawable/neuro_button_background_dark</item>
        <item name="android:textColor">@color/text_primary_dark</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingHorizontal">24dp</item>
        <item name="android:gravity">center</item>
    </style><style name="NeumorphismCard">
        <item name="android:background">@drawable/neuro_card_background_dark</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:padding">16dp</item>
    </style><style name="VoiceButton" parent="NeumorphismButton">
        <item name="android:background">@drawable/voice_button_background_dark</item>
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:textSize">0sp</item>
    </style><style name="SettingsItem">
        <item name="android:background">@drawable/neuro_settings_item_background_dark</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">4dp</item>
    </style><style name="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@android:color/white</item>
    </style><style name="TextAppearance.Zara" parent="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/text_primary_dark</item>
    </style><style name="TextAppearance.Zara.Headline1" parent="TextAppearance.Zara">
        <item name="android:textSize">32sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.Zara.Headline2" parent="TextAppearance.Zara">
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.Zara.Body1" parent="TextAppearance.Zara">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.Zara.Body2" parent="TextAppearance.Zara">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_dark</item>
    </style><style name="TextAppearance.Zara.Caption" parent="TextAppearance.Zara">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/text_tertiary_dark</item>
    </style><style name="VoiceStateText" parent="TextAppearance.Zara.Body1">
        <item name="android:textAlignment">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/text_secondary_dark</item>
    </style><style name="NeumorphismSwitch">
        <item name="android:thumb">@drawable/switch_thumb_dark</item>
        <item name="android:track">@drawable/switch_track_dark</item>
    </style><style name="NeumorphismSlider">
        <item name="android:background">@drawable/slider_background_dark</item>
    </style><style name="NeumorphismDialog">
        <item name="android:background">@drawable/dialog_background_dark</item>
        <item name="android:windowBackground">@color/transparent</item>
    </style><style name="NeumorphismBottomSheet">
        <item name="android:background">@drawable/bottom_sheet_background_dark</item>
    </style><style name="NeumorphismFAB">
        <item name="android:background">@drawable/fab_background_dark</item>
        <item name="android:elevation">0dp</item>
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="device_admin" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\xml\device_admin.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>