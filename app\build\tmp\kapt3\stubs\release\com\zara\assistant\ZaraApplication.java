package com.zara.assistant;

/**
 * Zara Application - Ultra-Fast AI Voice Assistant
 * Features:
 * - Azure Speech Services (STT/TTS)
 * - Dual AI (Cohere/Perplexity)
 * - 30-second auto-listen
 * - Hindi/English support
 * - Neumorphism UI
 * - System controls
 * - Performance optimized
 */
@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u0000 \u00132\u00020\u00012\u00020\u0002:\u0001\u0013B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\u0010\u001a\u00020\u0011H\u0002J\b\u0010\u0012\u001a\u00020\u0011H\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\u00020\u00078VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u001e\u0010\n\u001a\u00020\u000b8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0014"}, d2 = {"Lcom/zara/assistant/ZaraApplication;", "Landroid/app/Application;", "Landroidx/work/Configuration$Provider;", "()V", "applicationScope", "Lkotlinx/coroutines/CoroutineScope;", "workManagerConfiguration", "Landroidx/work/Configuration;", "getWorkManagerConfiguration", "()Landroidx/work/Configuration;", "workerFactory", "Landroidx/hilt/work/HiltWorkerFactory;", "getWorkerFactory", "()Landroidx/hilt/work/HiltWorkerFactory;", "setWorkerFactory", "(Landroidx/hilt/work/HiltWorkerFactory;)V", "createNotificationChannels", "", "onCreate", "Companion", "app_release"})
public final class ZaraApplication extends android.app.Application implements androidx.work.Configuration.Provider {
    @javax.inject.Inject()
    public androidx.hilt.work.HiltWorkerFactory workerFactory;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope applicationScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String VOICE_SERVICE_CHANNEL = "voice_service_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WAKE_WORD_CHANNEL = "wake_word_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AI_PROCESSING_CHANNEL = "ai_processing_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SYSTEM_CONTROL_CHANNEL = "system_control_channel";
    public static final int VOICE_SERVICE_ID = 1001;
    public static final int WAKE_WORD_SERVICE_ID = 1002;
    public static final int AI_SERVICE_ID = 1003;
    public static final int ACCESSIBILITY_SERVICE_ID = 1004;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.ZaraApplication.Companion Companion = null;
    
    public ZaraApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.hilt.work.HiltWorkerFactory getWorkerFactory() {
        return null;
    }
    
    public final void setWorkerFactory(@org.jetbrains.annotations.NotNull()
    androidx.hilt.work.HiltWorkerFactory p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    private final void createNotificationChannels() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.work.Configuration getWorkManagerConfiguration() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/ZaraApplication$Companion;", "", "()V", "ACCESSIBILITY_SERVICE_ID", "", "AI_PROCESSING_CHANNEL", "", "AI_SERVICE_ID", "SYSTEM_CONTROL_CHANNEL", "VOICE_SERVICE_CHANNEL", "VOICE_SERVICE_ID", "WAKE_WORD_CHANNEL", "WAKE_WORD_SERVICE_ID", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}