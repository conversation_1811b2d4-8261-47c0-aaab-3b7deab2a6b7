package com.zara.assistant.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.VoiceState
import kotlin.math.*
import kotlin.random.Random

/**
 * Voice Visualization - Real-time audio wave animation
 */
@Composable
fun VoiceVisualization(
    voiceState: VoiceState,
    modifier: Modifier = Modifier,
    barCount: Int = Constants.UI.VOICE_BARS_COUNT,
    barWidth: Dp = Constants.UI.VOICE_BAR_WIDTH.dp,
    barSpacing: Dp = Constants.UI.VOICE_BAR_SPACING.dp,
    maxHeight: Dp = 60.dp,
    color: Color = MaterialTheme.colorScheme.primary,
    animationDuration: Int = Constants.UI.VOICE_ANIMATION_DURATION.toInt()
) {
    val density = LocalDensity.current
    
    // Animation state
    val infiniteTransition = rememberInfiniteTransition(label = "voice_animation")
    
    // Create animated values for each bar
    val barHeights = remember { mutableStateListOf<Float>() }
    
    // Initialize bar heights
    LaunchedEffect(barCount) {
        barHeights.clear()
        repeat(barCount) {
            barHeights.add(0.1f)
        }
    }
    
    // Animate bars based on voice state
    LaunchedEffect(voiceState) {
        when (voiceState) {
            VoiceState.LISTENING -> {
                // Simulate listening animation
                while (true) {
                    for (i in barHeights.indices) {
                        barHeights[i] = Random.nextFloat() * 0.8f + 0.2f
                    }
                    kotlinx.coroutines.delay(animationDuration.toLong())
                }
            }
            VoiceState.PROCESSING -> {
                // Processing animation - wave pattern
                while (true) {
                    for (i in barHeights.indices) {
                        val phase = (i.toFloat() / barCount) * 2 * PI
                        barHeights[i] = (sin(System.currentTimeMillis() / 1000.0 + phase.toDouble()) * 0.4 + 0.6).coerceIn(0.1, 1.0).toFloat()
                    }
                    kotlinx.coroutines.delay(animationDuration.toLong())
                }
            }
            VoiceState.SPEAKING -> {
                // Speaking animation - more dynamic
                while (true) {
                    for (i in barHeights.indices) {
                        barHeights[i] = Random.nextFloat() * 0.9f + 0.1f
                    }
                    kotlinx.coroutines.delay((animationDuration * 0.7f).toLong())
                }
            }
            VoiceState.IDLE -> {
                // Idle animation - minimal movement
                for (i in barHeights.indices) {
                    barHeights[i] = 0.1f + Random.nextFloat() * 0.1f
                }
            }
            VoiceState.ERROR -> {
                // Error animation - red pulsing
                while (true) {
                    val pulse = (sin(System.currentTimeMillis() / 500.0) * 0.2 + 0.3).coerceIn(0.1, 0.5).toFloat()
                    for (i in barHeights.indices) {
                        barHeights[i] = pulse
                    }
                    kotlinx.coroutines.delay(50)
                }
            }
        }
    }
    
    // Draw the visualization
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(maxHeight)
    ) {
        drawVoiceBars(
            barHeights = barHeights,
            barWidth = with(density) { barWidth.toPx() },
            barSpacing = with(density) { barSpacing.toPx() },
            maxHeight = with(density) { maxHeight.toPx() },
            color = color
        )
    }
}

/**
 * Draw voice bars on canvas
 */
private fun DrawScope.drawVoiceBars(
    barHeights: List<Float>,
    barWidth: Float,
    barSpacing: Float,
    maxHeight: Float,
    color: Color
) {
    val totalWidth = size.width
    val barCount = barHeights.size
    val totalBarsWidth = barCount * barWidth + (barCount - 1) * barSpacing
    val startX = (totalWidth - totalBarsWidth) / 2f
    
    barHeights.forEachIndexed { index, height ->
        val x = startX + index * (barWidth + barSpacing)
        val barHeight = height * maxHeight
        val y = (size.height - barHeight) / 2f
        
        drawRect(
            color = color,
            topLeft = androidx.compose.ui.geometry.Offset(x, y),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )
    }
}

/**
 * Circular Voice Visualization - For voice button
 */
@Composable
fun CircularVoiceVisualization(
    voiceState: VoiceState,
    modifier: Modifier = Modifier,
    radius: Dp = 40.dp,
    ringCount: Int = 3,
    color: Color = MaterialTheme.colorScheme.primary
) {
    val infiniteTransition = rememberInfiniteTransition(label = "circular_voice_animation")
    
    val animatedValues = remember { mutableStateListOf<Float>() }
    
    // Initialize animated values
    LaunchedEffect(ringCount) {
        animatedValues.clear()
        repeat(ringCount) {
            animatedValues.add(0f)
        }
    }
    
    // Animate rings based on voice state
    LaunchedEffect(voiceState) {
        when (voiceState) {
            VoiceState.LISTENING, VoiceState.SPEAKING -> {
                while (true) {
                    for (i in animatedValues.indices) {
                        animatedValues[i] = Random.nextFloat() * 0.8f + 0.2f
                    }
                    kotlinx.coroutines.delay(200)
                }
            }
            VoiceState.PROCESSING -> {
                // Processing animation - expanding rings
                val time = System.currentTimeMillis() / 1500.0
                for (i in animatedValues.indices) {
                    val delay = i * 0.3f
                    animatedValues[i] = ((time + delay) % 1.0).coerceIn(0.0, 1.0).toFloat()
                }
            }
            VoiceState.IDLE -> {
                for (i in animatedValues.indices) {
                    animatedValues[i] = 0.1f
                }
            }
            VoiceState.ERROR -> {
                // Error animation - pulsing rings
                val pulse = (sin(System.currentTimeMillis() / 300.0) * 0.3 + 0.5).coerceIn(0.2, 0.8).toFloat()
                for (i in animatedValues.indices) {
                    animatedValues[i] = pulse
                }
            }
        }
    }
    
    Canvas(
        modifier = modifier.size(radius * 2)
    ) {
        drawCircularVoiceRings(
            animatedValues = animatedValues,
            radius = radius.toPx(),
            color = color
        )
    }
}

/**
 * Draw circular voice rings
 */
private fun DrawScope.drawCircularVoiceRings(
    animatedValues: List<Float>,
    radius: Float,
    color: Color
) {
    val center = androidx.compose.ui.geometry.Offset(size.width / 2f, size.height / 2f)
    
    animatedValues.forEachIndexed { index, value ->
        val ringRadius = radius * (0.3f + index * 0.2f) * value
        val alpha = (1f - value) * 0.7f + 0.3f
        
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = ringRadius,
            center = center,
            style = androidx.compose.ui.graphics.drawscope.Stroke(width = 2.dp.toPx())
        )
    }
}

/**
 * Voice State Indicator - Simple state indicator
 */
@Composable
fun VoiceStateIndicator(
    voiceState: VoiceState,
    modifier: Modifier = Modifier,
    size: Dp = 12.dp
) {
    val color = when (voiceState) {
        VoiceState.IDLE -> MaterialTheme.colorScheme.outline
        VoiceState.LISTENING -> MaterialTheme.colorScheme.primary
        VoiceState.PROCESSING -> MaterialTheme.colorScheme.secondary
        VoiceState.SPEAKING -> MaterialTheme.colorScheme.tertiary
        VoiceState.ERROR -> MaterialTheme.colorScheme.error
    }
    
    val infiniteTransition = rememberInfiniteTransition(label = "state_indicator")
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = if (voiceState == VoiceState.IDLE) 0.5f else 1f,
        targetValue = if (voiceState == VoiceState.IDLE) 0.5f else 0.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "indicator_alpha"
    )
    
    Canvas(
        modifier = modifier.size(size)
    ) {
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = size.toPx() / 2f
        )
    }
}
