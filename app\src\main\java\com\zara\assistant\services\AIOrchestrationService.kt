package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.*
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI Orchestration Service - Routes commands to appropriate AI services
 * Handles Cohere for conversations and Perplexity for information queries
 */
@Singleton
class AIOrchestrationService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: ApiKeyManager,
    private val performanceUtils: PerformanceUtils,
    private val cohereAIService: CohereAIService,
    private val perplexityAIService: PerplexityAIService
) {
    
    companion object {
        private const val TAG = "AIOrchestration"
    }
    
    // Conversation history for context
    private val conversationHistory = mutableListOf<Conversation>()
    private val maxHistorySize = 10

    /**
     * Process voice command with appropriate AI service
     */
    suspend fun processCommand(command: VoiceCommand): Result<AIResponse> = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("AI_ProcessCommand")
            
            Log.d(TAG, "🧠 Processing AI command: ${command.originalText}")
            
            // Classify the command to determine appropriate AI service
            val classification = classifyCommand(command.originalText, command.language)
            
            val result = when (classification.category) {
                CommandCategory.INFORMATION -> {
                    // Use Perplexity for information queries
                    Log.d(TAG, "📊 Routing to Perplexity for information query")
                    perplexityAIService.getResponse(command.originalText, command.language)
                }
                
                CommandCategory.AI_CONVERSATION -> {
                    // Use Cohere for conversational AI
                    Log.d(TAG, "💬 Routing to Cohere for conversation")
                    cohereAIService.getResponse(
                        message = command.originalText,
                        language = command.language,
                        conversationHistory = conversationHistory.takeLast(5)
                    )
                }
                
                else -> {
                    // Default to Cohere for general queries
                    Log.d(TAG, "🤖 Routing to Cohere (default)")
                    cohereAIService.getResponse(
                        message = command.originalText,
                        language = command.language,
                        conversationHistory = conversationHistory.takeLast(3)
                    )
                }
            }
            
            performanceUtils.endMeasurement(measurementId)
            
            // Save to conversation history if successful
            if (result.isSuccess) {
                val response = result.getOrNull()
                if (response != null) {
                    saveToHistory(command.originalText, response.text, command.language, response.source, response.processingTimeMs)
                }
            }
            
            Log.d(TAG, "✅ AI processing completed: ${result.isSuccess}")
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in AI orchestration: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Classify command to determine appropriate AI service
     */
    private fun classifyCommand(text: String, language: String): CommandClassification {
        val normalizedText = text.lowercase(Locale.getDefault())
        
        // Information query patterns
        val informationPatterns = listOf(
            "what is", "what are", "who is", "who are", "when is", "when did", "where is", "where are",
            "how much", "how many", "search for", "find", "lookup", "weather", "news", "stock",
            "definition of", "meaning of", "explain", "tell me about", "information about"
        )
        
        // Conversational patterns
        val conversationalPatterns = listOf(
            "hello", "hi", "hey", "good morning", "good evening", "how are you", "what's up",
            "thank you", "thanks", "please", "can you", "would you", "i need", "i want",
            "help me", "assist me", "advice", "suggest", "recommend"
        )
        
        // Check for information queries
        if (informationPatterns.any { pattern -> normalizedText.contains(pattern) }) {
            return CommandClassification(
                category = CommandCategory.INFORMATION,
                intent = CommandIntent.ASK_QUESTION,
                confidence = 0.8f
            )
        }
        
        // Check for conversational queries
        if (conversationalPatterns.any { pattern -> normalizedText.contains(pattern) }) {
            return CommandClassification(
                category = CommandCategory.AI_CONVERSATION,
                intent = CommandIntent.GENERAL_CHAT,
                confidence = 0.7f
            )
        }
        
        // Check for question words (likely information queries)
        if (normalizedText.startsWith("what") || normalizedText.startsWith("who") || 
            normalizedText.startsWith("when") || normalizedText.startsWith("where") || 
            normalizedText.startsWith("why") || normalizedText.startsWith("how")) {
            return CommandClassification(
                category = CommandCategory.INFORMATION,
                intent = CommandIntent.ASK_QUESTION,
                confidence = 0.6f
            )
        }
        
        // Default to conversation
        return CommandClassification(
            category = CommandCategory.AI_CONVERSATION,
            intent = CommandIntent.GENERAL_CHAT,
            confidence = 0.5f
        )
    }

    /**
     * Save conversation to history
     */
    private fun saveToHistory(
        userMessage: String,
        aiResponse: String,
        language: String,
        source: AISource,
        processingTime: Long
    ) {
        val conversation = Conversation(
            id = UUID.randomUUID().toString(),
            userMessage = userMessage,
            aiResponse = aiResponse,
            language = language,
            timestamp = Date(),
            source = source,
            processingTimeMs = processingTime
        )
        
        conversationHistory.add(conversation)
        
        // Keep only recent conversations
        if (conversationHistory.size > maxHistorySize) {
            conversationHistory.removeAt(0)
        }
        
        Log.d(TAG, "💾 Saved conversation to history (${conversationHistory.size}/$maxHistorySize)")
    }

    /**
     * Get conversation history
     */
    fun getConversationHistory(): List<Conversation> {
        return conversationHistory.toList()
    }

    /**
     * Clear conversation history
     */
    fun clearConversationHistory() {
        conversationHistory.clear()
        Log.d(TAG, "🗑️ Conversation history cleared")
    }

    /**
     * Get AI health status
     */
    suspend fun getAIHealthStatus(): AIHealthStatus = withContext(Dispatchers.IO) {
        try {
            val cohereAvailable = cohereAIService.isAvailable()
            val perplexityAvailable = perplexityAIService.isAvailable()
            
            // Calculate average response time from recent conversations
            val recentTimes = conversationHistory.takeLast(5).map { it.processingTimeMs }
            val averageResponseTime = if (recentTimes.isNotEmpty()) {
                recentTimes.average().toLong()
            } else {
                0L
            }
            
            // Simple error rate calculation (placeholder)
            val errorRate = 0.0f // Would be calculated from actual error tracking
            
            AIHealthStatus(
                cohereAvailable = cohereAvailable,
                perplexityAvailable = perplexityAvailable,
                localProcessingAvailable = true,
                averageResponseTime = averageResponseTime,
                errorRate = errorRate
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting AI health status: ${e.message}", e)
            AIHealthStatus(
                cohereAvailable = false,
                perplexityAvailable = false,
                localProcessingAvailable = true,
                averageResponseTime = 0L,
                errorRate = 1.0f
            )
        }
    }

    /**
     * Test AI services connectivity
     */
    suspend fun testAIServices(): Map<String, Boolean> = withContext(Dispatchers.IO) {
        val results = mutableMapOf<String, Boolean>()
        
        try {
            // Test Cohere
            val cohereTest = cohereAIService.testConnection()
            results["Cohere"] = cohereTest.isSuccess
            
            // Test Perplexity
            val perplexityTest = perplexityAIService.testConnection()
            results["Perplexity"] = perplexityTest.isSuccess
            
            Log.d(TAG, "🧪 AI services test results: $results")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error testing AI services: ${e.message}", e)
            results["Error"] = false
        }
        
        results
    }

    /**
     * Get recommended AI service for query type
     */
    fun getRecommendedAIService(text: String): AISource {
        val classification = classifyCommand(text, Constants.Voice.LANGUAGE_ENGLISH)
        
        return when (classification.category) {
            CommandCategory.INFORMATION -> AISource.PERPLEXITY
            CommandCategory.AI_CONVERSATION -> AISource.COHERE
            else -> AISource.COHERE
        }
    }

    /**
     * Process with specific AI service
     */
    suspend fun processWithSpecificAI(
        text: String,
        language: String,
        aiSource: AISource
    ): Result<AIResponse> = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("AI_ProcessSpecific")
            
            Log.d(TAG, "🎯 Processing with specific AI: $aiSource")
            
            val result = when (aiSource) {
                AISource.COHERE -> {
                    cohereAIService.getResponse(text, language, conversationHistory.takeLast(5))
                }
                AISource.PERPLEXITY -> {
                    perplexityAIService.getResponse(text, language)
                }
                AISource.LOCAL -> {
                    // Local processing would be handled elsewhere
                    Result.failure(Exception("Local processing not handled here"))
                }
            }
            
            performanceUtils.endMeasurement(measurementId)
            
            // Save to history if successful
            if (result.isSuccess) {
                val response = result.getOrNull()
                if (response != null) {
                    saveToHistory(text, response.text, language, response.source, response.processingTimeMs)
                }
            }
            
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing with specific AI: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Get performance metrics
     */
    fun getPerformanceMetrics(): Map<String, Any> {
        val metrics = mutableMapOf<String, Any>()
        
        if (conversationHistory.isNotEmpty()) {
            val responseTimes = conversationHistory.map { it.processingTimeMs }
            metrics["averageResponseTime"] = responseTimes.average()
            metrics["minResponseTime"] = responseTimes.minOrNull() ?: 0L
            metrics["maxResponseTime"] = responseTimes.maxOrNull() ?: 0L
            metrics["totalConversations"] = conversationHistory.size
            
            // AI source distribution
            val sourceDistribution = conversationHistory.groupBy { it.source }
                .mapValues { it.value.size }
            metrics["sourceDistribution"] = sourceDistribution
        }
        
        return metrics
    }
}
