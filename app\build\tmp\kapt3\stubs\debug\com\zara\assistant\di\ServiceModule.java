package com.zara.assistant.di;

/**
 * Service Module - Provides all Zara services for dependency injection
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J2\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0007J\u0012\u0010\u000f\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\"\u0010\u0010\u001a\u00020\f2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J\"\u0010\u0011\u001a\u00020\u00122\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\nH\u0007J\u0012\u0010\u0015\u001a\u00020\n2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\"\u0010\u0016\u001a\u00020\u000e2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J\u001a\u0010\u0017\u001a\u00020\u00142\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0007J\"\u0010\u0018\u001a\u00020\u00192\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J\"\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007\u00a8\u0006\u001c"}, d2 = {"Lcom/zara/assistant/di/ServiceModule;", "", "()V", "provideAIOrchestrationService", "Lcom/zara/assistant/services/AIOrchestrationService;", "context", "Landroid/content/Context;", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "performanceUtils", "Lcom/zara/assistant/utils/PerformanceUtils;", "cohereAIService", "Lcom/zara/assistant/services/CohereAIService;", "perplexityAIService", "Lcom/zara/assistant/services/PerplexityAIService;", "provideApiKeyManager", "provideCohereAIService", "provideLocalCommandProcessor", "Lcom/zara/assistant/services/LocalCommandProcessor;", "systemControlManager", "Lcom/zara/assistant/services/SystemControlManager;", "providePerformanceUtils", "providePerplexityAIService", "provideSystemControlManager", "provideZaraSTTService", "Lcom/zara/assistant/services/ZaraSTTService;", "provideZaraTTSService", "Lcom/zara/assistant/services/ZaraTTSService;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class ServiceModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.di.ServiceModule INSTANCE = null;
    
    private ServiceModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.ApiKeyManager provideApiKeyManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.PerformanceUtils providePerformanceUtils(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.ZaraSTTService provideZaraSTTService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.ZaraTTSService provideZaraTTSService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.SystemControlManager provideSystemControlManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.CohereAIService provideCohereAIService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.PerplexityAIService providePerplexityAIService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.LocalCommandProcessor provideLocalCommandProcessor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.SystemControlManager systemControlManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.AIOrchestrationService provideAIOrchestrationService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.PerformanceUtils performanceUtils, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.CohereAIService cohereAIService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.PerplexityAIService perplexityAIService) {
        return null;
    }
}