package com.zara.assistant.domain.repository;

/**
 * Permission Repository Interface
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\nH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\r\u001a\u00020\u000eH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u0010"}, d2 = {"Lcom/zara/assistant/domain/repository/PermissionRepository;", "", "areAllRequiredPermissionsGranted", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkPermission", "permission", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllPermissionStatuses", "", "Lcom/zara/assistant/domain/model/PermissionStatus;", "getMissingPermissions", "openAppSettings", "", "requestPermission", "app_release"})
public abstract interface PermissionRepository {
    
    /**
     * Get all permission statuses
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllPermissionStatuses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.PermissionStatus>> $completion);
    
    /**
     * Check specific permission
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object checkPermission(@org.jetbrains.annotations.NotNull()
    java.lang.String permission, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * Request permission
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object requestPermission(@org.jetbrains.annotations.NotNull()
    java.lang.String permission, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * Check if all required permissions are granted
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object areAllRequiredPermissionsGranted(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * Get missing permissions
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMissingPermissions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    /**
     * Open app settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object openAppSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}