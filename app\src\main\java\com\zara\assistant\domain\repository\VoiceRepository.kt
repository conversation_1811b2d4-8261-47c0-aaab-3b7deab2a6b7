package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * Voice Repository Interface - Defines voice-related operations
 */
interface VoiceRepository {
    
    // STT Operations
    suspend fun startListening(language: String): Result<Boolean>
    suspend fun stopListening(): Result<Boolean>
    fun getListeningState(): Flow<Boolean>
    
    // TTS Operations
    suspend fun speak(text: String, language: String? = null): Result<Boolean>
    suspend fun stopSpeaking(): Result<Boolean>
    fun getSpeakingState(): Flow<Boolean>
    
    // Voice Commands
    suspend fun processVoiceCommand(command: VoiceCommand): Result<AIResponse>
    fun getVoiceCommands(): Flow<List<VoiceCommand>>
    
    // Voice Settings
    suspend fun updateVoiceSettings(settings: VoiceSettings): Result<Boolean>
    suspend fun getVoiceSettings(): Result<VoiceSettings>
    
    // Wake Word
    suspend fun enableWakeWord(enabled: Boolean): Result<Boolean>
    fun getWakeWordState(): Flow<Boolean>
}

/**
 * Voice Settings Model
 */
data class VoiceSettings(
    val speechRate: Float = 1.0f,
    val speechPitch: Float = 1.0f,
    val selectedVoice: String = "JennyNeural",
    val language: String = "auto",
    val wakeWordEnabled: Boolean = true,
    val autoListenEnabled: Boolean = true,
    val autoListenTimeout: Long = 30000L
)
