package com.zara.assistant.services;

/**
 * Zara Voice Orchestration Service
 * Coordinates STT, TTS, AI processing, and system controls
 * Features:
 * - 30-second auto-listening timeout
 * - Hindi/English support with user selection
 * - Smart voice command processing
 * - Ultra-fast performance optimization
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u009c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0011\b\u0007\u0018\u0000 U2\u00020\u0001:\u0002UVB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010:\u001a\u00020;2\u0006\u0010<\u001a\u00020\u0019H\u0002J\b\u0010=\u001a\u00020>H\u0002J\u0012\u0010?\u001a\u00020@2\b\u0010A\u001a\u0004\u0018\u00010BH\u0016J\b\u0010C\u001a\u00020>H\u0016J\b\u0010D\u001a\u00020>H\u0016J\"\u0010E\u001a\u00020F2\b\u0010A\u001a\u0004\u0018\u00010B2\u0006\u0010G\u001a\u00020F2\u0006\u0010H\u001a\u00020FH\u0016J\u0016\u0010I\u001a\u00020>2\u0006\u0010J\u001a\u00020\u0005H\u0082@\u00a2\u0006\u0002\u0010KJ\u0010\u0010L\u001a\u00020>2\u0006\u0010M\u001a\u00020\u001bH\u0002J\u001c\u0010N\u001a\u00020>2\u0006\u0010O\u001a\u00020\u00192\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u0019H\u0002J\b\u0010Q\u001a\u00020>H\u0002J\u0010\u0010R\u001a\u00020>2\u0006\u0010P\u001a\u00020\u0019H\u0002J\b\u0010S\u001a\u00020>H\u0002J\b\u0010T\u001a\u00020>H\u0002R\u0016\u0010\u0003\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\n\u001a\u00020\u000b8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000fR\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0012\u001a\u00060\u0013R\u00020\u0000X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u001e\u0010 \u001a\u00020!8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010#\"\u0004\b$\u0010%R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010,\u001a\b\u0012\u0004\u0012\u00020\t0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u0017R\u001e\u0010.\u001a\u00020/8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u00101\"\u0004\b2\u00103R\u001e\u00104\u001a\u0002058\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u00107\"\u0004\b8\u00109\u00a8\u0006W"}, d2 = {"Lcom/zara/assistant/services/ZaraVoiceService;", "Landroid/app/Service;", "()V", "_currentCommand", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/domain/model/VoiceCommand;", "_lastResponse", "Lcom/zara/assistant/domain/model/AIResponse;", "_voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "aiOrchestrationService", "Lcom/zara/assistant/services/AIOrchestrationService;", "getAiOrchestrationService", "()Lcom/zara/assistant/services/AIOrchestrationService;", "setAiOrchestrationService", "(Lcom/zara/assistant/services/AIOrchestrationService;)V", "autoListenJob", "Lkotlinx/coroutines/Job;", "binder", "Lcom/zara/assistant/services/ZaraVoiceService$ZaraVoiceBinder;", "currentCommand", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentCommand", "()Lkotlinx/coroutines/flow/StateFlow;", "currentLanguage", "", "isAutoListenEnabled", "", "isListening", "isSpeaking", "lastResponse", "getLastResponse", "localCommandProcessor", "Lcom/zara/assistant/services/LocalCommandProcessor;", "getLocalCommandProcessor", "()Lcom/zara/assistant/services/LocalCommandProcessor;", "setLocalCommandProcessor", "(Lcom/zara/assistant/services/LocalCommandProcessor;)V", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "sttListener", "Lcom/zara/assistant/services/ZaraSTTService$STTListener;", "ttsListener", "Lcom/zara/assistant/services/ZaraTTSService$TTSListener;", "voiceState", "getVoiceState", "zaraSTTService", "Lcom/zara/assistant/services/ZaraSTTService;", "getZaraSTTService", "()Lcom/zara/assistant/services/ZaraSTTService;", "setZaraSTTService", "(Lcom/zara/assistant/services/ZaraSTTService;)V", "zaraTTSService", "Lcom/zara/assistant/services/ZaraTTSService;", "getZaraTTSService", "()Lcom/zara/assistant/services/ZaraTTSService;", "setZaraTTSService", "(Lcom/zara/assistant/services/ZaraTTSService;)V", "createNotification", "Landroid/app/Notification;", "status", "initializeServices", "", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "onStartCommand", "", "flags", "startId", "processVoiceCommand", "command", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAutoListenEnabled", "enabled", "speak", "text", "language", "startAutoListen", "startListening", "stopListening", "stopSpeaking", "Companion", "ZaraVoiceBinder", "app_release"})
public final class ZaraVoiceService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ZaraVoiceService";
    private static final int NOTIFICATION_ID = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_LISTENING = "START_LISTENING";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_LISTENING = "STOP_LISTENING";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SPEAK = "SPEAK";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_SPEAKING = "STOP_SPEAKING";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SET_AUTO_LISTEN = "SET_AUTO_LISTEN";
    @javax.inject.Inject()
    public com.zara.assistant.services.ZaraSTTService zaraSTTService;
    @javax.inject.Inject()
    public com.zara.assistant.services.ZaraTTSService zaraTTSService;
    @javax.inject.Inject()
    public com.zara.assistant.services.LocalCommandProcessor localCommandProcessor;
    @javax.inject.Inject()
    public com.zara.assistant.services.AIOrchestrationService aiOrchestrationService;
    private boolean isAutoListenEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job autoListenJob;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraVoiceService.ZaraVoiceBinder binder = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    private boolean isListening = false;
    private boolean isSpeaking = false;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentLanguage = "auto";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.VoiceState> _voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceState> voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.VoiceCommand> _currentCommand = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceCommand> currentCommand = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.AIResponse> _lastResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.AIResponse> lastResponse = null;
    
    /**
     * STT Listener implementation
     */
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraSTTService.STTListener sttListener = null;
    
    /**
     * TTS Listener implementation
     */
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.ZaraTTSService.TTSListener ttsListener = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.ZaraVoiceService.Companion Companion = null;
    
    public ZaraVoiceService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.ZaraSTTService getZaraSTTService() {
        return null;
    }
    
    public final void setZaraSTTService(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.ZaraSTTService p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.ZaraTTSService getZaraTTSService() {
        return null;
    }
    
    public final void setZaraTTSService(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.ZaraTTSService p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.LocalCommandProcessor getLocalCommandProcessor() {
        return null;
    }
    
    public final void setLocalCommandProcessor(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.LocalCommandProcessor p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.AIOrchestrationService getAiOrchestrationService() {
        return null;
    }
    
    public final void setAiOrchestrationService(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.AIOrchestrationService p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceState> getVoiceState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.VoiceCommand> getCurrentCommand() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.AIResponse> getLastResponse() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * Initialize all voice services
     */
    private final void initializeServices() {
    }
    
    /**
     * Start listening for voice input
     */
    private final void startListening(java.lang.String language) {
    }
    
    /**
     * Stop listening
     */
    private final void stopListening() {
    }
    
    /**
     * Speak text using TTS
     */
    private final void speak(java.lang.String text, java.lang.String language) {
    }
    
    /**
     * Stop speaking
     */
    private final void stopSpeaking() {
    }
    
    /**
     * Process voice command
     */
    private final java.lang.Object processVoiceCommand(com.zara.assistant.domain.model.VoiceCommand command, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Start auto-listen with 30-second timeout
     */
    private final void startAutoListen() {
    }
    
    /**
     * Set auto-listen enabled state
     */
    private final void setAutoListenEnabled(boolean enabled) {
    }
    
    /**
     * Create notification for foreground service
     */
    private final android.app.Notification createNotification(java.lang.String status) {
        return null;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\"\u0010\u0012\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u00042\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0004J\u0018\u0010\u0015\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0014\u001a\u00020\u0004J\u000e\u0010\u0016\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/zara/assistant/services/ZaraVoiceService$Companion;", "", "()V", "ACTION_SET_AUTO_LISTEN", "", "ACTION_SPEAK", "ACTION_START_LISTENING", "ACTION_STOP_LISTENING", "ACTION_STOP_SPEAKING", "NOTIFICATION_ID", "", "TAG", "setAutoListenEnabled", "", "context", "Landroid/content/Context;", "enabled", "", "speak", "text", "language", "startListening", "stopListening", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void startListening(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.lang.String language) {
        }
        
        public final void stopListening(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
        
        public final void speak(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.lang.String text, @org.jetbrains.annotations.Nullable()
        java.lang.String language) {
        }
        
        public final void setAutoListenEnabled(@org.jetbrains.annotations.NotNull()
        android.content.Context context, boolean enabled) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/ZaraVoiceService$ZaraVoiceBinder;", "Landroid/os/Binder;", "(Lcom/zara/assistant/services/ZaraVoiceService;)V", "getService", "Lcom/zara/assistant/services/ZaraVoiceService;", "app_release"})
    public final class ZaraVoiceBinder extends android.os.Binder {
        
        public ZaraVoiceBinder() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.zara.assistant.services.ZaraVoiceService getService() {
            return null;
        }
    }
}