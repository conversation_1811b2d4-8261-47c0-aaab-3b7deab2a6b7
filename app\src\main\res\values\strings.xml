<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- App Name -->
    <string name="app_name"><PERSON>ara</string>
    <string name="app_description">Your intelligent AI voice assistant</string>
    
    <!-- Main Interface -->
    <string name="welcome_message">Hi, I\'m <PERSON><PERSON>, your AI assistant. How can I help you today?</string>
    <string name="tap_to_speak">Tap to speak</string>
    <string name="listening">Listening...</string>
    <string name="processing">Processing...</string>
    <string name="speaking">Speaking...</string>
    <string name="wake_word_active">Wake word detection active</string>
    <string name="wake_word_inactive">Wake word detection inactive</string>
    
    <!-- Voice States -->
    <string name="voice_idle">Ready to listen</string>
    <string name="voice_listening_wake">Say \"Hey <PERSON><PERSON>\" to start</string>
    <string name="voice_listening_manual">Listening for your command</string>
    <string name="voice_processing_ai">Thinking about your request</string>
    <string name="voice_speaking_response"><PERSON><PERSON> is responding</string>
    <string name="voice_error">Sorry, I didn\'t catch that</string>
    
    <!-- Permissions -->
    <string name="permission_microphone_title">Microphone Access</string>
    <string name="permission_microphone_description">Zara needs microphone access to listen to your voice commands</string>
    <string name="permission_accessibility_title">Accessibility Service</string>
    <string name="permission_accessibility_description">Allow Zara to control your device and apps through voice commands</string>
    <string name="permission_notification_title">Notification Access</string>
    <string name="permission_notification_description">Let Zara read and respond to your notifications</string>
    <string name="permission_overlay_title">Display Over Apps</string>
    <string name="permission_overlay_description">Allow Zara to show voice interface over other apps</string>
    <string name="permission_phone_title">Phone Access</string>
    <string name="permission_phone_description">Enable Zara to make calls and send messages for you</string>
    <string name="permission_location_title">Location Access</string>
    <string name="permission_location_description">Help Zara provide location-based assistance</string>
    
    <!-- Settings -->
    <string name="settings">Settings</string>
    <string name="voice_settings">Voice Settings</string>
    <string name="wake_word_settings">Wake Word</string>
    <string name="ai_settings">AI Settings</string>
    <string name="privacy_settings">Privacy</string>
    <string name="accessibility_settings">Accessibility</string>
    <string name="about">About</string>

    <!-- Voice Settings -->
    <string name="wake_word_sensitivity">Wake Word Sensitivity</string>
    <string name="speech_rate">Speech Rate</string>
    <string name="speech_pitch">Speech Pitch</string>
    <string name="auto_listen_after_response">Auto Listen After Response</string>

    <!-- AI Settings -->
    <string name="ai_personality">AI Personality</string>
    <string name="ai_response_style">Response Style</string>

    <!-- Privacy Settings -->
    <string name="conversation_history">Conversation History</string>
    <string name="voice_data_storage">Voice Data Storage</string>
    <string name="analytics_enabled">Analytics</string>

    <!-- Data Management -->
    <string name="clear_data">Clear All Data</string>
    <string name="export_data">Export Data</string>
    
    <!-- Wake Word Settings -->
    <string name="wake_word_enabled">Wake word detection enabled</string>
    <string name="wake_word_disabled">Wake word detection disabled</string>
    <string name="wake_word_sensitivity_low">Low</string>
    <string name="wake_word_sensitivity_medium">Medium</string>
    <string name="wake_word_sensitivity_high">High</string>

    <!-- Additional Voice Settings -->
    <string name="voice_language">Voice Language</string>

    <!-- Additional AI Settings -->
    <string name="ai_response_brief">Brief</string>
    <string name="ai_response_detailed">Detailed</string>
    <string name="ai_response_conversational">Conversational</string>
    <string name="ai_personality_professional">Professional</string>
    <string name="ai_personality_friendly">Friendly</string>
    <string name="ai_personality_casual">Casual</string>
    
    <!-- Additional Privacy Settings -->
    <string name="data_collection">Data Collection</string>
    
    <!-- Accessibility Service -->
    <string name="accessibility_service_description">Zara Accessibility Service allows voice control of your device, including opening apps, adjusting settings, and interacting with UI elements.</string>
    <string name="accessibility_service_summary">Voice control for your Android device</string>
    
    <!-- Notifications -->
    <string name="wake_word_service_title">Zara is listening</string>
    <string name="wake_word_service_description">Say \"Hey Zara\" to start a conversation</string>
    <string name="voice_processing_notification">Processing your request...</string>
    
    <!-- Errors -->
    <string name="error_microphone_permission">Microphone permission is required for voice commands</string>
    <string name="error_network_connection">Please check your internet connection</string>
    <string name="error_ai_service_unavailable">AI service is temporarily unavailable</string>
    <string name="error_speech_recognition">Speech recognition failed</string>
    <string name="error_text_to_speech">Text-to-speech is not available</string>
    <string name="error_accessibility_service">Accessibility service is not enabled</string>
    <string name="error_notification_access">Notification access is not granted</string>
    
    <!-- Commands Help -->
    <string name="commands_help_title">Voice Commands</string>
    <string name="commands_device_control">Device Control</string>
    <string name="commands_apps">App Management</string>
    <string name="commands_communication">Communication</string>
    <string name="commands_information">Information</string>
    <string name="commands_entertainment">Entertainment</string>
    
    <!-- Device Control Commands -->
    <string name="cmd_turn_on_wifi">\"Turn on WiFi\"</string>
    <string name="cmd_turn_off_bluetooth">\"Turn off Bluetooth\"</string>
    <string name="cmd_increase_volume">\"Increase volume\"</string>
    <string name="cmd_set_brightness">\"Set brightness to 50%\"</string>
    <string name="cmd_enable_airplane_mode">\"Enable airplane mode\"</string>
    
    <!-- App Commands -->
    <string name="cmd_open_app">\"Open [app name]\"</string>
    <string name="cmd_close_app">\"Close [app name]\"</string>
    <string name="cmd_switch_app">\"Switch to [app name]\"</string>
    
    <!-- Communication Commands -->
    <string name="cmd_call_contact">\"Call [contact name]\"</string>
    <string name="cmd_send_message">\"Send message to [contact]\"</string>
    <string name="cmd_read_notifications">\"Read my notifications\"</string>
    
    <!-- Information Commands -->
    <string name="cmd_weather">\"What\'s the weather like?\"</string>
    <string name="cmd_time">\"What time is it?\"</string>
    <string name="cmd_news">\"Tell me the news\"</string>
    <string name="cmd_search">\"Search for [query]\"</string>
    
    <!-- Entertainment Commands -->
    <string name="cmd_play_music">\"Play music\"</string>
    <string name="cmd_next_song">\"Next song\"</string>
    <string name="cmd_pause_music">\"Pause music\"</string>
    
    <!-- Responses -->
    <string name="response_wifi_enabled">WiFi has been turned on</string>
    <string name="response_bluetooth_disabled">Bluetooth has been turned off</string>
    <string name="response_volume_changed">Volume adjusted</string>
    <string name="response_app_opened">Opening %1$s</string>
    <string name="response_calling_contact">Calling %1$s</string>
    <string name="response_no_notifications">You have no new notifications</string>
    
    <!-- About -->
    <string name="version">Version</string>
    <string name="build_date">Build Date</string>
    <string name="developer">Developed by</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="open_source_licenses">Open Source Licenses</string>
    
    <!-- Actions -->
    <string name="enable">Enable</string>
    <string name="disable">Disable</string>
    <string name="grant">Grant</string>
    <string name="deny">Deny</string>
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="save">Save</string>
    <string name="reset">Reset</string>
    <string name="delete">Delete</string>
    <string name="share">Share</string>
    <string name="copy">Copy</string>
    <string name="retry">Retry</string>
    <string name="close">Close</string>
    <string name="done">Done</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="continue_text">Continue</string>
    <string name="skip">Skip</string>
    <string name="stop">Stop</string>
</resources>
