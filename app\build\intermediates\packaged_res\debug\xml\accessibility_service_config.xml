<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFeedbackType="feedbackSpoken|feedbackHaptic"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds"
    android:canPerformGestures="true"
    android:canRetrieveWindowContent="true"
    android:description="@string/accessibility_service_description"
    android:notificationTimeout="100"
    android:packageNames="com.android.systemui,com.android.settings,com.google.android.apps.messaging,com.whatsapp,com.facebook.katana,com.instagram.android,com.spotify.music,com.netflix.mediaclient"
    android:settingsActivity="com.zara.assistant.ui.settings.AccessibilitySettingsActivity" />
