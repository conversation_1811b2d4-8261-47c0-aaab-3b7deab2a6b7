// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.CohereAIService;
import com.zara.assistant.utils.ApiKeyManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideCohereAIServiceFactory implements Factory<CohereAIService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public ServiceModule_ProvideCohereAIServiceFactory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public CohereAIService get() {
    return provideCohereAIService(contextProvider.get(), apiKeyManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static ServiceModule_ProvideCohereAIServiceFactory create(
      Provider<Context> contextProvider, Provider<ApiKeyManager> apiKeyManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new ServiceModule_ProvideCohereAIServiceFactory(contextProvider, apiKeyManagerProvider, performanceUtilsProvider);
  }

  public static CohereAIService provideCohereAIService(Context context, ApiKeyManager apiKeyManager,
      PerformanceUtils performanceUtils) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideCohereAIService(context, apiKeyManager, performanceUtils));
  }
}
