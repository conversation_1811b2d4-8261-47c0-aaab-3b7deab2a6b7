// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.ui;

import com.zara.assistant.utils.DirectPermissionManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<DirectPermissionManager> directPermissionManagerProvider;

  public MainActivity_MembersInjector(
      Provider<DirectPermissionManager> directPermissionManagerProvider) {
    this.directPermissionManagerProvider = directPermissionManagerProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<DirectPermissionManager> directPermissionManagerProvider) {
    return new MainActivity_MembersInjector(directPermissionManagerProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectDirectPermissionManager(instance, directPermissionManagerProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.ui.MainActivity.directPermissionManager")
  public static void injectDirectPermissionManager(MainActivity instance,
      DirectPermissionManager directPermissionManager) {
    instance.directPermissionManager = directPermissionManager;
  }
}
