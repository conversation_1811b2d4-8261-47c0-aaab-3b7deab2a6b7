package com.zara.assistant.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Performance Utilities - Ultra-fast Zara performance optimizations
 * Features:
 * - Real-time performance monitoring
 * - Memory usage tracking
 * - Device tier detection
 * - Performance recommendations
 * - Measurement utilities
 */
@Singleton
class PerformanceUtils @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        const val TAG = "PerformanceUtils"
    }

    private val performanceMetrics = ConcurrentHashMap<String, PerformanceMetric>()
    private val memoryUsageTracker = AtomicLong(0)
    private val activeMeasurements = ConcurrentHashMap<String, Long>()

    /**
     * Start performance measurement
     */
    fun startMeasurement(operationName: String): String {
        val measurementId = "${operationName}_${System.currentTimeMillis()}"
        activeMeasurements[measurementId] = System.nanoTime()
        return measurementId
    }

    /**
     * End performance measurement
     */
    fun endMeasurement(measurementId: String): Long {
        val startTime = activeMeasurements.remove(measurementId) ?: return 0L
        val endTime = System.nanoTime()
        val duration = (endTime - startTime) / 1_000_000 // Convert to milliseconds
        
        val operationName = measurementId.substringBeforeLast("_")
        updateAggregateMetrics(operationName, duration)
        Log.d(TAG, "⚡ $operationName: ${duration}ms")
        
        return duration
    }

    /**
     * Measure operation execution time
     */
    suspend fun <T> measureOperation(
        operationName: String,
        operation: suspend () -> T
    ): Pair<T, Long> {
        val startTime = System.nanoTime()
        val result = operation()
        val endTime = System.nanoTime()
        val duration = (endTime - startTime) / 1_000_000
        
        updateAggregateMetrics(operationName, duration)
        Log.d(TAG, "⚡ $operationName: ${duration}ms")
        
        return Pair(result, duration)
    }
    
    /**
     * Update aggregate metrics for operation
     */
    fun updateAggregateMetrics(operationName: String, duration: Long) {
        val metric = performanceMetrics.getOrPut(operationName) {
            PerformanceMetric(operationName)
        }
        
        metric.addMeasurement(duration)
    }

    /**
     * Get device performance tier
     */
    fun getDeviceTier(): DeviceTier {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            val totalMemoryGB = memoryInfo.totalMem / (1024 * 1024 * 1024)
            val cpuCores = Runtime.getRuntime().availableProcessors()
            
            when {
                totalMemoryGB >= 8 && cpuCores >= 8 -> DeviceTier.HIGH_END
                totalMemoryGB >= 4 && cpuCores >= 4 -> DeviceTier.MID_RANGE
                else -> DeviceTier.LOW_END
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error detecting device tier: ${e.message}", e)
            DeviceTier.MID_RANGE // Safe default
        }
    }

    /**
     * Get device optimization recommendations
     */
    suspend fun getDeviceOptimization(): DeviceOptimization = withContext(Dispatchers.IO) {
        val deviceTier = getDeviceTier()
        val memoryInfo = getMemoryInfo()
        
        DeviceOptimization(
            deviceTier = deviceTier,
            recommendedConcurrency = when (deviceTier) {
                DeviceTier.HIGH_END -> 4
                DeviceTier.MID_RANGE -> 2
                DeviceTier.LOW_END -> 1
            },
            enableAnimations = deviceTier != DeviceTier.LOW_END,
            enableAdvancedFeatures = deviceTier == DeviceTier.HIGH_END,
            memoryInfo = memoryInfo,
            recommendations = generatePerformanceRecommendations(deviceTier)
        )
    }

    /**
     * Get current memory information
     */
    fun getMemoryInfo(): MemoryInfo {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            val runtime = Runtime.getRuntime()
            
            MemoryInfo(
                totalMemory = memoryInfo.totalMem,
                availableMemory = memoryInfo.availMem,
                usedMemory = memoryInfo.totalMem - memoryInfo.availMem,
                appMaxMemory = runtime.maxMemory(),
                appUsedMemory = runtime.totalMemory() - runtime.freeMemory(),
                isLowMemory = memoryInfo.lowMemory
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting memory info: ${e.message}", e)
            MemoryInfo() // Return default values
        }
    }

    /**
     * Generate performance recommendations
     */
    fun generatePerformanceRecommendations(deviceTier: DeviceTier): List<String> {
        val recommendations = mutableListOf<String>()
        
        when (deviceTier) {
            DeviceTier.HIGH_END -> {
                recommendations.add("Enable all features")
                recommendations.add("Use maximum quality settings")
                recommendations.add("Enable advanced animations")
            }
            DeviceTier.MID_RANGE -> {
                recommendations.add("Use balanced settings")
                recommendations.add("Enable moderate animations")
                recommendations.add("Limit concurrent operations")
            }
            DeviceTier.LOW_END -> {
                recommendations.add("Use performance mode")
                recommendations.add("Disable animations")
                recommendations.add("Limit background processing")
                recommendations.add("Use simplified UI")
            }
        }
        
        return recommendations
    }

    /**
     * Get performance metrics summary
     */
    fun getPerformanceMetrics(): Map<String, PerformanceMetric> {
        return performanceMetrics.toMap()
    }

    /**
     * Clear performance metrics
     */
    fun clearMetrics() {
        performanceMetrics.clear()
        Log.d(TAG, "🧹 Performance metrics cleared")
    }

    /**
     * Log performance summary
     */
    fun logPerformanceSummary() {
        Log.d(TAG, "📊 Performance Summary:")
        performanceMetrics.forEach { (operation, metric) ->
            Log.d(TAG, "  $operation: avg=${metric.averageDuration}ms, count=${metric.measurementCount}")
        }
    }
}

/**
 * Performance Metric data class
 */
data class PerformanceMetric(
    val operationName: String,
    private var totalDuration: Long = 0L,
    private var count: Long = 0L,
    private var minDuration: Long = Long.MAX_VALUE,
    private var maxDuration: Long = 0L
) {
    val averageDuration: Long
        get() = if (count > 0) totalDuration / count else 0L

    val measurementCount: Long
        get() = count

    fun addMeasurement(duration: Long) {
        totalDuration += duration
        count++
        minDuration = minOf(minDuration, duration)
        maxDuration = maxOf(maxDuration, duration)
    }
}

/**
 * Memory Information data class
 */
data class MemoryInfo(
    val totalMemory: Long = 0L,
    val availableMemory: Long = 0L,
    val usedMemory: Long = 0L,
    val appMaxMemory: Long = 0L,
    val appUsedMemory: Long = 0L,
    val isLowMemory: Boolean = false
)

/**
 * Device Optimization data class
 */
data class DeviceOptimization(
    val deviceTier: DeviceTier,
    val recommendedConcurrency: Int,
    val enableAnimations: Boolean,
    val enableAdvancedFeatures: Boolean,
    val memoryInfo: MemoryInfo,
    val recommendations: List<String>
)

/**
 * Device Performance Tier enum
 */
enum class DeviceTier {
    HIGH_END,    // Latest flagship devices
    MID_RANGE,   // Modern mid-range devices
    LOW_END      // Older or budget devices
}
