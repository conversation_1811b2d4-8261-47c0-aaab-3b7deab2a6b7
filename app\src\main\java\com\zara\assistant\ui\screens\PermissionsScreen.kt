package com.zara.assistant.ui.screens

import android.Manifest
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.utils.DirectPermissionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.delay
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch

/**
 * Perfect Permissions Screen - Beautiful permission management
 * Features:
 * - Real-time permission status
 * - Direct permission granting
 * - Beautiful animations
 * - Intelligent permission flow
 * - Live status updates
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionsScreen(
    onPermissionsGranted: () -> Unit,
    onOpenSettings: () -> Unit,
    directPermissionManager: DirectPermissionManager
) {
    // Permission states
    val permissionStates by directPermissionManager.permissionStates.collectAsState()
    val allPermissionsGranted by directPermissionManager.allPermissionsGranted.collectAsState()

    // UI states
    var isGrantingPermissions by remember { mutableStateOf(false) }
    var grantingProgress by remember { mutableStateOf(0f) }
    
    // Lifecycle scope for coroutines
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "permissions_animation")
    
    // Background gradient animation
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "gradient_animation"
    )

    // Permission data
    val permissionItems = remember {
        listOf(
            PermissionItem(
                permission = Manifest.permission.RECORD_AUDIO,
                title = "Microphone Access",
                description = "Required for voice commands and speech recognition",
                icon = Icons.Default.Mic,
                isRequired = true
            ),
            PermissionItem(
                permission = Manifest.permission.MODIFY_AUDIO_SETTINGS,
                title = "Audio Settings",
                description = "Needed to control volume and audio routing",
                icon = Icons.Default.VolumeUp,
                isRequired = true
            ),
            PermissionItem(
                permission = "SYSTEM_ALERT_WINDOW",
                title = "Display Over Apps",
                description = "Allows Zara to show floating controls",
                icon = Icons.Default.OpenInNew,
                isRequired = true
            ),
            PermissionItem(
                permission = "NOTIFICATION_LISTENER",
                title = "Notification Access",
                description = "Read and manage notifications with voice",
                icon = Icons.Default.Notifications,
                isRequired = true
            ),
            PermissionItem(
                permission = "ACCESSIBILITY_SERVICE",
                title = "Accessibility Service",
                description = "Control device functions with voice commands",
                icon = Icons.Default.Accessibility,
                isRequired = true
            )
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E).copy(alpha = 0.9f + gradientOffset * 0.1f),
                        Color(0xFF16213E).copy(alpha = 0.8f + gradientOffset * 0.2f),
                        Color(0xFF0F3460).copy(alpha = 0.7f + gradientOffset * 0.3f)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp))
            
            // Header
            Text(
                text = "🔐 Permissions Required",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Text(
                text = "Grant these permissions for the best Zara experience",
                fontSize = 16.sp,
                color = Color.White.copy(alpha = 0.8f),
                modifier = Modifier.padding(top = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Permission List
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(permissionItems) { item ->
                    PermissionCard(
                        item = item,
                        isGranted = permissionStates[item.permission] == true,
                        isGrantingPermissions = isGrantingPermissions
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Action Buttons
            PermissionActions(
                allPermissionsGranted = allPermissionsGranted,
                isGrantingPermissions = isGrantingPermissions,
                grantingProgress = grantingProgress,
                onGrantPermissions = {
                    if (!isGrantingPermissions) {
                        isGrantingPermissions = true
                        lifecycleOwner.lifecycleScope.launch {
                            try {
                                // Animate progress
                                for (i in 1..100) {
                                    grantingProgress = i / 100f
                                    delay(30)
                                }

                                // Grant permissions directly
                                val success = directPermissionManager.grantAllPermissionsDirectly()

                                if (success) {
                                    onPermissionsGranted()
                                }
                            } catch (e: Exception) {
                                // Handle error
                            } finally {
                                isGrantingPermissions = false
                                grantingProgress = 0f
                            }
                        }
                    }
                },
                onOpenSettings = onOpenSettings,
                onContinue = onPermissionsGranted
            )
        }
    }
}

@Composable
private fun PermissionCard(
    item: PermissionItem,
    isGranted: Boolean,
    isGrantingPermissions: Boolean
) {
    val scale by animateFloatAsState(
        targetValue = if (isGrantingPermissions) 0.95f else 1f,
        animationSpec = tween(300),
        label = "card_scale"
    )
    
    NeumorphismCard(
        modifier = Modifier
            .fillMaxWidth()
            .scale(scale)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon
            Icon(
                imageVector = item.icon,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = if (isGranted) Color(0xFF4CAF50) else Color(0xFFFF9800)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Content
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = item.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )
                
                Text(
                    text = item.description,
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.7f),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // Status
            Icon(
                imageVector = if (isGranted) Icons.Default.CheckCircle else Icons.Default.RadioButtonUnchecked,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = if (isGranted) Color(0xFF4CAF50) else Color.White.copy(alpha = 0.5f)
            )
        }
    }
}

@Composable
private fun PermissionActions(
    allPermissionsGranted: Boolean,
    isGrantingPermissions: Boolean,
    grantingProgress: Float,
    onGrantPermissions: () -> Unit,
    onOpenSettings: () -> Unit,
    onContinue: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Grant permissions button with direct granting
        if (!allPermissionsGranted) {
            NeumorphismButton(
                onClick = onGrantPermissions,
                enabled = !isGrantingPermissions,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (isGrantingPermissions) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator(
                            progress = grantingProgress,
                            modifier = Modifier.size(20.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Granting Permissions...",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                } else {
                    Text(
                        text = "🚀 Grant All Permissions",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }
            
            // Manual settings button
            NeumorphismButton(
                onClick = onOpenSettings,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "⚙️ Open Settings Manually",
                    color = Color.White,
                    fontSize = 16.sp
                )
            }
        } else {
            // Continue button when all permissions granted
            NeumorphismButton(
                onClick = onContinue,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "✅ Continue to Zara",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

/**
 * Permission Item data class
 */
data class PermissionItem(
    val permission: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val isRequired: Boolean = true
)
