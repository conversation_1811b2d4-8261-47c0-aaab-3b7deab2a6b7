package com.zara.assistant.domain.model;

/**
 * Command Categories for intelligent routing
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/domain/model/CommandCategory;", "", "(Ljava/lang/String;I)V", "DEVICE_CONTROL", "APP_MANAGEMENT", "COMMUNICATION", "MEDIA_CONTROL", "INFORMATION", "SYSTEM_ACTION", "AI_CONVERSATION", "UNKNOWN", "app_release"})
public enum CommandCategory {
    /*public static final*/ DEVICE_CONTROL /* = new DEVICE_CONTROL() */,
    /*public static final*/ APP_MANAGEMENT /* = new APP_MANAGEMENT() */,
    /*public static final*/ COMMUNICATION /* = new COMMUNICATION() */,
    /*public static final*/ MEDIA_CONTROL /* = new MEDIA_CONTROL() */,
    /*public static final*/ INFORMATION /* = new INFORMATION() */,
    /*public static final*/ SYSTEM_ACTION /* = new SYSTEM_ACTION() */,
    /*public static final*/ AI_CONVERSATION /* = new AI_CONVERSATION() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    CommandCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.CommandCategory> getEntries() {
        return null;
    }
}