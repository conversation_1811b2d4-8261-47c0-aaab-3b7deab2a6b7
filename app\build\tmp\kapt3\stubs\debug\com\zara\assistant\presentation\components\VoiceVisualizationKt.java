package com.zara.assistant.presentation.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u0007\n\u0002\b\u0007\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\f\u0010\r\u001a.\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0010\u0010\u0011\u001a`\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0013\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\u00072\b\b\u0002\u0010\u0015\u001a\u00020\u00072\b\b\u0002\u0010\u0016\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u0017\u001a\u00020\tH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0018\u0010\u0019\u001a4\u0010\u001a\u001a\u00020\u0001*\u00020\u001b2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u0006\u001a\u00020\u001e2\u0006\u0010\n\u001a\u00020\u000bH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001f\u0010 \u001aD\u0010!\u001a\u00020\u0001*\u00020\u001b2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u0014\u001a\u00020\u001e2\u0006\u0010\u0015\u001a\u00020\u001e2\u0006\u0010\u0016\u001a\u00020\u001e2\u0006\u0010\n\u001a\u00020\u000bH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b#\u0010$\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006%"}, d2 = {"CircularVoiceVisualization", "", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "modifier", "Landroidx/compose/ui/Modifier;", "radius", "Landroidx/compose/ui/unit/Dp;", "ringCount", "", "color", "Landroidx/compose/ui/graphics/Color;", "CircularVoiceVisualization-ZarA7XQ", "(Lcom/zara/assistant/domain/model/VoiceState;Landroidx/compose/ui/Modifier;FIJ)V", "VoiceStateIndicator", "size", "VoiceStateIndicator-wH6b6FI", "(Lcom/zara/assistant/domain/model/VoiceState;Landroidx/compose/ui/Modifier;F)V", "VoiceVisualization", "barCount", "barWidth", "barSpacing", "maxHeight", "animationDuration", "VoiceVisualization-V7nrBmk", "(Lcom/zara/assistant/domain/model/VoiceState;Landroidx/compose/ui/Modifier;IFFFJI)V", "drawCircularVoiceRings", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "animatedValues", "", "", "drawCircularVoiceRings-g2O1Hgs", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Ljava/util/List;FJ)V", "drawVoiceBars", "barHeights", "drawVoiceBars-kKL39v8", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Ljava/util/List;FFFJ)V", "app_debug"})
public final class VoiceVisualizationKt {
}