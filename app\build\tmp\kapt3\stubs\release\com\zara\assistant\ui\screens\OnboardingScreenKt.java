package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\u001a>\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a\u0012\u0010\r\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a\u001a\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u000f\u001a\u00020\u00022\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a\u0016\u0010\u0010\u001a\u00020\u00042\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\tH\u0007\u001a\"\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\"\u0014\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"onboardingPages", "", "Lcom/zara/assistant/ui/screens/OnboardingPageData;", "OnboardingActions", "", "currentPage", "", "totalPages", "onNext", "Lkotlin/Function0;", "onSkip", "modifier", "Landroidx/compose/ui/Modifier;", "OnboardingHeader", "OnboardingPage", "page", "OnboardingScreen", "onComplete", "PageIndicators", "pageCount", "app_release"})
public final class OnboardingScreenKt {
    
    /**
     * Onboarding pages content
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.zara.assistant.ui.screens.OnboardingPageData> onboardingPages = null;
    
    /**
     * Mind-Blowing Onboarding Screen - Welcome to the future of voice assistants
     * Features:
     * - Stunning animations
     * - Interactive tutorials
     * - Beautiful gradients
     * - Smooth transitions
     * - Engaging content
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void OnboardingScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
    }
    
    /**
     * Onboarding Header with logo and title
     */
    @androidx.compose.runtime.Composable()
    private static final void OnboardingHeader(androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Individual onboarding page
     */
    @androidx.compose.runtime.Composable()
    private static final void OnboardingPage(com.zara.assistant.ui.screens.OnboardingPageData page, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Page indicators
     */
    @androidx.compose.runtime.Composable()
    private static final void PageIndicators(int pageCount, int currentPage, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Action buttons
     */
    @androidx.compose.runtime.Composable()
    private static final void OnboardingActions(int currentPage, int totalPages, kotlin.jvm.functions.Function0<kotlin.Unit> onNext, kotlin.jvm.functions.Function0<kotlin.Unit> onSkip, androidx.compose.ui.Modifier modifier) {
    }
}