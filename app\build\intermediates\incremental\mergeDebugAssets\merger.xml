<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="androidx.benchmark:benchmark-macro:1.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\assets"><file name="trace_processor_shell_aarch64" path="C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\assets\trace_processor_shell_aarch64"/><file name="trace_processor_shell_arm" path="C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\assets\trace_processor_shell_arm"/><file name="trace_processor_shell_x86" path="C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\assets\trace_processor_shell_x86"/><file name="trace_processor_shell_x86_64" path="C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\assets\trace_processor_shell_x86_64"/></source></dataSet><dataSet config="androidx.benchmark:benchmark-common:1.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\assets"><file name="tracebox_aarch64" path="C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\assets\tracebox_aarch64"/><file name="tracebox_arm" path="C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\assets\tracebox_arm"/><file name="tracebox_x86" path="C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\assets\tracebox_x86"/><file name="tracebox_x86_64" path="C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\assets\tracebox_x86_64"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\assets"><file name="api_keys.key" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\assets\api_keys.key"/><file name="hey_zara_android.ppn" path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\assets\hey_zara_android.ppn"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Zara\Zara\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>