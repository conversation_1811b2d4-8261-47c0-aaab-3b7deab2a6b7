package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommandsHelpScreen(
    onNavigateBack: () -> Unit
) {
    val infiniteTransition = rememberInfiniteTransition(label = "commands_animation")
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f, targetValue = 1f,
        animationSpec = infiniteRepeatable(animation = tween(8000, easing = LinearEasing), repeatMode = RepeatMode.Reverse),
        label = "gradient_offset"
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6B73FF)),
                    start = androidx.compose.ui.geometry.Offset(gradientOffset * 1000f, gradientOffset * 1000f),
                    end = androidx.compose.ui.geometry.Offset((1f - gradientOffset) * 1000f, (1f - gradientOffset) * 1000f)
                )
            )
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            // Top bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .statusBarsPadding(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                NeumorphismButton(
                    onClick = onNavigateBack,
                    modifier = Modifier.size(48.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(imageVector = Icons.Default.ArrowBack, contentDescription = "Back", tint = MaterialTheme.colorScheme.primary)
                }
                Spacer(modifier = Modifier.width(16.dp))
                Text(
                    text = "Voice Commands",
                    style = MaterialTheme.typography.headlineMedium.copy(fontWeight = FontWeight.Bold, color = Color.White)
                )
            }
            
            LazyColumn(
                modifier = Modifier.fillMaxSize().padding(horizontal = 24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(commandCategories) { category ->
                    CommandCategoryCard(category = category)
                }
                item { Spacer(modifier = Modifier.height(32.dp)) }
            }
        }
    }
}

@Composable
private fun CommandCategoryCard(category: CommandCategory) {
    var isVisible by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy, stiffness = Spring.StiffnessLow),
        label = "category_scale"
    )
    
    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(category.animationDelay)
        isVisible = true
    }
    
    NeumorphismCard(
        modifier = Modifier.fillMaxWidth().scale(scale),
        elevation = 12.dp
    ) {
        Column(modifier = Modifier.padding(20.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = category.icon,
                    contentDescription = category.title,
                    modifier = Modifier.size(24.dp),
                    tint = category.color
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = category.title,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = category.color
                    )
                )
            }
            
            if (category.description.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = category.description,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            category.commands.forEach { command ->
                CommandItem(command = command)
                if (command != category.commands.last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
private fun CommandItem(command: VoiceCommand) {
    Row(
        modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "\"${command.phrase}\"",
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            ),
            modifier = Modifier.weight(1f)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = command.description,
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        )
    }
}

data class CommandCategory(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val color: Color,
    val commands: List<VoiceCommand>,
    val animationDelay: Long
)

data class VoiceCommand(
    val phrase: String,
    val description: String
)

private val commandCategories = listOf(
    CommandCategory(
        title = "Device Control",
        description = "Control your device settings and connectivity",
        icon = Icons.Default.Settings,
        color = Color(0xFF4F46E5),
        animationDelay = 100L,
        commands = listOf(
            VoiceCommand("Turn on WiFi", "Enable WiFi connection"),
            VoiceCommand("Turn off WiFi", "Disable WiFi connection"),
            VoiceCommand("Turn on Bluetooth", "Enable Bluetooth"),
            VoiceCommand("Turn off Bluetooth", "Disable Bluetooth"),
            VoiceCommand("Turn on flashlight", "Enable device flashlight"),
            VoiceCommand("Turn off flashlight", "Disable device flashlight"),
            VoiceCommand("Set volume to 50", "Set device volume level"),
            VoiceCommand("Take screenshot", "Capture screen")
        )
    ),
    CommandCategory(
        title = "App Management",
        description = "Open, close, and manage applications",
        icon = Icons.Default.Apps,
        color = Color(0xFF059669),
        animationDelay = 200L,
        commands = listOf(
            VoiceCommand("Open Settings", "Launch Settings app"),
            VoiceCommand("Open Camera", "Launch Camera app"),
            VoiceCommand("Open Calculator", "Launch Calculator app"),
            VoiceCommand("Open Phone", "Launch Phone app"),
            VoiceCommand("Open Messages", "Launch Messages app"),
            VoiceCommand("Open [app name]", "Open any installed app"),
            VoiceCommand("Close [app name]", "Close running app")
        )
    ),
    CommandCategory(
        title = "Navigation",
        description = "Navigate through your device interface",
        icon = Icons.Default.Navigation,
        color = Color(0xFF7C3AED),
        animationDelay = 300L,
        commands = listOf(
            VoiceCommand("Go back", "Navigate back"),
            VoiceCommand("Go home", "Go to home screen"),
            VoiceCommand("Show recent apps", "Open recent apps"),
            VoiceCommand("Show notifications", "Open notification panel"),
            VoiceCommand("Lock screen", "Lock the device")
        )
    ),
    CommandCategory(
        title = "Information & AI",
        description = "Get information and have conversations",
        icon = Icons.Default.Psychology,
        color = Color(0xFFDC2626),
        animationDelay = 400L,
        commands = listOf(
            VoiceCommand("What's the weather?", "Get weather information"),
            VoiceCommand("What's the latest news?", "Get current news"),
            VoiceCommand("Tell me about [topic]", "Get information about any topic"),
            VoiceCommand("How are you?", "Start a conversation"),
            VoiceCommand("What can you do?", "Learn about Zara's capabilities"),
            VoiceCommand("Help me with [task]", "Get assistance with tasks")
        )
    ),
    CommandCategory(
        title = "Communication",
        description = "Make calls and send messages",
        icon = Icons.Default.Phone,
        color = Color(0xFFEA580C),
        animationDelay = 500L,
        commands = listOf(
            VoiceCommand("Call [contact name]", "Make a phone call"),
            VoiceCommand("Send message to [contact]", "Send SMS message"),
            VoiceCommand("Read my notifications", "Read recent notifications"),
            VoiceCommand("Dismiss all notifications", "Clear all notifications")
        )
    ),
    CommandCategory(
        title = "Wake Word",
        description = "Activate Zara hands-free",
        icon = Icons.Default.Mic,
        color = Color(0xFF0891B2),
        animationDelay = 600L,
        commands = listOf(
            VoiceCommand("Hey Zara", "Activate voice assistant"),
            VoiceCommand("Stop listening", "Stop voice recognition"),
            VoiceCommand("Stop speaking", "Interrupt TTS playback")
        )
    )
)
