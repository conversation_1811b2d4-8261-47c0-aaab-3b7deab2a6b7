/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.zara.assistant;

public final class BuildConfig {
  public static final boolean DEBUG = false;
  public static final String APPLICATION_ID = "com.zara.assistant";
  public static final String BUILD_TYPE = "release";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0.0";
  // Field from default config.
  public static final String AZURE_SPEECH_KEY = "placeholder";
  // Field from default config.
  public static final String AZURE_SPEECH_REGION = "placeholder";
  // Field from default config.
  public static final String COHERE_API_KEY = "placeholder";
  // Field from build type: release
  public static final boolean DEBUG_MODE = false;
  // Field from default config.
  public static final String PERPLEXITY_API_KEY = "placeholder";
  // Field from default config.
  public static final String PORCUPINE_ACCESS_KEY = "placeholder";
}
