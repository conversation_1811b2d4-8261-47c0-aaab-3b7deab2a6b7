package com.zara.assistant.domain.repository;

/**
 * AI Repository Interface - Handles AI processing and responses
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\b\u001a\u00020\tH\u00a6@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000b\u001a\u00020\fH\u00a6@\u00a2\u0006\u0002\u0010\nJ>\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0010\u001a\u00020\u00052\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u0005H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u0016J\u001e\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u001aH&J0\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u001c\u001a\u00020\u00052\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u0005H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001e\u0010\u0007J$\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010 \u001a\u00020!H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J\u0016\u0010$\u001a\u00020\t2\u0006\u0010%\u001a\u00020\u0013H\u00a6@\u00a2\u0006\u0002\u0010&\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\'"}, d2 = {"Lcom/zara/assistant/domain/repository/AIRepository;", "", "classifyCommand", "Lcom/zara/assistant/domain/repository/CommandClassification;", "text", "", "language", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearConversationHistory", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAIHealthStatus", "Lcom/zara/assistant/domain/repository/AIHealthStatus;", "getCohereResponse", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/AIResponse;", "message", "conversationHistory", "", "Lcom/zara/assistant/domain/model/Conversation;", "personalityMode", "getCohereResponse-BWLJW6A", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getConversationHistory", "Lkotlinx/coroutines/flow/Flow;", "limit", "", "getPerplexityResponse", "query", "context", "getPerplexityResponse-0E7RQCE", "processCommand", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "processCommand-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveConversation", "conversation", "(Lcom/zara/assistant/domain/model/Conversation;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface AIRepository {
    
    /**
     * Classify command category and intent
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object classifyCommand(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.CommandClassification> $completion);
    
    /**
     * Get conversation history
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.domain.model.Conversation>> getConversationHistory(int limit);
    
    /**
     * Save conversation
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveConversation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Conversation conversation, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear conversation history
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearConversationHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Get AI health status
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAIHealthStatus(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.AIHealthStatus> $completion);
    
    /**
     * AI Repository Interface - Handles AI processing and responses
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}