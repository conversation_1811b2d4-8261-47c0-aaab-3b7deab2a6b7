// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ApiKeyManager_Factory implements Factory<ApiKeyManager> {
  private final Provider<Context> contextProvider;

  public ApiKeyManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ApiKeyManager get() {
    return newInstance(contextProvider.get());
  }

  public static ApiKeyManager_Factory create(Provider<Context> contextProvider) {
    return new ApiKeyManager_Factory(contextProvider);
  }

  public static ApiKeyManager newInstance(Context context) {
    return new ApiKeyManager(context);
  }
}
