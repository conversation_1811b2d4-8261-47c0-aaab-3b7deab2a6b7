// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.ui.viewmodel;

import com.zara.assistant.services.AIOrchestrationService;
import com.zara.assistant.services.LocalCommandProcessor;
import com.zara.assistant.services.SystemControlManager;
import com.zara.assistant.services.ZaraSTTService;
import com.zara.assistant.services.ZaraTTSService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<ZaraSTTService> zaraSTTServiceProvider;

  private final Provider<ZaraTTSService> zaraTTSServiceProvider;

  private final Provider<LocalCommandProcessor> localCommandProcessorProvider;

  private final Provider<AIOrchestrationService> aiOrchestrationServiceProvider;

  private final Provider<SystemControlManager> systemControlManagerProvider;

  public MainViewModel_Factory(Provider<ZaraSTTService> zaraSTTServiceProvider,
      Provider<ZaraTTSService> zaraTTSServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider,
      Provider<SystemControlManager> systemControlManagerProvider) {
    this.zaraSTTServiceProvider = zaraSTTServiceProvider;
    this.zaraTTSServiceProvider = zaraTTSServiceProvider;
    this.localCommandProcessorProvider = localCommandProcessorProvider;
    this.aiOrchestrationServiceProvider = aiOrchestrationServiceProvider;
    this.systemControlManagerProvider = systemControlManagerProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(zaraSTTServiceProvider.get(), zaraTTSServiceProvider.get(), localCommandProcessorProvider.get(), aiOrchestrationServiceProvider.get(), systemControlManagerProvider.get());
  }

  public static MainViewModel_Factory create(Provider<ZaraSTTService> zaraSTTServiceProvider,
      Provider<ZaraTTSService> zaraTTSServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider,
      Provider<SystemControlManager> systemControlManagerProvider) {
    return new MainViewModel_Factory(zaraSTTServiceProvider, zaraTTSServiceProvider, localCommandProcessorProvider, aiOrchestrationServiceProvider, systemControlManagerProvider);
  }

  public static MainViewModel newInstance(ZaraSTTService zaraSTTService,
      ZaraTTSService zaraTTSService, LocalCommandProcessor localCommandProcessor,
      AIOrchestrationService aiOrchestrationService, SystemControlManager systemControlManager) {
    return new MainViewModel(zaraSTTService, zaraTTSService, localCommandProcessor, aiOrchestrationService, systemControlManager);
  }
}
