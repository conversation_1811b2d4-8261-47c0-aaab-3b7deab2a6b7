// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.SystemControlManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideSystemControlManagerFactory implements Factory<SystemControlManager> {
  private final Provider<Context> contextProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public ServiceModule_ProvideSystemControlManagerFactory(Provider<Context> contextProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public SystemControlManager get() {
    return provideSystemControlManager(contextProvider.get(), performanceUtilsProvider.get());
  }

  public static ServiceModule_ProvideSystemControlManagerFactory create(
      Provider<Context> contextProvider, Provider<PerformanceUtils> performanceUtilsProvider) {
    return new ServiceModule_ProvideSystemControlManagerFactory(contextProvider, performanceUtilsProvider);
  }

  public static SystemControlManager provideSystemControlManager(Context context,
      PerformanceUtils performanceUtils) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideSystemControlManager(context, performanceUtils));
  }
}
