{"logs": [{"outputFile": "com.zara.assistant.app-mergeReleaseResources-83:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76f8ef271629eccbc242979ade06d14f\\transformed\\core-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2777,2875,2977,3075,3173,3280,3389,12334", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "2870,2972,3070,3168,3275,3384,3504,12430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a81feca8f62c4cb5733ffcc676aee47\\transformed\\jetified-material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4593,4681,4786,4867,4950,5049,5147,5242,5340,5428,5531,5631,5734,5850,5931,6031", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4588,4676,4781,4862,4945,5044,5142,5237,5335,5423,5526,5626,5729,5845,5926,6026,6122"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5646,5763,5881,5996,6112,6212,6316,6437,6578,6706,6848,6933,7032,7122,7218,7333,7454,7558,7686,7811,7943,8109,8234,8356,8479,8608,8699,8798,8914,9040,9140,9250,9353,9490,9630,9736,9834,9911,10005,10099,10184,10272,10377,10458,10541,10640,10738,10833,10931,11019,11122,11222,11325,11441,11522,11622", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "5758,5876,5991,6107,6207,6311,6432,6573,6701,6843,6928,7027,7117,7213,7328,7449,7553,7681,7806,7938,8104,8229,8351,8474,8603,8694,8793,8909,9035,9135,9245,9348,9485,9625,9731,9829,9906,10000,10094,10179,10267,10372,10453,10536,10635,10733,10828,10926,11014,11117,11217,11320,11436,11517,11617,11713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,12182", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,12258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c1dbd90dee8ec981efff90fba80648e\\transformed\\jetified-ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "36,37,39,41,42,53,54,111,112,113,114,115,116,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3509,3605,3802,3990,4091,5479,5555,11718,11808,11894,11958,12023,12101,12263,12435,12516,12586", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "3600,3687,3894,4086,4172,5550,5641,11803,11889,11953,12018,12096,12177,12329,12511,12581,12701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10f8b8bca7dd0586b8f58168a92d3396\\transformed\\biometric-1.1.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,256,374,512,655,776,909,1053,1153,1291,1436", "endColumns": "109,90,117,137,142,120,132,143,99,137,144,121", "endOffsets": "160,251,369,507,650,771,904,1048,1148,1286,1431,1553"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3692,3899,4177,4295,4433,4576,4697,4830,4974,5074,5212,5357", "endColumns": "109,90,117,137,142,120,132,143,99,137,144,121", "endOffsets": "3797,3985,4290,4428,4571,4692,4825,4969,5069,5207,5352,5474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3093826e12afe4e65cbc1e4cc9cbb375\\transformed\\jetified-foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12706,12793", "endColumns": "86,84", "endOffsets": "12788,12873"}}]}]}