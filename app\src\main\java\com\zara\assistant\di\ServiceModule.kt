package com.zara.assistant.di

import android.content.Context
import com.zara.assistant.services.*
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Service Module - Provides all Zara services for dependency injection
 */
@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {

    @Provides
    @Singleton
    fun provideApiKeyManager(
        @ApplicationContext context: Context
    ): ApiKeyManager {
        return ApiKeyManager(context)
    }

    @Provides
    @Singleton
    fun providePerformanceUtils(
        @ApplicationContext context: Context
    ): PerformanceUtils {
        return PerformanceUtils(context)
    }

    @Provides
    @Singleton
    fun provideZaraSTTService(
        @ApplicationContext context: Context,
        apiKeyManager: ApiKeyManager,
        performanceUtils: PerformanceUtils
    ): ZaraSTTService {
        return ZaraSTTService(context, apiKeyManager, performanceUtils)
    }

    @Provides
    @Singleton
    fun provideZaraTTSService(
        @ApplicationContext context: Context,
        apiKeyManager: ApiKeyManager,
        performanceUtils: PerformanceUtils
    ): ZaraTTSService {
        return ZaraTTSService(context, apiKeyManager, performanceUtils)
    }

    @Provides
    @Singleton
    fun provideSystemControlManager(
        @ApplicationContext context: Context,
        performanceUtils: PerformanceUtils
    ): SystemControlManager {
        return SystemControlManager(context, performanceUtils)
    }

    @Provides
    @Singleton
    fun provideCohereAIService(
        @ApplicationContext context: Context,
        apiKeyManager: ApiKeyManager,
        performanceUtils: PerformanceUtils
    ): CohereAIService {
        return CohereAIService(context, apiKeyManager, performanceUtils)
    }

    @Provides
    @Singleton
    fun providePerplexityAIService(
        @ApplicationContext context: Context,
        apiKeyManager: ApiKeyManager,
        performanceUtils: PerformanceUtils
    ): PerplexityAIService {
        return PerplexityAIService(context, apiKeyManager, performanceUtils)
    }

    @Provides
    @Singleton
    fun provideLocalCommandProcessor(
        @ApplicationContext context: Context,
        systemControlManager: SystemControlManager,
        performanceUtils: PerformanceUtils
    ): LocalCommandProcessor {
        return LocalCommandProcessor(context, systemControlManager, performanceUtils)
    }

    @Provides
    @Singleton
    fun provideAIOrchestrationService(
        @ApplicationContext context: Context,
        apiKeyManager: ApiKeyManager,
        performanceUtils: PerformanceUtils,
        cohereAIService: CohereAIService,
        perplexityAIService: PerplexityAIService
    ): AIOrchestrationService {
        return AIOrchestrationService(
            context, 
            apiKeyManager, 
            performanceUtils, 
            cohereAIService, 
            perplexityAIService
        )
    }
}
