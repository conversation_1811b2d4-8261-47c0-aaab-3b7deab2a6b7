-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:156:9-164:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:160:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:159:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:157:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-168:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-168:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-168:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-168:12
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e604187a9d7ae5379a6372cc94bef45e\transformed\jetified-camera-video-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a9845fa983e0b169a05de08150ca37c\transformed\jetified-camera-lifecycle-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bdc921d1f4d0055fa05a172e1713bcb\transformed\jetified-camera-view-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\21bb5e18c0eee6b9c7273b75e0822c2a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15f37acf3eebe57f0a69692a2c5e3982\transformed\jetified-lottie-compose-6.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c8c20c5f9f0ed8aec2c62427edbc4af\transformed\jetified-lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\73f2f29d630982718561535260364d81\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\929910efe9f1554efb0e36b356143651\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a304dea9782dc2e75928dd2cbccbb06a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f23599b573ef70c77582c129b534bd8f\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fc49c7b11c512a1d5735a80cedcd49e\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e100d358aff62c4f57607391f1e38263\transformed\navigation-common-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\1265bc1e4299577aaef89d15e8083b82\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b668840e5b0de47a5c031d1885bcbf3a\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b7abad29792ddee7b7e3ae04889f787f\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e89d9412b652ddb8bee8c212921bd38\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\c559136167001409daa6b0eb304b881b\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e65d15a690676c3bc283bbca11884a4\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\b355f29a1458f6c32ffc2af5d02340f3\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\256d85af91a7ef493b14963e8e05452c\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\79aaffa27de3df944af87f39d562ecd3\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a81feca8f62c4cb5733ffcc676aee47\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4a513bc4373c2c879db14c26be4da63\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd6774776abaed8002d3bb4c0f35652f\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\30da03594a25bd0875bf62da8e5b0cee\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\85fed85dffac2d18e86c740134e250cb\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ffc3c71bb8f34469256e24c4e3b93b8f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59052bd8069c5d317b58c27b46d75f80\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a6a21db684b54289a88d7a007e60aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2296c18e9f29de7ebfb0c75ea301cc57\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\12de75d634f60636f56bde6cb6401c98\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ceb108433718d033ea831ef547d97f25\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80c984df0c21dad72430473dd250ecf\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7d6f535687d3658d5bb7dfab0a28d6\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\349193617a718f5fb8c165bf0fd1cfdf\transformed\jetified-animation-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66e3df1cf1a1107b87371c9164200e58\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd1f99e4ce21923ccae8d046ccf95c62\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3093826e12afe4e65cbc1e4cc9cbb375\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5f8b8ab4943f2f9890fbce77bd1bb9\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ed8df5488b52c4c39582198fddc17d9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\723da7cb6d11414e24e7efb014dc730d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e36654a205a25f1bbce5289d81fc9b57\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3cfc3be0edb864b95a6a8fbfc9896f93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:17:1-39:12
MERGED from [androidx.benchmark:benchmark-macro-junit4:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62279fe3536f90579957455ef3edcbbf\transformed\jetified-benchmark-macro-junit4-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:17:1-53:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e583ec9877c0d3fb57a162030bbe24\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f371ec8eab1aaa9a14a473d4cb5a67c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\530429991ec28496eb49066e8c7e5384\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed15b946d5ed459c4dc3faf38ed1165f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b9afbc0e89456c0ec55572c84b6e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1ea15a6d1c31f45b2085fa5e508c15\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d1db041e9eb6e82b657a563f0ccac0d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65fc8c3cdc1863ea9598cdfcf69edfc2\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87b032a873d208425c2beac531fdbab4\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb9d6b9761275db19ae89364a148392\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c1dbd90dee8ec981efff90fba80648e\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a11ff6f3c6e6d98530a8949acc81ec33\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\cb63013de8f2edf0e296b4e6b08092b3\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c0126a6a18779241fd55cc58603e1228\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a95c9115a1b1c4a64511144c2956c7d0\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\00fe00817e70044afe3bb8c2afd24aa6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17f76cad914ea46e7f5d2ab21805a2f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb75474e75410beced6e3046fd33c13e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b07fe748c6984fef53f4d8f194a04f3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\941588eefb7afce6a92273f14e933d68\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b14ef6d64ed0497f4c285af691dcf21\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52be0562697f63c1f60f36d041cf7d07\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d62b3d76758d6d8116b8ce6df19db9c\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0050c829fbf762265b737dd8d1e1c9f7\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3466e6c242a3767caa59288b0189e761\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a259f4fef2f815ba319024046901d62\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb3dc04c0640376363fb2b790e15848\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbdc8267fd556be6dc2e8dfa9d26e41c\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80134d55b5873b3d6bb0d04c95e433a8\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a09f8dd2fb26dd825d9f7920a2382b4\transformed\jetified-napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\13daad6bb89ec7f6f5ec7221da89654d\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc0ccfa480eb1ca5288ede3499d229c\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.tracing:tracing-ktx:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4759c072a1c6b646557b84e2e84e1ad4\transformed\jetified-tracing-ktx-1.1.0-rc01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35830d9b0663696cc0a630215e126cac\transformed\jetified-tracing-perfetto-binary-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:17:1-58:12
MERGED from [androidx.test:rules:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\3393dc33b6f507cfa8b937d0fbe0f9d7\transformed\rules-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.test.services:storage:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\c90a39d910de46c6828e8d06992eb92e\transformed\jetified-storage-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:monitor:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bf37a1144bcfa0d88e13e5c5408b138\transformed\monitor-1.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:annotation:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4b090e38fec8b9bdd0fda2a303f13b0\transformed\jetified-annotation-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\679fdd4398b5d2e37c9d25114fe08064\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\50b2a20dae357b6f7320edea7bc14ff8\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e447c26dd5f255c20f2c0f36c391365\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c77233900f88e2c71939cff7906acfc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3293a1eba87e31dcd6d0ede7885c1f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ccec940a63867819580e5d0fe6a146e\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\116cf402b094172ceb51bede154d4bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\b30c0b1cae335d55f81d658e884eed6d\transformed\jetified-tracing-1.1.0-rc01\AndroidManifest.xml:17:1-24:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\174a26c03ccc0544d5bc3b6fbe757394\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\171522798132df46b0696a5b268cabc1\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7182d15ebb6333c7887fd406cd53c2f\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:2:1-9:12
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:2:1-21:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\d283a706fb607caccf33fef8a271b16b\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcfa5af20c6014540f0977c9f225d18f\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\d28786fcc2484d5a7597da43f8bf11be\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:2:1-6:12
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a44137f53da630bd47bf7a73d470d81\transformed\uiautomator-2.2.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:11:5-71
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-66
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-72
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:13:22-69
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-69
uses-permission#android.permission.SEND_SMS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-64
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:5-67
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:31:5-67
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:31:5-67
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:12:5-67
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:12:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:19:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:23:22-75
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-88
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_PHONE_CALL
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-88
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-85
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:31:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:32:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:36:22-75
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:37:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:40:22-62
uses-permission#android.permission.FLASHLIGHT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:41:22-66
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-78
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-47:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-90
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-74
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:5-50:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:50:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:49:22-82
uses-permission#android.permission.BIND_DEVICE_ADMIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-81
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:27:5-81
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-78
uses-feature#android.hardware.microphone
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:58:5-60:35
	android:required
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:60:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:9-51
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:61:5-63:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:62:9-47
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-66:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:66:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:65:9-53
uses-feature#android.hardware.bluetooth
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-69:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:9-50
uses-feature#android.hardware.wifi
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:70:5-72:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:72:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:71:9-45
application
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-166:19
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-166:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c8c20c5f9f0ed8aec2c62427edbc4af\transformed\jetified-lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c8c20c5f9f0ed8aec2c62427edbc4af\transformed\jetified-lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:29:5-37:19
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:29:5-37:19
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:42:5-81
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:42:5-81
MERGED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:26:5-51:19
MERGED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:26:5-51:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:23:5-56:19
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:23:5-56:19
MERGED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:30:5-20
MERGED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:30:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e447c26dd5f255c20f2c0f36c391365\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e447c26dd5f255c20f2c0f36c391365\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\171522798132df46b0696a5b268cabc1\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\171522798132df46b0696a5b268cabc1\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:14:5-19:19
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:14:5-19:19
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcfa5af20c6014540f0977c9f225d18f\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcfa5af20c6014540f0977c9f225d18f\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\d28786fcc2484d5a7597da43f8bf11be\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:4:4-5:18
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\d28786fcc2484d5a7597da43f8bf11be\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:4:4-5:18
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a44137f53da630bd47bf7a73d470d81\transformed\uiautomator-2.2.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a44137f53da630bd47bf7a73d470d81\transformed\uiautomator-2.2.0\AndroidManifest.xml:22:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:9-54
	android:largeHeap
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:9-33
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:9-43
	android:networkSecurityConfig
		ADDED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:42:18-78
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:82:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:80:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:86:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:76:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:83:9-42
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:77:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:75:9-40
activity#com.zara.assistant.ui.MainActivity
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:89:9-103:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:94:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:13-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:90:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:95:13-98:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:27-74
intent-filter#action:name:android.intent.action.VOICE_COMMAND+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:99:13-102:29
action#android.intent.action.VOICE_COMMAND
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:25-75
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:27-73
service#com.zara.assistant.services.ZaraVoiceService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:106:9-110:58
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:109:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:13-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:107:13-54
service#com.zara.assistant.services.WakeWordService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:113:9-117:58
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:115:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:116:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:117:13-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:13-53
service#com.zara.assistant.services.ZaraAccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:9-130:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:13-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:13-62
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:13-126:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:17-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:127:13-129:72
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:129:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:128:17-60
service#com.zara.assistant.services.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:133:9-140:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:13-65
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:13-139:29
action#android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:17-99
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:25-96
receiver#com.zara.assistant.services.ZaraDeviceAdminReceiver
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:143:9-153:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:13-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:144:13-61
meta-data#android.app.device_admin
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:147:13-149:56
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:149:17-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:148:17-56
intent-filter#action:name:android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:150:13-152:29
action#android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:25-79
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:13-163:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:163:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e604187a9d7ae5379a6372cc94bef45e\transformed\jetified-camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e604187a9d7ae5379a6372cc94bef45e\transformed\jetified-camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a9845fa983e0b169a05de08150ca37c\transformed\jetified-camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a9845fa983e0b169a05de08150ca37c\transformed\jetified-camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bdc921d1f4d0055fa05a172e1713bcb\transformed\jetified-camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bdc921d1f4d0055fa05a172e1713bcb\transformed\jetified-camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\21bb5e18c0eee6b9c7273b75e0822c2a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\21bb5e18c0eee6b9c7273b75e0822c2a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15f37acf3eebe57f0a69692a2c5e3982\transformed\jetified-lottie-compose-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15f37acf3eebe57f0a69692a2c5e3982\transformed\jetified-lottie-compose-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c8c20c5f9f0ed8aec2c62427edbc4af\transformed\jetified-lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c8c20c5f9f0ed8aec2c62427edbc4af\transformed\jetified-lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\73f2f29d630982718561535260364d81\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\73f2f29d630982718561535260364d81\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\929910efe9f1554efb0e36b356143651\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\929910efe9f1554efb0e36b356143651\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a304dea9782dc2e75928dd2cbccbb06a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a304dea9782dc2e75928dd2cbccbb06a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f23599b573ef70c77582c129b534bd8f\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f23599b573ef70c77582c129b534bd8f\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fc49c7b11c512a1d5735a80cedcd49e\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fc49c7b11c512a1d5735a80cedcd49e\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e100d358aff62c4f57607391f1e38263\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e100d358aff62c4f57607391f1e38263\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\1265bc1e4299577aaef89d15e8083b82\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\1265bc1e4299577aaef89d15e8083b82\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b668840e5b0de47a5c031d1885bcbf3a\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b668840e5b0de47a5c031d1885bcbf3a\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b7abad29792ddee7b7e3ae04889f787f\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\b7abad29792ddee7b7e3ae04889f787f\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e89d9412b652ddb8bee8c212921bd38\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e89d9412b652ddb8bee8c212921bd38\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\c559136167001409daa6b0eb304b881b\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\c559136167001409daa6b0eb304b881b\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e65d15a690676c3bc283bbca11884a4\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e65d15a690676c3bc283bbca11884a4\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\b355f29a1458f6c32ffc2af5d02340f3\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\b355f29a1458f6c32ffc2af5d02340f3\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\256d85af91a7ef493b14963e8e05452c\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\256d85af91a7ef493b14963e8e05452c\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\79aaffa27de3df944af87f39d562ecd3\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\79aaffa27de3df944af87f39d562ecd3\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a81feca8f62c4cb5733ffcc676aee47\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a81feca8f62c4cb5733ffcc676aee47\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4a513bc4373c2c879db14c26be4da63\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4a513bc4373c2c879db14c26be4da63\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd6774776abaed8002d3bb4c0f35652f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd6774776abaed8002d3bb4c0f35652f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\30da03594a25bd0875bf62da8e5b0cee\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\30da03594a25bd0875bf62da8e5b0cee\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\85fed85dffac2d18e86c740134e250cb\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\85fed85dffac2d18e86c740134e250cb\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ffc3c71bb8f34469256e24c4e3b93b8f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ffc3c71bb8f34469256e24c4e3b93b8f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59052bd8069c5d317b58c27b46d75f80\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59052bd8069c5d317b58c27b46d75f80\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a6a21db684b54289a88d7a007e60aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a6a21db684b54289a88d7a007e60aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2296c18e9f29de7ebfb0c75ea301cc57\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2296c18e9f29de7ebfb0c75ea301cc57\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\12de75d634f60636f56bde6cb6401c98\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\12de75d634f60636f56bde6cb6401c98\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ceb108433718d033ea831ef547d97f25\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ceb108433718d033ea831ef547d97f25\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80c984df0c21dad72430473dd250ecf\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80c984df0c21dad72430473dd250ecf\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7d6f535687d3658d5bb7dfab0a28d6\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7d6f535687d3658d5bb7dfab0a28d6\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\349193617a718f5fb8c165bf0fd1cfdf\transformed\jetified-animation-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\349193617a718f5fb8c165bf0fd1cfdf\transformed\jetified-animation-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66e3df1cf1a1107b87371c9164200e58\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66e3df1cf1a1107b87371c9164200e58\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd1f99e4ce21923ccae8d046ccf95c62\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd1f99e4ce21923ccae8d046ccf95c62\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3093826e12afe4e65cbc1e4cc9cbb375\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3093826e12afe4e65cbc1e4cc9cbb375\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5f8b8ab4943f2f9890fbce77bd1bb9\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae5f8b8ab4943f2f9890fbce77bd1bb9\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ed8df5488b52c4c39582198fddc17d9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ed8df5488b52c4c39582198fddc17d9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\723da7cb6d11414e24e7efb014dc730d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\723da7cb6d11414e24e7efb014dc730d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e36654a205a25f1bbce5289d81fc9b57\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e36654a205a25f1bbce5289d81fc9b57\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3cfc3be0edb864b95a6a8fbfc9896f93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3cfc3be0edb864b95a6a8fbfc9896f93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-macro-junit4:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62279fe3536f90579957455ef3edcbbf\transformed\jetified-benchmark-macro-junit4-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.benchmark:benchmark-macro-junit4:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62279fe3536f90579957455ef3edcbbf\transformed\jetified-benchmark-macro-junit4-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e583ec9877c0d3fb57a162030bbe24\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e583ec9877c0d3fb57a162030bbe24\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f371ec8eab1aaa9a14a473d4cb5a67c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f371ec8eab1aaa9a14a473d4cb5a67c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\530429991ec28496eb49066e8c7e5384\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\530429991ec28496eb49066e8c7e5384\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed15b946d5ed459c4dc3faf38ed1165f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed15b946d5ed459c4dc3faf38ed1165f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b9afbc0e89456c0ec55572c84b6e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b9afbc0e89456c0ec55572c84b6e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1ea15a6d1c31f45b2085fa5e508c15\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1ea15a6d1c31f45b2085fa5e508c15\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d1db041e9eb6e82b657a563f0ccac0d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d1db041e9eb6e82b657a563f0ccac0d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65fc8c3cdc1863ea9598cdfcf69edfc2\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65fc8c3cdc1863ea9598cdfcf69edfc2\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87b032a873d208425c2beac531fdbab4\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87b032a873d208425c2beac531fdbab4\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb9d6b9761275db19ae89364a148392\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb9d6b9761275db19ae89364a148392\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c1dbd90dee8ec981efff90fba80648e\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c1dbd90dee8ec981efff90fba80648e\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a11ff6f3c6e6d98530a8949acc81ec33\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a11ff6f3c6e6d98530a8949acc81ec33\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\cb63013de8f2edf0e296b4e6b08092b3\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\cb63013de8f2edf0e296b4e6b08092b3\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c0126a6a18779241fd55cc58603e1228\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c0126a6a18779241fd55cc58603e1228\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a95c9115a1b1c4a64511144c2956c7d0\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a95c9115a1b1c4a64511144c2956c7d0\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\00fe00817e70044afe3bb8c2afd24aa6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\00fe00817e70044afe3bb8c2afd24aa6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17f76cad914ea46e7f5d2ab21805a2f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17f76cad914ea46e7f5d2ab21805a2f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb75474e75410beced6e3046fd33c13e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb75474e75410beced6e3046fd33c13e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b07fe748c6984fef53f4d8f194a04f3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b07fe748c6984fef53f4d8f194a04f3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\941588eefb7afce6a92273f14e933d68\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\941588eefb7afce6a92273f14e933d68\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b14ef6d64ed0497f4c285af691dcf21\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b14ef6d64ed0497f4c285af691dcf21\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52be0562697f63c1f60f36d041cf7d07\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52be0562697f63c1f60f36d041cf7d07\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d62b3d76758d6d8116b8ce6df19db9c\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d62b3d76758d6d8116b8ce6df19db9c\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0050c829fbf762265b737dd8d1e1c9f7\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0050c829fbf762265b737dd8d1e1c9f7\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3466e6c242a3767caa59288b0189e761\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3466e6c242a3767caa59288b0189e761\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a259f4fef2f815ba319024046901d62\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a259f4fef2f815ba319024046901d62\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb3dc04c0640376363fb2b790e15848\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb3dc04c0640376363fb2b790e15848\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbdc8267fd556be6dc2e8dfa9d26e41c\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbdc8267fd556be6dc2e8dfa9d26e41c\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80134d55b5873b3d6bb0d04c95e433a8\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80134d55b5873b3d6bb0d04c95e433a8\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a09f8dd2fb26dd825d9f7920a2382b4\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a09f8dd2fb26dd825d9f7920a2382b4\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\13daad6bb89ec7f6f5ec7221da89654d\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\13daad6bb89ec7f6f5ec7221da89654d\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc0ccfa480eb1ca5288ede3499d229c\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc0ccfa480eb1ca5288ede3499d229c\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.tracing:tracing-ktx:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4759c072a1c6b646557b84e2e84e1ad4\transformed\jetified-tracing-ktx-1.1.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4759c072a1c6b646557b84e2e84e1ad4\transformed\jetified-tracing-ktx-1.1.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35830d9b0663696cc0a630215e126cac\transformed\jetified-tracing-perfetto-binary-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35830d9b0663696cc0a630215e126cac\transformed\jetified-tracing-perfetto-binary-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.test:rules:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\3393dc33b6f507cfa8b937d0fbe0f9d7\transformed\rules-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\3393dc33b6f507cfa8b937d0fbe0f9d7\transformed\rules-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.services:storage:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\c90a39d910de46c6828e8d06992eb92e\transformed\jetified-storage-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.services:storage:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\c90a39d910de46c6828e8d06992eb92e\transformed\jetified-storage-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bf37a1144bcfa0d88e13e5c5408b138\transformed\monitor-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bf37a1144bcfa0d88e13e5c5408b138\transformed\monitor-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:annotation:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4b090e38fec8b9bdd0fda2a303f13b0\transformed\jetified-annotation-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:annotation:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4b090e38fec8b9bdd0fda2a303f13b0\transformed\jetified-annotation-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\679fdd4398b5d2e37c9d25114fe08064\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\679fdd4398b5d2e37c9d25114fe08064\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\50b2a20dae357b6f7320edea7bc14ff8\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\50b2a20dae357b6f7320edea7bc14ff8\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e447c26dd5f255c20f2c0f36c391365\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e447c26dd5f255c20f2c0f36c391365\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c77233900f88e2c71939cff7906acfc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c77233900f88e2c71939cff7906acfc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3293a1eba87e31dcd6d0ede7885c1f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3293a1eba87e31dcd6d0ede7885c1f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ccec940a63867819580e5d0fe6a146e\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\1ccec940a63867819580e5d0fe6a146e\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\116cf402b094172ceb51bede154d4bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\116cf402b094172ceb51bede154d4bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\b30c0b1cae335d55f81d658e884eed6d\transformed\jetified-tracing-1.1.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\b30c0b1cae335d55f81d658e884eed6d\transformed\jetified-tracing-1.1.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\174a26c03ccc0544d5bc3b6fbe757394\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\174a26c03ccc0544d5bc3b6fbe757394\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\171522798132df46b0696a5b268cabc1\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\171522798132df46b0696a5b268cabc1\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7182d15ebb6333c7887fd406cd53c2f\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:5:5-7:41
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7182d15ebb6333c7887fd406cd53c2f\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\d283a706fb607caccf33fef8a271b16b\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\d283a706fb607caccf33fef8a271b16b\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcfa5af20c6014540f0977c9f225d18f\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcfa5af20c6014540f0977c9f225d18f\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a44137f53da630bd47bf7a73d470d81\transformed\uiautomator-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a44137f53da630bd47bf7a73d470d81\transformed\uiautomator-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2cb5e438d5aa3c041a3a2ac8678a04a\transformed\jetified-camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea01c6fde50e04487cfc69083a14ca0f\transformed\jetified-camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\10f8b8bca7dd0586b8f58168a92d3396\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:39:9-47:20
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:39:9-47:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d7bcac3a9f64fa9e2de7c8777a96db5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\998ccd9a361f25607ee9bdd8ab3141e5\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\40555b9710e309bebb459be0b629b17a\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6b56806747a2089575f49a4bcd47646\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\784541fa46d848ef406673a1158fc1e7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0dc3648b6fafbd094aa7101f31068b03\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
activity#androidx.benchmark.IsolationActivity
ADDED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:32:9-36:20
	android:exported
		ADDED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:34:13-36
	android:theme
		ADDED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:35:13-77
	android:name
		ADDED from [androidx.benchmark:benchmark-common:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2e4bdc359074b4d97b23dc13768226c\transformed\jetified-benchmark-common-1.2.0\AndroidManifest.xml:33:13-64
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:23:5-25:53
	tools:ignore
		ADDED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:25:9-50
	android:name
		ADDED from [androidx.benchmark:benchmark-macro:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\beffc584618e50b43572ae43053470c9\transformed\jetified-benchmark-macro-1.2.0\AndroidManifest.xml:24:9-61
uses-permission#android.permission.REORDER_TASKS
ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:22-69
activity#androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity
ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:27:9-34:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:30:13-56
	android:name
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:28:13-99
intent-filter#category:name:android.intent.category.LAUNCHER
ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
	android:priority
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity
ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:35:9-42:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:37:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:38:13-56
	android:name
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:36:13-95
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity
ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:43:9-50:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:45:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:46:13-62
	android:name
		ADDED from [androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\665c7e8b6f514c6f99b6e4f872771716\transformed\jetified-core-1.5.0\AndroidManifest.xml:44:13-103
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\76f8ef271629eccbc242979ade06d14f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cf16bdd9520d7e6bfe74c5e122c640b4\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
receiver#androidx.tracing.perfetto.TracingReceiver
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
	android:enabled
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
	android:exported
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
	android:directBootAware
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
intent-filter#action:name:androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START+action:name:androidx.tracing.perfetto.action.ENABLE_TRACING+action:name:androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
action#androidx.tracing.perfetto.action.ENABLE_TRACING
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
action#androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
action#androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
meta-data#androidx.tracing.perfetto.StartupTracingInitializer
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
	android:value
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
receiver#androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
	android:enabled
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
	android:exported
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
	tools:targetApi
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:54:13-32
	android:directBootAware
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdeb96aa377db6646cb18c763205ddd9\transformed\jetified-tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
queries
ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:24:5-28:15
package#androidx.test.orchestrator
ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:9-62
	android:name
		ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:25:18-59
package#androidx.test.services
ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:9-58
	android:name
		ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:26:18-55
package#com.google.android.apps.common.testing.services
ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:9-83
	android:name
		ADDED from [androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\13978adee963ce42c146e3c9b5d90df8\transformed\runner-1.5.0\AndroidManifest.xml:27:18-80
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c94d7631e3dcbc7b87c1529e4058da6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
provider#com.microsoft.cognitiveservices.speech.util.InternalContentProvider
ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
	android:authorities
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
	android:exported
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
	android:name
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29ceac0a28ed7bbe426936ce1ac791\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
