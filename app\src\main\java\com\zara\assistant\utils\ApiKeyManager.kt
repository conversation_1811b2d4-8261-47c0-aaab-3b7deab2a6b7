package com.zara.assistant.utils

import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API Key Manager - Secure API key management for Zara
 * Loads API keys from assets/api_keys.key file
 */
@Singleton
class ApiKeyManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "ApiKeyManager"
        private const val API_KEYS_FILE = "api_keys.key"
        
        // API Key identifiers
        private const val COHERE_KEY = "COHERE_API_KEY"
        private const val PERPLEXITY_KEY = "PERPLEXITY_API_KEY"
        private const val PORCUPINE_KEY = "PORCUPINE_ACCESS_KEY"
        private const val AZURE_SPEECH_KEY = "AZURE_SPEECH_KEY"
        private const val AZURE_SPEECH_REGION = "AZURE_SPEECH_REGION"
    }
    
    private var apiKeys: Map<String, String> = emptyMap()
    private var isInitialized = false
    
    /**
     * Initialize API keys from assets
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔑 Loading API keys from assets...")
            
            val inputStream = context.assets.open(API_KEYS_FILE)
            val reader = BufferedReader(InputStreamReader(inputStream))
            
            val keys = mutableMapOf<String, String>()
            
            reader.useLines { lines ->
                lines.forEach { line ->
                    val trimmedLine = line.trim()
                    if (trimmedLine.isNotEmpty() && !trimmedLine.startsWith("#")) {
                        val parts = trimmedLine.split("=", limit = 2)
                        if (parts.size == 2) {
                            val key = parts[0].trim()
                            val value = parts[1].trim().removeSurrounding("\"")
                            keys[key] = value
                        }
                    }
                }
            }
            
            apiKeys = keys.toMap()
            isInitialized = true
            
            Log.d(TAG, "✅ API keys loaded successfully (${apiKeys.size} keys)")
            validateApiKeys()
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to load API keys: ${e.message}", e)
            false
        }
    }
    
    /**
     * Get Cohere API key
     */
    fun getCohereApiKey(): String? {
        return getApiKey(COHERE_KEY)
    }
    
    /**
     * Get Perplexity API key
     */
    fun getPerplexityApiKey(): String? {
        return getApiKey(PERPLEXITY_KEY)
    }
    
    /**
     * Get Porcupine access key
     */
    fun getPorcupineAccessKey(): String? {
        return getApiKey(PORCUPINE_KEY)
    }
    
    /**
     * Get Azure Speech key
     */
    fun getAzureSpeechKey(): String? {
        return getApiKey(AZURE_SPEECH_KEY)
    }
    
    /**
     * Get Azure Speech region
     */
    fun getAzureSpeechRegion(): String? {
        return getApiKey(AZURE_SPEECH_REGION) ?: "eastus"
    }
    
    /**
     * Get API key by identifier
     */
    private fun getApiKey(keyIdentifier: String): String? {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ API keys not initialized, call initialize() first")
            return null
        }
        
        val key = apiKeys[keyIdentifier]
        if (key.isNullOrEmpty()) {
            Log.w(TAG, "⚠️ API key not found: $keyIdentifier")
            return null
        }
        
        return key
    }
    
    /**
     * Check if all required API keys are available
     */
    fun areAllKeysAvailable(): Boolean {
        return getCohereApiKey() != null &&
                getPerplexityApiKey() != null &&
                getPorcupineAccessKey() != null &&
                getAzureSpeechKey() != null &&
                getAzureSpeechRegion() != null
    }
    
    /**
     * Get missing API keys
     */
    fun getMissingKeys(): List<String> {
        val missing = mutableListOf<String>()
        
        if (getCohereApiKey() == null) missing.add(COHERE_KEY)
        if (getPerplexityApiKey() == null) missing.add(PERPLEXITY_KEY)
        if (getPorcupineAccessKey() == null) missing.add(PORCUPINE_KEY)
        if (getAzureSpeechKey() == null) missing.add(AZURE_SPEECH_KEY)
        if (getAzureSpeechRegion() == null) missing.add(AZURE_SPEECH_REGION)
        
        return missing
    }
    
    /**
     * Validate API keys format
     */
    private fun validateApiKeys() {
        val validationResults = mutableListOf<String>()
        
        // Validate Cohere key (should start with 'co-')
        getCohereApiKey()?.let { key ->
            if (!key.startsWith("co-")) {
                validationResults.add("⚠️ Cohere API key format may be invalid")
            } else {
                validationResults.add("✅ Cohere API key format valid")
            }
        }
        
        // Validate Porcupine key (should be alphanumeric)
        getPorcupineAccessKey()?.let { key ->
            if (key.length < 20) {
                validationResults.add("⚠️ Porcupine access key may be too short")
            } else {
                validationResults.add("✅ Porcupine access key format valid")
            }
        }
        
        // Validate Azure Speech key (should be 32 characters)
        getAzureSpeechKey()?.let { key ->
            if (key.length != 32) {
                validationResults.add("⚠️ Azure Speech key should be 32 characters")
            } else {
                validationResults.add("✅ Azure Speech key format valid")
            }
        }
        
        // Validate Azure region
        getAzureSpeechRegion()?.let { region ->
            val validRegions = listOf("eastus", "westus", "westus2", "eastus2", "centralus", "northcentralus", "southcentralus", "westcentralus")
            if (region in validRegions) {
                validationResults.add("✅ Azure Speech region valid")
            } else {
                validationResults.add("⚠️ Azure Speech region may be invalid: $region")
            }
        }
        
        validationResults.forEach { result ->
            Log.d(TAG, result)
        }
    }
    
    /**
     * Get API key status summary
     */
    fun getApiKeyStatus(): Map<String, Boolean> {
        return mapOf(
            "Cohere" to (getCohereApiKey() != null),
            "Perplexity" to (getPerplexityApiKey() != null),
            "Porcupine" to (getPorcupineAccessKey() != null),
            "Azure Speech" to (getAzureSpeechKey() != null),
            "Azure Region" to (getAzureSpeechRegion() != null)
        )
    }
}
