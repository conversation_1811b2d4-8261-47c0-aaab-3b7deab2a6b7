package com.zara.assistant.presentation.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color

/**
 * Zara Neumorphism Theme - Beautiful soft UI design
 */

// Light theme colors
private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF6366F1),           // Indigo
    onPrimary = Color.White,
    primaryContainer = Color(0xFFE0E7FF),
    onPrimaryContainer = Color(0xFF1E1B4B),
    
    secondary = Color(0xFF8B5CF6),         // Purple
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFF3E8FF),
    onSecondaryContainer = Color(0xFF581C87),
    
    tertiary = Color(0xFF06B6D4),          // Cyan
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFCFFAFE),
    onTertiaryContainer = Color(0xFF164E63),
    
    error = Color(0xFFEF4444),             // Red
    onError = Color.White,
    errorContainer = Color(0xFFFEE2E2),
    onErrorContainer = Color(0xFF991B1B),
    
    background = Color(0xFFF8FAFC),        // Very light gray
    onBackground = Color(0xFF1E293B),
    
    surface = Color(0xFFFFFFFF),           // White
    onSurface = Color(0xFF1E293B),
    surfaceVariant = Color(0xFFF1F5F9),
    onSurfaceVariant = Color(0xFF475569),
    
    outline = Color(0xFFCBD5E1),
    outlineVariant = Color(0xFFE2E8F0),
    
    scrim = Color(0x80000000),
    
    inverseSurface = Color(0xFF1E293B),
    inverseOnSurface = Color(0xFFF8FAFC),
    inversePrimary = Color(0xFFA5B4FC)
)

// Dark theme colors
private val DarkColorScheme = darkColorScheme(
    primary = Color(0xFFA5B4FC),           // Light indigo
    onPrimary = Color(0xFF1E1B4B),
    primaryContainer = Color(0xFF3730A3),
    onPrimaryContainer = Color(0xFFE0E7FF),
    
    secondary = Color(0xFFC4B5FD),         // Light purple
    onSecondary = Color(0xFF581C87),
    secondaryContainer = Color(0xFF7C3AED),
    onSecondaryContainer = Color(0xFFF3E8FF),
    
    tertiary = Color(0xFF67E8F9),          // Light cyan
    onTertiary = Color(0xFF164E63),
    tertiaryContainer = Color(0xFF0891B2),
    onTertiaryContainer = Color(0xFFCFFAFE),
    
    error = Color(0xFFFCA5A5),             // Light red
    onError = Color(0xFF991B1B),
    errorContainer = Color(0xFFDC2626),
    onErrorContainer = Color(0xFFFEE2E2),
    
    background = Color(0xFF0F172A),        // Very dark blue
    onBackground = Color(0xFFF8FAFC),
    
    surface = Color(0xFF1E293B),           // Dark blue-gray
    onSurface = Color(0xFFF8FAFC),
    surfaceVariant = Color(0xFF334155),
    onSurfaceVariant = Color(0xFFCBD5E1),
    
    outline = Color(0xFF64748B),
    outlineVariant = Color(0xFF475569),
    
    scrim = Color(0x80000000),
    
    inverseSurface = Color(0xFFF8FAFC),
    inverseOnSurface = Color(0xFF1E293B),
    inversePrimary = Color(0xFF6366F1)
)

// Neumorphism colors
data class NeumorphismColors(
    val lightShadow: Color,
    val darkShadow: Color,
    val highlight: Color,
    val surface: Color,
    val pressed: Color
)

val LightNeumorphismColors = NeumorphismColors(
    lightShadow = Color(0xFFFFFFFF),       // White highlight
    darkShadow = Color(0xFFD1D5DB),        // Light gray shadow
    highlight = Color(0xFFFEFEFE),         // Almost white
    surface = Color(0xFFF8FAFC),           // Light surface
    pressed = Color(0xFFE5E7EB)            // Pressed state
)

val DarkNeumorphismColors = NeumorphismColors(
    lightShadow = Color(0xFF374151),       // Lighter dark
    darkShadow = Color(0xFF111827),        // Darker shadow
    highlight = Color(0xFF4B5563),         // Dark highlight
    surface = Color(0xFF1F2937),           // Dark surface
    pressed = Color(0xFF1A202C)            // Dark pressed state
)

// Composition local for neumorphism colors
val LocalNeumorphismColors = staticCompositionLocalOf { LightNeumorphismColors }

@Composable
fun ZaraTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) DarkColorScheme else LightColorScheme
    val neumorphismColors = if (darkTheme) DarkNeumorphismColors else LightNeumorphismColors

    CompositionLocalProvider(
        LocalNeumorphismColors provides neumorphismColors
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = ZaraTypography,
            content = content
        )
    }
}

// Extension to access neumorphism colors
val MaterialTheme.neumorphismColors: NeumorphismColors
    @Composable get() = LocalNeumorphismColors.current
