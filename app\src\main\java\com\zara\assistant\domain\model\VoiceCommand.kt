package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Voice Command Model - Represents a voice command with processing metadata
 */
@Parcelize
data class VoiceCommand(
    val id: String = "",
    val originalText: String,
    val processedText: String = originalText,
    val language: String,
    val confidence: Float,
    val timestamp: Date = Date(),
    val category: CommandCategory,
    val intent: CommandIntent,
    val parameters: Map<String, String> = emptyMap(),
    val isProcessed: Boolean = false,
    val processingTimeMs: Long = 0L,
    val source: VoiceSource = VoiceSource.MANUAL
) : Parcelable

/**
 * Command Categories for intelligent routing
 */
enum class CommandCategory {
    DEVICE_CONTROL,    // WiFi, Bluetooth, settings
    APP_MANAGEMENT,    // Open, close, switch apps
    COMMUNICATION,     // Calls, messages, contacts
    MEDIA_CONTROL,     // Music, videos, playback
    INFORMATION,       // Weather, search, questions
    SYSTEM_ACTION,     // Back, home, notifications
    AI_CONVERSATION,   // General AI chat
    UNKNOWN
}

/**
 * Command Intents for specific actions
 */
enum class CommandIntent {
    // Device Control
    TOGGLE_WIFI,
    TOGGLE_BLUETOOTH,
    TOGGLE_MOBILE_DATA,
    TOGGLE_HOTSPOT,
    TOGGLE_NFC,
    TOGGLE_DND,
    SET_BRIGHTNESS,
    SET_VOLUME,
    TOGGLE_FLASHLIGHT,
    
    // App Management
    OPEN_APP,
    CLOSE_APP,
    SWITCH_APP,
    UNINSTALL_APP,
    
    // Communication
    MAKE_CALL,
    SEND_MESSAGE,
    READ_MESSAGES,
    READ_NOTIFICATIONS,
    
    // Media Control
    PLAY_MUSIC,
    PAUSE_MUSIC,
    NEXT_TRACK,
    PREVIOUS_TRACK,
    SET_VOLUME_MEDIA,
    
    // System Actions
    GO_BACK,
    GO_HOME,
    SHOW_RECENT_APPS,
    SHOW_NOTIFICATIONS,
    SHOW_QUICK_SETTINGS,
    TAKE_SCREENSHOT,
    LOCK_SCREEN,
    
    // Information
    GET_WEATHER,
    SEARCH_WEB,
    GET_TIME,
    GET_DATE,
    GET_LOCATION,
    
    // AI Conversation
    GENERAL_CHAT,
    ASK_QUESTION,
    GET_ADVICE,
    
    // Unknown
    UNKNOWN
}

/**
 * Voice Source - How the command was triggered
 */
enum class VoiceSource {
    WAKE_WORD,    // Triggered by "Hey Zara"
    MANUAL,       // Manual button press
    AUTO_LISTEN   // Auto-listen after response
}

/**
 * Voice Processing State
 */
enum class VoiceState {
    IDLE,           // Not listening or speaking
    LISTENING,      // Actively listening for voice input
    PROCESSING,     // Processing voice command
    SPEAKING,       // Speaking response
    ERROR           // Error state
}


