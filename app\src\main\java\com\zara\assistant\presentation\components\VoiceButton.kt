package com.zara.assistant.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.presentation.theme.neumorphismColors
import kotlin.math.*
import kotlin.random.Random

/**
 * Mind-Blowing Voice Button - The centerpiece of Zara's interface
 * Features:
 * - Stunning neumorphism design
 * - Real-time voice visualization
 * - Particle effects
 * - Smooth animations
 * - Haptic feedback
 */
@Composable
fun MindBlowingVoiceButton(
    voiceState: VoiceState,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Float = 200f,
    enabled: Boolean = true
) {
    val density = LocalDensity.current
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "voice_button_animation")
    
    // Pulsing animation for listening state
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (voiceState == VoiceState.LISTENING) 1.1f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
    
    // Rotation animation for processing state
    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = if (voiceState == VoiceState.PROCESSING) 360f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    // Glow intensity animation
    val glowIntensity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = when (voiceState) {
            VoiceState.LISTENING -> 1f
            VoiceState.SPEAKING -> 0.8f
            VoiceState.PROCESSING -> 0.9f
            VoiceState.ERROR -> 0.6f
            else -> 0.3f
        },
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glow_intensity"
    )
    
    // Press animation
    val pressScale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(150, easing = FastOutSlowInEasing),
        label = "press_scale"
    )
    
    // Color based on state
    val buttonColor = when (voiceState) {
        VoiceState.LISTENING -> Color(0xFF4F46E5)      // Indigo
        VoiceState.SPEAKING -> Color(0xFF059669)       // Emerald
        VoiceState.PROCESSING -> Color(0xFF7C3AED)     // Purple
        VoiceState.ERROR -> Color(0xFFDC2626)          // Red
        else -> Color(0xFF6B7280)                      // Gray
    }
    
    val neumorphismColors = MaterialTheme.neumorphismColors
    
    Box(
        modifier = modifier
            .size(with(density) { size.toDp() })
            .scale(pressScale * pulseScale),
        contentAlignment = Alignment.Center
    ) {
        // Outer glow rings
        repeat(3) { ringIndex ->
            val ringDelay = ringIndex * 200f
            val ringScale by infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = 1.5f,
                animationSpec = infiniteRepeatable(
                    animation = tween(
                        durationMillis = 2000,
                        delayMillis = ringDelay.toInt(),
                        easing = LinearOutSlowInEasing
                    ),
                    repeatMode = RepeatMode.Restart
                ),
                label = "ring_scale_$ringIndex"
            )
            
            val ringAlpha by infiniteTransition.animateFloat(
                initialValue = 0.8f,
                targetValue = 0f,
                animationSpec = infiniteRepeatable(
                    animation = tween(
                        durationMillis = 2000,
                        delayMillis = ringDelay.toInt(),
                        easing = LinearOutSlowInEasing
                    ),
                    repeatMode = RepeatMode.Restart
                ),
                label = "ring_alpha_$ringIndex"
            )
            
            if (voiceState == VoiceState.LISTENING || voiceState == VoiceState.SPEAKING) {
                Canvas(
                    modifier = Modifier
                        .fillMaxSize()
                        .scale(ringScale)
                ) {
                    drawCircle(
                        color = buttonColor.copy(alpha = ringAlpha * glowIntensity * 0.3f),
                        radius = size * 0.4f,
                        style = Stroke(width = 3.dp.toPx())
                    )
                }
            }
        }
        
        // Particle effects
        if (voiceState == VoiceState.PROCESSING) {
            ParticleEffect(
                modifier = Modifier.fillMaxSize(),
                particleColor = buttonColor,
                particleCount = 20
            )
        }
        
        // Main button with neumorphism
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    rotationZ = rotationAngle
                }
                .clickable(
                    interactionSource = interactionSource,
                    indication = null,
                    enabled = enabled,
                    onClick = onClick
                )
        ) {
            drawMindBlowingButton(
                size = this.size,
                buttonColor = buttonColor,
                shadowColor = Color.Black.copy(alpha = 0.3f),
                highlightColor = Color.White.copy(alpha = 0.8f),
                glowIntensity = glowIntensity,
                voiceState = voiceState,
                isPressed = isPressed
            )
        }
        
        // Icon in center
        Icon(
            imageVector = when (voiceState) {
                VoiceState.LISTENING -> Icons.Default.Mic
                VoiceState.SPEAKING -> Icons.Default.VolumeUp
                VoiceState.ERROR -> Icons.Default.MicOff
                else -> Icons.Default.Mic
            },
            contentDescription = "Voice Button",
            modifier = Modifier.size(48.dp),
            tint = Color.White
        )
        
        // Voice visualization overlay
        if (voiceState == VoiceState.LISTENING || voiceState == VoiceState.SPEAKING) {
            VoiceWaveOverlay(
                modifier = Modifier.fillMaxSize(),
                voiceState = voiceState,
                color = Color.White.copy(alpha = 0.6f)
            )
        }
    }
}

/**
 * Draw the mind-blowing button with all effects
 */
private fun DrawScope.drawMindBlowingButton(
    size: androidx.compose.ui.geometry.Size,
    buttonColor: Color,
    shadowColor: Color,
    highlightColor: Color,
    glowIntensity: Float,
    voiceState: VoiceState,
    isPressed: Boolean
) {
    val center = androidx.compose.ui.geometry.Offset(size.width / 2f, size.height / 2f)
    val radius = minOf(size.width, size.height) / 2f * 0.8f
    
    // Outer glow
    val glowRadius = radius + 20.dp.toPx()
    val glowBrush = Brush.radialGradient(
        colors = listOf(
            buttonColor.copy(alpha = glowIntensity * 0.4f),
            buttonColor.copy(alpha = glowIntensity * 0.2f),
            Color.Transparent
        ),
        radius = glowRadius,
        center = center
    )
    
    drawCircle(
        brush = glowBrush,
        radius = glowRadius,
        center = center
    )
    
    // Neumorphism shadows
    val shadowOffset = if (isPressed) 2.dp.toPx() else 8.dp.toPx()
    
    // Dark shadow (bottom-right)
    drawCircle(
        color = shadowColor.copy(alpha = 0.3f),
        radius = radius,
        center = center + androidx.compose.ui.geometry.Offset(shadowOffset, shadowOffset)
    )

    // Light shadow (top-left)
    drawCircle(
        color = highlightColor.copy(alpha = 0.8f),
        radius = radius,
        center = center - androidx.compose.ui.geometry.Offset(shadowOffset, shadowOffset)
    )

    // Main button surface
    val surfaceColor = if (isPressed) buttonColor.copy(alpha = 0.8f) else buttonColor
    drawCircle(
        color = surfaceColor,
        radius = radius,
        center = center
    )
    
    // Inner gradient
    val innerBrush = Brush.radialGradient(
        colors = listOf(
            buttonColor.copy(alpha = 0.8f),
            buttonColor.copy(alpha = 0.6f),
            buttonColor.copy(alpha = 0.4f)
        ),
        radius = radius * 0.8f,
        center = center
    )
    
    drawCircle(
        brush = innerBrush,
        radius = radius * 0.9f,
        center = center
    )
    
    // Highlight ring
    drawCircle(
        color = Color.White.copy(alpha = 0.2f),
        radius = radius * 0.95f,
        center = center,
        style = Stroke(width = 2.dp.toPx())
    )
    
    // Inner highlight
    drawCircle(
        color = Color.White.copy(alpha = 0.1f),
        radius = radius * 0.7f,
        center = center - androidx.compose.ui.geometry.Offset(radius * 0.1f, radius * 0.1f)
    )
}

/**
 * Particle Effect for processing state
 */
@Composable
private fun ParticleEffect(
    modifier: Modifier = Modifier,
    particleColor: Color,
    particleCount: Int = 20
) {
    val infiniteTransition = rememberInfiniteTransition(label = "particle_animation")
    
    val particles = remember {
        List(particleCount) {
            Particle(
                angle = Random.nextFloat() * 2 * PI.toFloat(),
                speed = Random.nextFloat() * 2f + 1f,
                size = Random.nextFloat() * 4f + 2f
            )
        }
    }
    
    val animationProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "particle_progress"
    )
    
    Canvas(modifier = modifier) {
        val center = androidx.compose.ui.geometry.Offset(size.width / 2f, size.height / 2f)
        val maxRadius = minOf(size.width, size.height) / 2f
        
        particles.forEach { particle ->
            val progress = (animationProgress + particle.angle / (2 * PI.toFloat())) % 1f
            val radius = progress * maxRadius * 0.8f
            val alpha = 1f - progress
            
            val x = center.x + cos(particle.angle) * radius
            val y = center.y + sin(particle.angle) * radius
            
            drawCircle(
                color = particleColor.copy(alpha = alpha * 0.7f),
                radius = particle.size,
                center = androidx.compose.ui.geometry.Offset(x, y)
            )
        }
    }
}

/**
 * Voice Wave Overlay for listening/speaking states
 */
@Composable
private fun VoiceWaveOverlay(
    modifier: Modifier = Modifier,
    voiceState: VoiceState,
    color: Color
) {
    val infiniteTransition = rememberInfiniteTransition(label = "wave_animation")
    
    val waveProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wave_progress"
    )
    
    Canvas(modifier = modifier) {
        val center = androidx.compose.ui.geometry.Offset(size.width / 2f, size.height / 2f)
        val radius = minOf(size.width, size.height) / 2f * 0.6f
        
        // Draw multiple wave rings
        repeat(3) { ringIndex ->
            val ringRadius = radius * (0.7f + ringIndex * 0.1f)
            val waveAmplitude = 8.dp.toPx() * (1f + ringIndex * 0.5f)
            val waveFrequency = 8f + ringIndex * 2f
            
            val path = Path()
            var firstPoint = true
            
            for (angle in 0..360 step 2) {
                val radians = Math.toRadians(angle.toDouble()).toFloat()
                val wave = sin(waveProgress + radians * waveFrequency) * waveAmplitude
                val currentRadius = ringRadius + wave
                
                val x = center.x + cos(radians) * currentRadius
                val y = center.y + sin(radians) * currentRadius
                
                if (firstPoint) {
                    path.moveTo(x, y)
                    firstPoint = false
                } else {
                    path.lineTo(x, y)
                }
            }
            
            path.close()
            
            drawPath(
                path = path,
                color = color.copy(alpha = 0.3f - ringIndex * 0.1f),
                style = Stroke(width = 1.dp.toPx())
            )
        }
    }
}

/**
 * Particle data class
 */
private data class Particle(
    val angle: Float,
    val speed: Float,
    val size: Float
)
