package com.zara.assistant.utils;

/**
 * Performance Metric data class
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0005J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c2\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c2\u0003J\t\u0010\u0017\u001a\u00020\u0005H\u00c2\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c2\u0003J;\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010\r\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\fR\u000e\u0010\u0007\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/zara/assistant/utils/PerformanceMetric;", "", "operationName", "", "totalDuration", "", "count", "minDuration", "maxDuration", "(Ljava/lang/String;JJJJ)V", "averageDuration", "getAverageDuration", "()J", "measurementCount", "getMeasurementCount", "getOperationName", "()Ljava/lang/String;", "addMeasurement", "", "duration", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class PerformanceMetric {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String operationName = null;
    private long totalDuration;
    private long count;
    private long minDuration;
    private long maxDuration;
    
    public PerformanceMetric(@org.jetbrains.annotations.NotNull()
    java.lang.String operationName, long totalDuration, long count, long minDuration, long maxDuration) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOperationName() {
        return null;
    }
    
    public final long getAverageDuration() {
        return 0L;
    }
    
    public final long getMeasurementCount() {
        return 0L;
    }
    
    public final void addMeasurement(long duration) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    private final long component2() {
        return 0L;
    }
    
    private final long component3() {
        return 0L;
    }
    
    private final long component4() {
        return 0L;
    }
    
    private final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.utils.PerformanceMetric copy(@org.jetbrains.annotations.NotNull()
    java.lang.String operationName, long totalDuration, long count, long minDuration, long maxDuration) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}