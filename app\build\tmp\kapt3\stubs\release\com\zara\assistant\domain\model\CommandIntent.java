package com.zara.assistant.domain.model;

/**
 * Command Intents for specific actions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b(\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019j\u0002\b\u001aj\u0002\b\u001bj\u0002\b\u001cj\u0002\b\u001dj\u0002\b\u001ej\u0002\b\u001fj\u0002\b j\u0002\b!j\u0002\b\"j\u0002\b#j\u0002\b$j\u0002\b%j\u0002\b&j\u0002\b\'j\u0002\b(\u00a8\u0006)"}, d2 = {"Lcom/zara/assistant/domain/model/CommandIntent;", "", "(Ljava/lang/String;I)V", "TOGGLE_WIFI", "TOGGLE_BLUETOOTH", "TOGGLE_MOBILE_DATA", "TOGGLE_HOTSPOT", "TOGGLE_NFC", "TOGGLE_DND", "SET_BRIGHTNESS", "SET_VOLUME", "TOGGLE_FLASHLIGHT", "OPEN_APP", "CLOSE_APP", "SWITCH_APP", "UNINSTALL_APP", "MAKE_CALL", "SEND_MESSAGE", "READ_MESSAGES", "READ_NOTIFICATIONS", "PLAY_MUSIC", "PAUSE_MUSIC", "NEXT_TRACK", "PREVIOUS_TRACK", "SET_VOLUME_MEDIA", "GO_BACK", "GO_HOME", "SHOW_RECENT_APPS", "SHOW_NOTIFICATIONS", "SHOW_QUICK_SETTINGS", "TAKE_SCREENSHOT", "LOCK_SCREEN", "GET_WEATHER", "SEARCH_WEB", "GET_TIME", "GET_DATE", "GET_LOCATION", "GENERAL_CHAT", "ASK_QUESTION", "GET_ADVICE", "UNKNOWN", "app_release"})
public enum CommandIntent {
    /*public static final*/ TOGGLE_WIFI /* = new TOGGLE_WIFI() */,
    /*public static final*/ TOGGLE_BLUETOOTH /* = new TOGGLE_BLUETOOTH() */,
    /*public static final*/ TOGGLE_MOBILE_DATA /* = new TOGGLE_MOBILE_DATA() */,
    /*public static final*/ TOGGLE_HOTSPOT /* = new TOGGLE_HOTSPOT() */,
    /*public static final*/ TOGGLE_NFC /* = new TOGGLE_NFC() */,
    /*public static final*/ TOGGLE_DND /* = new TOGGLE_DND() */,
    /*public static final*/ SET_BRIGHTNESS /* = new SET_BRIGHTNESS() */,
    /*public static final*/ SET_VOLUME /* = new SET_VOLUME() */,
    /*public static final*/ TOGGLE_FLASHLIGHT /* = new TOGGLE_FLASHLIGHT() */,
    /*public static final*/ OPEN_APP /* = new OPEN_APP() */,
    /*public static final*/ CLOSE_APP /* = new CLOSE_APP() */,
    /*public static final*/ SWITCH_APP /* = new SWITCH_APP() */,
    /*public static final*/ UNINSTALL_APP /* = new UNINSTALL_APP() */,
    /*public static final*/ MAKE_CALL /* = new MAKE_CALL() */,
    /*public static final*/ SEND_MESSAGE /* = new SEND_MESSAGE() */,
    /*public static final*/ READ_MESSAGES /* = new READ_MESSAGES() */,
    /*public static final*/ READ_NOTIFICATIONS /* = new READ_NOTIFICATIONS() */,
    /*public static final*/ PLAY_MUSIC /* = new PLAY_MUSIC() */,
    /*public static final*/ PAUSE_MUSIC /* = new PAUSE_MUSIC() */,
    /*public static final*/ NEXT_TRACK /* = new NEXT_TRACK() */,
    /*public static final*/ PREVIOUS_TRACK /* = new PREVIOUS_TRACK() */,
    /*public static final*/ SET_VOLUME_MEDIA /* = new SET_VOLUME_MEDIA() */,
    /*public static final*/ GO_BACK /* = new GO_BACK() */,
    /*public static final*/ GO_HOME /* = new GO_HOME() */,
    /*public static final*/ SHOW_RECENT_APPS /* = new SHOW_RECENT_APPS() */,
    /*public static final*/ SHOW_NOTIFICATIONS /* = new SHOW_NOTIFICATIONS() */,
    /*public static final*/ SHOW_QUICK_SETTINGS /* = new SHOW_QUICK_SETTINGS() */,
    /*public static final*/ TAKE_SCREENSHOT /* = new TAKE_SCREENSHOT() */,
    /*public static final*/ LOCK_SCREEN /* = new LOCK_SCREEN() */,
    /*public static final*/ GET_WEATHER /* = new GET_WEATHER() */,
    /*public static final*/ SEARCH_WEB /* = new SEARCH_WEB() */,
    /*public static final*/ GET_TIME /* = new GET_TIME() */,
    /*public static final*/ GET_DATE /* = new GET_DATE() */,
    /*public static final*/ GET_LOCATION /* = new GET_LOCATION() */,
    /*public static final*/ GENERAL_CHAT /* = new GENERAL_CHAT() */,
    /*public static final*/ ASK_QUESTION /* = new ASK_QUESTION() */,
    /*public static final*/ GET_ADVICE /* = new GET_ADVICE() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    CommandIntent() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.CommandIntent> getEntries() {
        return null;
    }
}