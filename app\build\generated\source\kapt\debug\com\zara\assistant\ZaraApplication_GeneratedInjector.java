package com.zara.assistant;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = ZaraApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface ZaraApplication_GeneratedInjector {
  void injectZaraApplication(ZaraApplication zaraApplication);
}
