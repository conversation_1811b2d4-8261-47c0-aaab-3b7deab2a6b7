// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.LocalCommandProcessor;
import com.zara.assistant.services.SystemControlManager;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideLocalCommandProcessorFactory implements Factory<LocalCommandProcessor> {
  private final Provider<Context> contextProvider;

  private final Provider<SystemControlManager> systemControlManagerProvider;

  private final Provider<PerformanceUtils> performanceUtilsProvider;

  public ServiceModule_ProvideLocalCommandProcessorFactory(Provider<Context> contextProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    this.contextProvider = contextProvider;
    this.systemControlManagerProvider = systemControlManagerProvider;
    this.performanceUtilsProvider = performanceUtilsProvider;
  }

  @Override
  public LocalCommandProcessor get() {
    return provideLocalCommandProcessor(contextProvider.get(), systemControlManagerProvider.get(), performanceUtilsProvider.get());
  }

  public static ServiceModule_ProvideLocalCommandProcessorFactory create(
      Provider<Context> contextProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<PerformanceUtils> performanceUtilsProvider) {
    return new ServiceModule_ProvideLocalCommandProcessorFactory(contextProvider, systemControlManagerProvider, performanceUtilsProvider);
  }

  public static LocalCommandProcessor provideLocalCommandProcessor(Context context,
      SystemControlManager systemControlManager, PerformanceUtils performanceUtils) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideLocalCommandProcessor(context, systemControlManager, performanceUtils));
  }
}
