package com.zara.assistant

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.zara.assistant.ui.MainActivity
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented tests for MainActivity
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class MainActivityTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun mainScreen_displaysCorrectly() {
        // Verify main UI elements are displayed
        composeTestRule.onNodeWithText("Zara").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Voice Button").assertIsDisplayed()
        composeTestRule.onNodeWithText("Ready to listen").assertIsDisplayed()
    }

    @Test
    fun voiceButton_isClickable() {
        // Test voice button interaction
        composeTestRule.onNodeWithContentDescription("Voice Button")
            .assertIsDisplayed()
            .performClick()
        
        // Note: In a real test, you'd verify the state change
        // This would require mocking the ViewModel or using test doubles
    }

    @Test
    fun settingsButton_opensSettings() {
        // Test settings button
        composeTestRule.onNodeWithContentDescription("Settings")
            .assertIsDisplayed()
            .performClick()
        
        // Verify settings interaction
        // In a complete implementation, this would navigate to settings
    }

    @Test
    fun wakeWordToggle_isDisplayed() {
        // Verify wake word toggle is present
        composeTestRule.onNodeWithText("Wake word detection enabled")
            .assertIsDisplayed()
    }

    @Test
    fun permissionDialog_showsWhenNeeded() {
        // This test would require triggering the permission dialog state
        // In a real implementation, you'd set up the ViewModel state
        // to show the permission dialog and then verify it appears
    }
}
