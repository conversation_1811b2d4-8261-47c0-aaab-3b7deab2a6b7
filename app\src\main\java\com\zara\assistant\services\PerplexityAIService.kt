package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.*
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Perplexity AI Service - Information queries using Perplexity API
 * Optimized for real-time information retrieval and factual responses
 */
@Singleton
class PerplexityAIService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: <PERSON><PERSON><PERSON><PERSON><PERSON>anager,
    private val performanceUtils: PerformanceUtils
) {
    
    companion object {
        private const val TAG = "PerplexityAI"
        private const val BASE_URL = "https://api.perplexity.ai/"
        private const val CHAT_ENDPOINT = "chat/completions"
    }
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(Constants.AI.AI_CONNECT_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .readTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .writeTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .build()

    /**
     * Get information response from Perplexity
     */
    suspend fun getResponse(
        query: String,
        language: String = Constants.Voice.LANGUAGE_ENGLISH,
        context: String? = null
    ): Result<AIResponse> = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("Perplexity_GetResponse")
            
            Log.d(TAG, "🔍 Getting Perplexity response for: ${query.take(50)}...")
            
            val apiKey = apiKeyManager.getPerplexityApiKey()
            if (apiKey == null) {
                return@withContext Result.failure(Exception("Perplexity API key not available"))
            }
            
            // Build messages for chat completion
            val messages = buildMessages(query, language, context)
            
            // Create request
            val request = createPerplexityRequest(messages, apiKey)
            
            // Execute request with timeout
            val response = withTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS) {
                httpClient.newCall(request).execute()
            }
            
            val result = if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    parsePerplexityResponse(responseBody, measurementId)
                } else {
                    Result.failure(Exception("Empty response from Perplexity"))
                }
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Log.e(TAG, "❌ Perplexity API error: ${response.code} - $errorBody")
                Result.failure(Exception("Perplexity API error: ${response.code}"))
            }
            
            performanceUtils.endMeasurement(measurementId)
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting Perplexity response: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Build messages for chat completion
     */
    private fun buildMessages(
        query: String,
        language: String,
        context: String?
    ): List<PerplexityMessage> {
        val messages = mutableListOf<PerplexityMessage>()
        
        // System message
        val systemPrompt = buildSystemPrompt(language)
        messages.add(PerplexityMessage(role = "system", content = systemPrompt))
        
        // Add context if provided
        if (!context.isNullOrBlank()) {
            messages.add(PerplexityMessage(
                role = "user", 
                content = "Context: $context"
            ))
        }
        
        // User query
        messages.add(PerplexityMessage(role = "user", content = query))
        
        return messages
    }

    /**
     * Build system prompt for Perplexity
     */
    private fun buildSystemPrompt(language: String): String {
        val languageInstruction = when (language) {
            Constants.Voice.LANGUAGE_HINDI -> "Respond in Hindi if the user asks in Hindi, otherwise respond in English."
            else -> "Respond in English unless specifically asked to use another language."
        }
        
        return """You are Zara, an AI assistant specialized in providing accurate, up-to-date information.

Your capabilities include:
- Answering factual questions with current information
- Providing weather updates, news, and real-time data
- Explaining concepts and definitions
- Searching for specific information
- Giving brief, accurate responses

$languageInstruction

Keep responses concise but informative. Always provide accurate, current information when possible."""
    }

    /**
     * Create Perplexity API request
     */
    private fun createPerplexityRequest(messages: List<PerplexityMessage>, apiKey: String): Request {
        val requestBody = PerplexityRequest(
            model = Constants.AI.PERPLEXITY_MODEL,
            messages = messages,
            maxTokens = Constants.AI.MAX_TOKENS,
            temperature = Constants.AI.TEMPERATURE,
            topP = Constants.AI.TOP_P,
            stream = false
        )
        
        val jsonBody = json.encodeToString(PerplexityRequest.serializer(), requestBody)
        
        return Request.Builder()
            .url(BASE_URL + CHAT_ENDPOINT)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", "application/json")
            .addHeader("User-Agent", "Zara-Assistant/1.0")
            .post(jsonBody.toRequestBody("application/json".toMediaType()))
            .build()
    }

    /**
     * Parse Perplexity API response
     */
    private fun parsePerplexityResponse(responseBody: String, measurementId: String): Result<AIResponse> {
        return try {
            val perplexityResponse = json.decodeFromString(PerplexityResponse.serializer(), responseBody)
            
            val choice = perplexityResponse.choices.firstOrNull()
            val text = choice?.message?.content?.trim()
            
            if (text.isNullOrBlank()) {
                Result.failure(Exception("Empty response from Perplexity"))
            } else {
                val processingTime = performanceUtils.endMeasurement(measurementId)
                
                val aiResponse = AIResponse(
                    id = UUID.randomUUID().toString(),
                    text = text,
                    source = AISource.PERPLEXITY,
                    confidence = 0.95f, // Perplexity provides high-quality factual information
                    processingTimeMs = processingTime,
                    timestamp = Date(),
                    metadata = mapOf(
                        "model" to Constants.AI.PERPLEXITY_MODEL,
                        "finishReason" to (choice?.finishReason ?: "unknown"),
                        "usage" to (perplexityResponse.usage?.totalTokens?.toString() ?: "0")
                    )
                )
                
                Log.d(TAG, "✅ Perplexity response: ${text.take(100)}... (${processingTime}ms)")
                Result.success(aiResponse)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error parsing Perplexity response: ${e.message}", e)
            Result.failure(Exception("Failed to parse Perplexity response: ${e.message}"))
        }
    }

    /**
     * Test Perplexity API connectivity
     */
    suspend fun testConnection(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🧪 Testing Perplexity connection...")
            
            val testResult = getResponse(
                query = "What is the current time?",
                language = Constants.Voice.LANGUAGE_ENGLISH
            )
            
            val isSuccess = testResult.isSuccess
            Log.d(TAG, "🧪 Perplexity connection test: ${if (isSuccess) "SUCCESS" else "FAILED"}")
            
            Result.success(isSuccess)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Perplexity connection test failed: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Check if Perplexity service is available
     */
    fun isAvailable(): Boolean {
        return apiKeyManager.getPerplexityApiKey() != null
    }

    /**
     * Get service status
     */
    fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "available" to isAvailable(),
            "apiKeyConfigured" to (apiKeyManager.getPerplexityApiKey() != null),
            "model" to Constants.AI.PERPLEXITY_MODEL,
            "baseUrl" to BASE_URL
        )
    }

    /**
     * Search for specific information
     */
    suspend fun searchInformation(
        searchQuery: String,
        language: String = Constants.Voice.LANGUAGE_ENGLISH
    ): Result<AIResponse> {
        val enhancedQuery = "Search for information about: $searchQuery"
        return getResponse(enhancedQuery, language)
    }

    /**
     * Get weather information
     */
    suspend fun getWeatherInfo(
        location: String? = null,
        language: String = Constants.Voice.LANGUAGE_ENGLISH
    ): Result<AIResponse> {
        val query = if (location != null) {
            "What is the current weather in $location?"
        } else {
            "What is the current weather?"
        }
        return getResponse(query, language)
    }

    /**
     * Get news information
     */
    suspend fun getNewsInfo(
        topic: String? = null,
        language: String = Constants.Voice.LANGUAGE_ENGLISH
    ): Result<AIResponse> {
        val query = if (topic != null) {
            "What are the latest news about $topic?"
        } else {
            "What are the latest news headlines?"
        }
        return getResponse(query, language)
    }
}

/**
 * Perplexity API Request Models
 */
@Serializable
data class PerplexityRequest(
    val model: String,
    val messages: List<PerplexityMessage>,
    val maxTokens: Int,
    val temperature: Float,
    val topP: Float,
    val stream: Boolean
)

@Serializable
data class PerplexityMessage(
    val role: String,
    val content: String
)

/**
 * Perplexity API Response Models
 */
@Serializable
data class PerplexityResponse(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<PerplexityChoice>,
    val usage: PerplexityUsage? = null
)

@Serializable
data class PerplexityChoice(
    val index: Int,
    val finishReason: String,
    val message: PerplexityMessage
)

@Serializable
data class PerplexityUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int
)
