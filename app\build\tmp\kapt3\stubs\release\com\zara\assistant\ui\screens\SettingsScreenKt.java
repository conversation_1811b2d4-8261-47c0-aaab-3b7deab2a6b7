package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\u001a$\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00022\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00040\u0007H\u0003\u001a\u001e\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\b2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u0003\u001aN\u0010\r\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u0007\u001a \u0010\u0013\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u0003\"\u0014\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"settingsCategories", "", "Lcom/zara/assistant/ui/screens/SettingsCategory;", "SettingsCategory", "", "category", "onItemClick", "Lkotlin/Function1;", "Lcom/zara/assistant/ui/screens/SettingsItem;", "SettingsItemRow", "item", "onClick", "Lkotlin/Function0;", "SettingsScreen", "onNavigateBack", "onNavigateToVoiceSettings", "onNavigateToPermissions", "onNavigateToAbout", "onNavigateToCommands", "SettingsTopBar", "modifier", "Landroidx/compose/ui/Modifier;", "app_release"})
public final class SettingsScreenKt {
    
    /**
     * Settings categories data
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.zara.assistant.ui.screens.SettingsCategory> settingsCategories = null;
    
    /**
     * Mind-Blowing Settings Screen - Beautiful and functional settings
     * Features:
     * - Stunning neumorphism design
     * - Animated interactions
     * - Organized setting categories
     * - Real-time status updates
     * - Smooth transitions
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SettingsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToVoiceSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToPermissions, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAbout, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCommands) {
    }
    
    /**
     * Settings top bar
     */
    @androidx.compose.runtime.Composable()
    private static final void SettingsTopBar(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Settings category section
     */
    @androidx.compose.runtime.Composable()
    private static final void SettingsCategory(com.zara.assistant.ui.screens.SettingsCategory category, kotlin.jvm.functions.Function1<? super com.zara.assistant.ui.screens.SettingsItem, kotlin.Unit> onItemClick) {
    }
    
    /**
     * Individual settings item row
     */
    @androidx.compose.runtime.Composable()
    private static final void SettingsItemRow(com.zara.assistant.ui.screens.SettingsItem item, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}