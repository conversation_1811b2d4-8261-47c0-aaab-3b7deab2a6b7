package com.zara.assistant

import com.zara.assistant.core.Constants
import com.zara.assistant.data.remote.api.CohereApiService
import com.zara.assistant.data.remote.api.CohereRequest
import com.zara.assistant.data.remote.api.PerplexityApiService
import com.zara.assistant.data.remote.dto.CohereGeneration
import com.zara.assistant.data.remote.dto.CohereResponse
import com.zara.assistant.data.repository.AIRepositoryImpl
import com.zara.assistant.domain.model.CommandType
import com.zara.assistant.domain.model.VoiceCommand
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import retrofit2.Response
import java.util.Date
import java.util.UUID

/**
 * Unit tests for AIRepositoryImpl
 */
class AIRepositoryTest {

    @Mock
    private lateinit var cohereApiService: CohereApiService

    @Mock
    private lateinit var perplexityApiService: PerplexityApiService

    private lateinit var aiRepository: AIRepositoryImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        aiRepository = AIRepositoryImpl(cohereApiService, perplexityApiService)
    }

    @Test
    fun `generateCohereResponse returns success when API call succeeds`() = runTest {
        // Given
        val prompt = "Hello, how are you?"
        val expectedResponse = "I'm doing well, thank you!"
        val cohereResponse = CohereResponse(
            id = "test-id",
            generations = listOf(
                CohereGeneration(
                    id = "gen-id",
                    text = expectedResponse,
                    likelihood = null,
                    token_likelihoods = null
                )
            ),
            prompt = prompt,
            meta = null
        )

        whenever(cohereApiService.generateText(any<CohereRequest>()))
            .thenReturn(Response.success(cohereResponse))

        // When
        val result = aiRepository.generateCohereResponse(prompt)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedResponse, result.getOrNull())
    }

    @Test
    fun `generateCohereResponse returns failure when API call fails`() = runTest {
        // Given
        val prompt = "Hello"
        whenever(cohereApiService.generateText(any<CohereRequest>()))
            .thenReturn(Response.error(500, okhttp3.ResponseBody.create(null, "Server Error")))

        // When
        val result = aiRepository.generateCohereResponse(prompt)

        // Then
        assertTrue(result.isFailure)
    }

    @Test
    fun `classifyCommand correctly identifies system commands`() = runTest {
        // Given
        val systemCommands = listOf(
            "open settings",
            "turn on wifi",
            "close app",
            "set brightness to 50%"
        )

        // When & Then
        systemCommands.forEach { command ->
            val result = aiRepository.classifyCommand(command)
            assertTrue(result.isSuccess)
            assertEquals(CommandType.SYSTEM_CONTROL, result.getOrNull())
        }
    }

    @Test
    fun `classifyCommand correctly identifies information queries`() = runTest {
        // Given
        val infoQueries = listOf(
            "what's the weather like?",
            "when is my next meeting?",
            "how do I cook pasta?",
            "search for restaurants nearby"
        )

        // When & Then
        infoQueries.forEach { query ->
            val result = aiRepository.classifyCommand(query)
            assertTrue(result.isSuccess)
            assertEquals(CommandType.INFORMATION, result.getOrNull())
        }
    }

    @Test
    fun `classifyCommand correctly identifies communication commands`() = runTest {
        // Given
        val commCommands = listOf(
            "call mom",
            "send message to John",
            "text Sarah hello"
        )

        // When & Then
        commCommands.forEach { command ->
            val result = aiRepository.classifyCommand(command)
            assertTrue(result.isSuccess)
            assertEquals(CommandType.COMMUNICATION, result.getOrNull())
        }
    }

    @Test
    fun `buildContextPrompt includes personality and style`() = runTest {
        // Given
        val command = "Tell me a joke"
        val history = listOf("User: Hello", "Zara: Hi there!")
        val personality = Constants.AIPersonality.FRIENDLY
        val style = Constants.AIResponseStyle.CONVERSATIONAL

        // When
        val prompt = aiRepository.buildContextPrompt(command, history, personality, style)

        // Then
        assertTrue(prompt.contains("friendly", ignoreCase = true))
        assertTrue(prompt.contains("conversational", ignoreCase = true))
        assertTrue(prompt.contains(command))
        assertTrue(prompt.contains("Hello"))
    }

    @Test
    fun `processCommand handles different command types appropriately`() = runTest {
        // Given
        val systemCommand = VoiceCommand(
            id = UUID.randomUUID().toString(),
            text = "open settings",
            timestamp = Date(),
            confidence = 0.9f,
            language = "en-US",
            commandType = CommandType.SYSTEM_CONTROL
        )

        // Mock successful Cohere response
        val cohereResponse = CohereResponse(
            id = "test-id",
            generations = listOf(
                CohereGeneration(
                    id = "gen-id",
                    text = "Opening settings for you",
                    likelihood = null,
                    token_likelihoods = null
                )
            ),
            prompt = "test",
            meta = null
        )

        whenever(cohereApiService.generateText(any<CohereRequest>()))
            .thenReturn(Response.success(cohereResponse))

        // When
        val result = aiRepository.processCommand(systemCommand)

        // Then
        assertTrue(result.isSuccess)
        val response = result.getOrNull()
        assertNotNull(response)
        assertEquals(systemCommand.id, response?.commandId)
    }

    @Test
    fun `cacheResponse and getCachedResponse work correctly`() = runTest {
        // Given
        val cacheKey = "test-key"
        val mockResponse = createMockAIResponse()

        // When
        aiRepository.cacheResponse(cacheKey, mockResponse)
        val cachedResponse = aiRepository.getCachedResponse(cacheKey)

        // Then
        assertNotNull(cachedResponse)
        assertEquals(mockResponse.id, cachedResponse?.id)
        assertEquals(mockResponse.text, cachedResponse?.text)
    }

    @Test
    fun `clearResponseCache removes all cached responses`() = runTest {
        // Given
        val cacheKey = "test-key"
        val mockResponse = createMockAIResponse()
        aiRepository.cacheResponse(cacheKey, mockResponse)

        // When
        aiRepository.clearResponseCache()
        val cachedResponse = aiRepository.getCachedResponse(cacheKey)

        // Then
        assertNull(cachedResponse)
    }

    private fun createMockAIResponse() = com.zara.assistant.domain.model.AIResponse(
        id = UUID.randomUUID().toString(),
        text = "Mock response",
        timestamp = Date(),
        commandId = "command-id",
        responseTime = 1000L,
        source = com.zara.assistant.domain.model.AISource.COHERE,
        confidence = 0.9f
    )
}
