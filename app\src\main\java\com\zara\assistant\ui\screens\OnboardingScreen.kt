package com.zara.assistant.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * Mind-Blowing Onboarding Screen - Welcome to the future of voice assistants
 * Features:
 * - Stunning animations
 * - Interactive tutorials
 * - Beautiful gradients
 * - Smooth transitions
 * - Engaging content
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun OnboardingScreen(
    onComplete: () -> Unit
) {
    val pagerState = rememberPagerState(pageCount = { onboardingPages.size })
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "onboarding_animation")
    
    // Background gradient animation
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "gradient_offset"
    )
    
    // Floating animation
    val floatingOffset by infiniteTransition.animateFloat(
        initialValue = -10f,
        targetValue = 10f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "floating_offset"
    )
    
    // Auto-advance pages
    LaunchedEffect(pagerState.currentPage) {
        if (pagerState.currentPage < onboardingPages.size - 1) {
            delay(5000) // 5 seconds per page
            pagerState.animateScrollToPage(pagerState.currentPage + 1)
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF667EEA),
                        Color(0xFF764BA2),
                        Color(0xFF6B73FF),
                        Color(0xFF9A4993)
                    ),
                    start = androidx.compose.ui.geometry.Offset(
                        gradientOffset * 1000f,
                        gradientOffset * 1000f
                    ),
                    end = androidx.compose.ui.geometry.Offset(
                        (1f - gradientOffset) * 1000f,
                        (1f - gradientOffset) * 1000f
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(60.dp))
            
            // App logo and title
            OnboardingHeader(
                modifier = Modifier.offset(y = floatingOffset.dp)
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Pager content
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.weight(1f)
            ) { page ->
                OnboardingPage(
                    page = onboardingPages[page],
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 24.dp)
                        .offset(y = (-floatingOffset * 0.5f).dp)
                )
            }
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Page indicators
            PageIndicators(
                pageCount = onboardingPages.size,
                currentPage = pagerState.currentPage,
                modifier = Modifier.padding(horizontal = 24.dp)
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Action buttons
            OnboardingActions(
                currentPage = pagerState.currentPage,
                totalPages = onboardingPages.size,
                onNext = {
                    if (pagerState.currentPage < onboardingPages.size - 1) {
                        lifecycleOwner.lifecycleScope.launch {
                            pagerState.animateScrollToPage(pagerState.currentPage + 1)
                        }
                    } else {
                        onComplete()
                    }
                },
                onSkip = onComplete,
                modifier = Modifier.padding(horizontal = 24.dp)
            )
            
            Spacer(modifier = Modifier.height(40.dp))
        }
    }
}

/**
 * Onboarding Header with logo and title
 */
@Composable
private fun OnboardingHeader(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Animated logo
        val scale by rememberInfiniteTransition(label = "logo_animation").animateFloat(
            initialValue = 0.9f,
            targetValue = 1.1f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = FastOutSlowInEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "logo_scale"
        )
        
        NeumorphismCard(
            modifier = Modifier
                .size(80.dp)
                .scale(scale),
            shape = CircleShape,
            elevation = 16.dp
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Mic,
                    contentDescription = "Zara Logo",
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Zara",
            style = MaterialTheme.typography.headlineLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 48.sp,
                color = Color.White
            )
        )
        
        Text(
            text = "Your AI Voice Assistant",
            style = MaterialTheme.typography.titleMedium.copy(
                color = Color.White.copy(alpha = 0.8f)
            )
        )
    }
}

/**
 * Individual onboarding page
 */
@Composable
private fun OnboardingPage(
    page: OnboardingPageData,
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }
    
    val alpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(1000, easing = FastOutSlowInEasing),
        label = "page_alpha"
    )
    
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "page_scale"
    )
    
    LaunchedEffect(Unit) {
        delay(300)
        isVisible = true
    }
    
    NeumorphismCard(
        modifier = modifier
            .alpha(alpha)
            .scale(scale),
        elevation = 20.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Icon with animation
            val iconScale by rememberInfiniteTransition(label = "icon_animation").animateFloat(
                initialValue = 1f,
                targetValue = 1.2f,
                animationSpec = infiniteRepeatable(
                    animation = tween(2000, easing = FastOutSlowInEasing),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "icon_scale"
            )
            
            Icon(
                imageVector = page.icon,
                contentDescription = page.title,
                modifier = Modifier
                    .size(120.dp)
                    .scale(iconScale),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Text(
                text = page.title,
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                ),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = page.description,
                style = MaterialTheme.typography.bodyLarge.copy(
                    lineHeight = 24.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                ),
                textAlign = TextAlign.Center
            )
            
            if (page.features.isNotEmpty()) {
                Spacer(modifier = Modifier.height(24.dp))
                
                page.features.forEach { feature ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        Text(
                            text = feature,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * Page indicators
 */
@Composable
private fun PageIndicators(
    pageCount: Int,
    currentPage: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        repeat(pageCount) { index ->
            val isSelected = index == currentPage
            
            val width by animateDpAsState(
                targetValue = if (isSelected) 32.dp else 8.dp,
                animationSpec = tween(300, easing = FastOutSlowInEasing),
                label = "indicator_width"
            )
            
            Box(
                modifier = Modifier
                    .width(width)
                    .height(8.dp)
                    .background(
                        color = if (isSelected) Color.White else Color.White.copy(alpha = 0.3f),
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
    }
}

/**
 * Action buttons
 */
@Composable
private fun OnboardingActions(
    currentPage: Int,
    totalPages: Int,
    onNext: () -> Unit,
    onSkip: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Skip button
        if (currentPage < totalPages - 1) {
            TextButton(
                onClick = onSkip,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color.White.copy(alpha = 0.7f)
                )
            ) {
                Text(
                    text = "Skip",
                    style = MaterialTheme.typography.labelLarge
                )
            }
        } else {
            Spacer(modifier = Modifier.width(1.dp))
        }
        
        // Next/Get Started button
        NeumorphismButton(
            onClick = onNext,
            modifier = Modifier.height(56.dp),
            shape = RoundedCornerShape(28.dp)
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = if (currentPage < totalPages - 1) "Next" else "Get Started",
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                    imageVector = if (currentPage < totalPages - 1) Icons.Default.ArrowForward else Icons.Default.Rocket,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Onboarding page data
 */
data class OnboardingPageData(
    val icon: ImageVector,
    val title: String,
    val description: String,
    val features: List<String> = emptyList()
)

/**
 * Onboarding pages content
 */
private val onboardingPages = listOf(
    OnboardingPageData(
        icon = Icons.Default.Mic,
        title = "Voice-First Experience",
        description = "Experience the future of hands-free interaction with ultra-fast voice recognition and natural conversations.",
        features = listOf(
            "\"Hey Zara\" wake word detection",
            "Hindi and English support",
            "30-second auto-listening"
        )
    ),
    OnboardingPageData(
        icon = Icons.Default.Psychology,
        title = "Dual AI Intelligence",
        description = "Powered by Cohere and Perplexity AI for the most intelligent and up-to-date responses.",
        features = listOf(
            "Conversational AI with Cohere",
            "Real-time information with Perplexity",
            "Smart command routing"
        )
    ),
    OnboardingPageData(
        icon = Icons.Default.Settings,
        title = "Complete System Control",
        description = "Control your device with voice commands - from WiFi and Bluetooth to apps and notifications.",
        features = listOf(
            "Device settings control",
            "App management",
            "Notification handling",
            "Smart home integration"
        )
    ),
    OnboardingPageData(
        icon = Icons.Default.Security,
        title = "Privacy & Security",
        description = "Your privacy is our priority. All processing is secure with encrypted storage and optional data sharing.",
        features = listOf(
            "Encrypted local storage",
            "Optional conversation history",
            "Secure API communications",
            "Privacy-first design"
        )
    )
)
