package com.zara.assistant.services;

/**
 * Wake Word Service - "Hey Zara" detection using Picovoice Porcupine
 * Features:
 * - Ultra-fast wake word detection
 * - Low power consumption
 * - Adjustable sensitivity
 * - Foreground service for reliability
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\n\b\u0007\u0018\u0000 *2\u00020\u0001:\u0001*B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014J\b\u0010\u0015\u001a\u00020\u0016H\u0002J\u0016\u0010\u0017\u001a\u00020\u00052\u0006\u0010\u000e\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0014\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0016J\b\u0010\u001d\u001a\u00020\u001eH\u0016J\b\u0010\u001f\u001a\u00020\u001eH\u0016J\"\u0010 \u001a\u00020!2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\"\u001a\u00020!2\u0006\u0010#\u001a\u00020!H\u0016J\b\u0010$\u001a\u00020\u001eH\u0002J\b\u0010%\u001a\u00020\u001eH\u0002J\u0010\u0010&\u001a\u00020\u001e2\u0006\u0010\u000e\u001a\u00020\u0007H\u0002J\b\u0010\'\u001a\u00020\u001eH\u0002J\u0010\u0010(\u001a\u00020\u001e2\u0006\u0010)\u001a\u00020\u0007H\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00050\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u000bR\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000bR\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006+"}, d2 = {"Lcom/zara/assistant/services/WakeWordService;", "Landroid/app/Service;", "()V", "_isWakeWordActive", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_sensitivity", "", "isListening", "isWakeWordActive", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "porcupine", "Lai/picovoice/porcupine/Porcupine;", "sensitivity", "getSensitivity", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "copyPPNFileFromAssets", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNotification", "Landroid/app/Notification;", "initializePorcupine", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "", "onDestroy", "onStartCommand", "", "flags", "startId", "onWakeWordDetected", "startListeningLoop", "startWakeWordDetection", "stopWakeWordDetection", "updateSensitivity", "newSensitivity", "Companion", "app_debug"})
public final class WakeWordService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "WakeWordService";
    private static final int NOTIFICATION_ID = 1002;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_WAKE_WORD = "START_WAKE_WORD";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_WAKE_WORD = "STOP_WAKE_WORD";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SET_SENSITIVITY = "SET_SENSITIVITY";
    @org.jetbrains.annotations.Nullable()
    private ai.picovoice.porcupine.Porcupine porcupine;
    private boolean isListening = false;
    @org.jetbrains.annotations.NotNull()
    private kotlinx.coroutines.CoroutineScope serviceScope;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isWakeWordActive = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isWakeWordActive = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Float> _sensitivity = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Float> sensitivity = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.WakeWordService.Companion Companion = null;
    
    public WakeWordService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isWakeWordActive() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Float> getSensitivity() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * Start wake word detection
     */
    private final void startWakeWordDetection(float sensitivity) {
    }
    
    /**
     * Stop wake word detection
     */
    private final void stopWakeWordDetection() {
    }
    
    /**
     * Update sensitivity
     */
    private final void updateSensitivity(float newSensitivity) {
    }
    
    /**
     * Initialize Porcupine wake word engine
     */
    private final java.lang.Object initializePorcupine(float sensitivity, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Copy PPN file from assets to internal storage
     */
    private final java.lang.Object copyPPNFileFromAssets(kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Main listening loop
     */
    private final void startListeningLoop() {
    }
    
    /**
     * Handle wake word detection
     */
    private final void onWakeWordDetected() {
    }
    
    /**
     * Create notification for foreground service
     */
    private final android.app.Notification createNotification() {
        return null;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/zara/assistant/services/WakeWordService$Companion;", "", "()V", "ACTION_SET_SENSITIVITY", "", "ACTION_START_WAKE_WORD", "ACTION_STOP_WAKE_WORD", "NOTIFICATION_ID", "", "TAG", "startWakeWordDetection", "", "context", "Landroid/content/Context;", "sensitivity", "", "stopWakeWordDetection", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Start wake word detection service
         */
        public final void startWakeWordDetection(@org.jetbrains.annotations.NotNull()
        android.content.Context context, float sensitivity) {
        }
        
        /**
         * Stop wake word detection service
         */
        public final void stopWakeWordDetection(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
    }
}