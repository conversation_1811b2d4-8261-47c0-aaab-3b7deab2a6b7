// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.utils.PerformanceUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvidePerformanceUtilsFactory implements Factory<PerformanceUtils> {
  private final Provider<Context> contextProvider;

  public ServiceModule_ProvidePerformanceUtilsFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PerformanceUtils get() {
    return providePerformanceUtils(contextProvider.get());
  }

  public static ServiceModule_ProvidePerformanceUtilsFactory create(
      Provider<Context> contextProvider) {
    return new ServiceModule_ProvidePerformanceUtilsFactory(contextProvider);
  }

  public static PerformanceUtils providePerformanceUtils(Context context) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.providePerformanceUtils(context));
  }
}
