package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * System Control Repository Interface - Defines system control operations
 */
interface SystemControlRepository {
    
    // Device Control
    suspend fun setWifiEnabled(enabled: Boolean): Result<SystemControlResult>
    suspend fun setBluetoothEnabled(enabled: Boolean): Result<SystemControlResult>
    suspend fun setMobileDataEnabled(enabled: Boolean): Result<SystemControlResult>
    suspend fun setFlashlightEnabled(enabled: Boolean): Result<SystemControlResult>
    suspend fun setVolume(level: Int): Result<SystemControlResult>
    suspend fun setBrightness(level: Int): Result<SystemControlResult>
    
    // System Navigation
    suspend fun performGlobalAction(action: Int): Result<SystemControlResult>
    suspend fun takeScreenshot(): Result<SystemControlResult>
    suspend fun lockScreen(): Result<SystemControlResult>
    
    // App Management
    suspend fun openApp(packageName: String): Result<SystemControlResult>
    suspend fun openAppByName(appName: String): Result<SystemControlResult>
    suspend fun closeApp(packageName: String): Result<SystemControlResult>
    suspend fun getInstalledApps(): Result<List<AppInfo>>
    
    // Device State
    suspend fun getDeviceState(): Result<DeviceState>
    fun observeDeviceState(): Flow<DeviceState>
    
    // Notification Management
    suspend fun getActiveNotifications(): Result<List<NotificationData>>
    suspend fun dismissNotification(key: String): Result<SystemControlResult>
    suspend fun dismissAllNotifications(): Result<SystemControlResult>
    suspend fun replyToNotification(key: String, message: String): Result<SystemControlResult>
}

/**
 * Notification Data Model
 */
data class NotificationData(
    val key: String,
    val packageName: String,
    val appName: String,
    val title: String,
    val text: String,
    val timestamp: Long,
    val isHighPriority: Boolean,
    val hasReplyAction: Boolean,
    val category: String?,
    val group: String?
)
