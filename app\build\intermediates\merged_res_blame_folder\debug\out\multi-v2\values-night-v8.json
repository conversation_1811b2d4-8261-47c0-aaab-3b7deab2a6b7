{"logs": [{"outputFile": "com.zara.assistant.app-mergeDebugResources-87:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "72,73,74,75,76,77,78,102", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3584,3654,3738,3822,3918,4020,4122,5326", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3649,3733,3817,3913,4015,4117,4211,5410"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Zara\\Zara\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "126,28,41,120,131,115,109,56,64,69,86,90,95,75,81,3,48,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5422,1336,1971,5182,5611,5020,4798,2607,2961,3134,3835,3973,4184,3370,3628,88,2251,4496", "endLines": "128,38,45,123,134,117,112,61,67,72,88,93,99,79,84,25,53,106", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "5558,1924,2202,5379,5780,5145,4983,2916,3128,3334,3967,4178,4455,3622,3829,1287,2563,4761"}, "to": {"startLines": "3,6,17,22,26,30,33,37,43,47,51,54,58,63,68,79,103,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "141,282,875,1111,1313,1487,1617,1807,2121,2293,2498,2635,2845,3121,3378,4216,5415,5732", "endLines": "5,16,21,25,29,32,36,42,46,50,53,57,62,67,71,101,108,113", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "277,870,1106,1308,1482,1612,1802,2116,2288,2493,2630,2840,3116,3373,3579,5321,5727,5997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1a259f4fef2f815ba319024046901d62\\transformed\\jetified-core-splashscreen-1.0.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "85", "endOffsets": "136"}}]}]}