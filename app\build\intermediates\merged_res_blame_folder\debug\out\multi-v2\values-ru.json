{"logs": [{"outputFile": "com.zara.assistant.app-mergeDebugResources-87:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c1dbd90dee8ec981efff90fba80648e\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "36,37,39,41,42,53,54,111,112,113,114,115,116,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3549,3642,3832,4025,4127,5548,5630,11855,11943,12025,12096,12166,12250,12419,12592,12676,12746", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "3637,3720,3925,4122,4214,5625,5715,11938,12020,12091,12161,12245,12332,12486,12671,12741,12864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10f8b8bca7dd0586b8f58168a92d3396\\transformed\\biometric-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,257,377,508,650,789,921,1064,1163,1304,1461", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "157,252,372,503,645,784,916,1059,1158,1299,1456,1581"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3725,3930,4219,4339,4470,4612,4751,4883,5026,5125,5266,5423", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "3827,4020,4334,4465,4607,4746,4878,5021,5120,5261,5418,5543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f2f29d630982718561535260364d81\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,12337", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,12414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a81feca8f62c4cb5733ffcc676aee47\\transformed\\jetified-material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4621,4708,4816,4896,4980,5078,5179,5273,5368,5456,5563,5661,5760,5907,5987,6093", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4616,4703,4811,4891,4975,5073,5174,5268,5363,5451,5558,5656,5755,5902,5982,6088,6185"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5720,5838,5954,6072,6190,6289,6386,6500,6641,6758,6898,6982,7080,7173,7271,7386,7509,7612,7741,7869,7995,8175,8299,8422,8549,8669,8763,8863,8984,9117,9215,9329,9436,9568,9706,9816,9916,10001,10096,10192,10286,10373,10481,10561,10645,10743,10844,10938,11033,11121,11228,11326,11425,11572,11652,11758", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "5833,5949,6067,6185,6284,6381,6495,6636,6753,6893,6977,7075,7168,7266,7381,7504,7607,7736,7864,7990,8170,8294,8417,8544,8664,8758,8858,8979,9112,9210,9324,9431,9563,9701,9811,9911,9996,10091,10187,10281,10368,10476,10556,10640,10738,10839,10933,11028,11116,11223,11321,11420,11567,11647,11753,11850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3093826e12afe4e65cbc1e4cc9cbb375\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12869,12957", "endColumns": "87,90", "endOffsets": "12952,13043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76f8ef271629eccbc242979ade06d14f\\transformed\\core-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2822,2920,3022,3123,3224,3329,3432,12491", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "2915,3017,3118,3219,3324,3427,3544,12587"}}]}]}