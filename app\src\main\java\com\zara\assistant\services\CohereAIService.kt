package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.*
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PerformanceUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Cohere AI Service - Conversational AI using Cohere API
 * Optimized for natural conversations and personality-based responses
 */
@Singleton
class CohereAIService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: <PERSON><PERSON><PERSON><PERSON>Manager,
    private val performanceUtils: PerformanceUtils
) {
    
    companion object {
        private const val TAG = "CohereAI"
        private const val BASE_URL = "https://api.cohere.ai/v1/"
        private const val GENERATE_ENDPOINT = "generate"
    }
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(Constants.AI.AI_CONNECT_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .readTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .writeTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        .build()

    /**
     * Get conversational response from Cohere
     */
    suspend fun getResponse(
        message: String,
        language: String = Constants.Voice.LANGUAGE_ENGLISH,
        conversationHistory: List<Conversation> = emptyList(),
        personalityMode: String = Constants.AI.PERSONALITY_FRIENDLY
    ): Result<AIResponse> = withContext(Dispatchers.IO) {
        try {
            val measurementId = performanceUtils.startMeasurement("Cohere_GetResponse")
            
            Log.d(TAG, "🤖 Getting Cohere response for: ${message.take(50)}...")
            
            val apiKey = apiKeyManager.getCohereApiKey()
            if (apiKey == null) {
                return@withContext Result.failure(Exception("Cohere API key not available"))
            }
            
            // Build prompt with context and personality
            val prompt = buildPrompt(message, language, conversationHistory, personalityMode)
            
            // Create request
            val request = createCohereRequest(prompt, apiKey)
            
            // Execute request with timeout
            val response = withTimeout(Constants.AI.AI_REQUEST_TIMEOUT_MS) {
                httpClient.newCall(request).execute()
            }
            
            val result = if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    parseCohereResponse(responseBody, measurementId)
                } else {
                    Result.failure(Exception("Empty response from Cohere"))
                }
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                Log.e(TAG, "❌ Cohere API error: ${response.code} - $errorBody")
                Result.failure(Exception("Cohere API error: ${response.code}"))
            }
            
            performanceUtils.endMeasurement(measurementId)
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting Cohere response: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Build prompt with context and personality
     */
    private fun buildPrompt(
        message: String,
        language: String,
        conversationHistory: List<Conversation>,
        personalityMode: String
    ): String {
        val personalityPrompt = when (personalityMode) {
            Constants.AI.PERSONALITY_PROFESSIONAL -> {
                "You are Zara, a professional AI assistant. Provide clear, concise, and helpful responses."
            }
            Constants.AI.PERSONALITY_FRIENDLY -> {
                "You are Zara, a friendly AI assistant. Be warm, helpful, and conversational in your responses."
            }
            Constants.AI.PERSONALITY_CASUAL -> {
                "You are Zara, a casual AI assistant. Be relaxed, friendly, and use a conversational tone."
            }
            else -> {
                "You are Zara, an AI assistant. Be helpful and provide useful responses."
            }
        }
        
        val languageInstruction = when (language) {
            Constants.Voice.LANGUAGE_HINDI -> "Respond in Hindi if the user speaks in Hindi, otherwise respond in English."
            else -> "Respond in English unless specifically asked to use another language."
        }
        
        val contextPrompt = if (conversationHistory.isNotEmpty()) {
            val recentHistory = conversationHistory.takeLast(3)
            val historyText = recentHistory.joinToString("\n") { 
                "User: ${it.userMessage}\nZara: ${it.aiResponse}" 
            }
            "\n\nRecent conversation:\n$historyText\n\n"
        } else {
            "\n\n"
        }
        
        return """$personalityPrompt
        
$languageInstruction

Keep responses concise but helpful. If asked about your capabilities, mention that you can:
- Control device settings (WiFi, Bluetooth, volume, etc.)
- Open and manage apps
- Answer questions and provide information
- Have natural conversations
- Help with various tasks

$contextPrompt

User: $message
Zara:"""
    }

    /**
     * Create Cohere API request
     */
    private fun createCohereRequest(prompt: String, apiKey: String): Request {
        val requestBody = CohereRequest(
            model = Constants.AI.COHERE_MODEL,
            prompt = prompt,
            maxTokens = Constants.AI.MAX_TOKENS,
            temperature = Constants.AI.TEMPERATURE,
            p = Constants.AI.TOP_P,
            stopSequences = listOf("User:", "\n\n")
        )
        
        val jsonBody = json.encodeToString(CohereRequest.serializer(), requestBody)
        
        return Request.Builder()
            .url(BASE_URL + GENERATE_ENDPOINT)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", "application/json")
            .addHeader("User-Agent", "Zara-Assistant/1.0")
            .post(jsonBody.toRequestBody("application/json".toMediaType()))
            .build()
    }

    /**
     * Parse Cohere API response
     */
    private fun parseCohereResponse(responseBody: String, measurementId: String): Result<AIResponse> {
        return try {
            val cohereResponse = json.decodeFromString(CohereResponse.serializer(), responseBody)
            
            val text = cohereResponse.generations.firstOrNull()?.text?.trim()
            
            if (text.isNullOrBlank()) {
                Result.failure(Exception("Empty response from Cohere"))
            } else {
                val processingTime = performanceUtils.endMeasurement(measurementId)
                
                val aiResponse = AIResponse(
                    id = UUID.randomUUID().toString(),
                    text = text,
                    source = AISource.COHERE,
                    confidence = 0.9f, // Cohere generally provides high-quality responses
                    processingTimeMs = processingTime,
                    timestamp = Date(),
                    metadata = mapOf(
                        "model" to Constants.AI.COHERE_MODEL,
                        "tokens" to (cohereResponse.generations.firstOrNull()?.text?.split(" ")?.size ?: 0).toString()
                    )
                )
                
                Log.d(TAG, "✅ Cohere response: ${text.take(100)}... (${processingTime}ms)")
                Result.success(aiResponse)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error parsing Cohere response: ${e.message}", e)
            Result.failure(Exception("Failed to parse Cohere response: ${e.message}"))
        }
    }

    /**
     * Test Cohere API connectivity
     */
    suspend fun testConnection(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🧪 Testing Cohere connection...")
            
            val testResult = getResponse(
                message = "Hello, this is a test message.",
                language = Constants.Voice.LANGUAGE_ENGLISH,
                conversationHistory = emptyList(),
                personalityMode = Constants.AI.PERSONALITY_PROFESSIONAL
            )
            
            val isSuccess = testResult.isSuccess
            Log.d(TAG, "🧪 Cohere connection test: ${if (isSuccess) "SUCCESS" else "FAILED"}")
            
            Result.success(isSuccess)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Cohere connection test failed: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * Check if Cohere service is available
     */
    fun isAvailable(): Boolean {
        return apiKeyManager.getCohereApiKey() != null
    }

    /**
     * Get service status
     */
    fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "available" to isAvailable(),
            "apiKeyConfigured" to (apiKeyManager.getCohereApiKey() != null),
            "model" to Constants.AI.COHERE_MODEL,
            "baseUrl" to BASE_URL
        )
    }
}

/**
 * Cohere API Request Model
 */
@Serializable
data class CohereRequest(
    val model: String,
    val prompt: String,
    val maxTokens: Int,
    val temperature: Float,
    val p: Float,
    val stopSequences: List<String>
)

/**
 * Cohere API Response Model
 */
@Serializable
data class CohereResponse(
    val id: String,
    val generations: List<CohereGeneration>,
    val prompt: String? = null
)

@Serializable
data class CohereGeneration(
    val id: String,
    val text: String,
    val finishReason: String? = null
)
